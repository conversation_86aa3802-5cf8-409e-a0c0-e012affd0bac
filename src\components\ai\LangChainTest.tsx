/**
 * <PERSON><PERSON><PERSON>n Test Component
 * Simple component to test LangChain integration
 */

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Lo<PERSON>, Bot, User, CheckCircle, AlertTriangle } from "lucide-react";
import { useSimpleLangChain } from "@/hooks/useSimpleLangChain";
import { useToast } from "@/hooks/use-toast";

export const LangChainTest = () => {
  const [input, setInput] = useState('');
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  
  const { 
    messages, 
    isLoading, 
    sendMessage, 
    sendHackerMessage, 
    sendFuturisticMessage,
    clearMessages,
    hasEnhancedMessages 
  } = useSimpleLangChain();
  
  const { toast } = useToast();

  const runTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    clearMessages();

    const tests = [
      {
        name: 'Basic Message Test',
        test: () => sendMessage('Hello, can you help me?'),
        description: 'Tests basic LangChain message processing'
      },
      {
        name: 'Hacker Interface Test',
        test: () => sendHackerMessage('system status'),
        description: 'Tests hacker-style terminal responses'
      },
      {
        name: 'Futuristic Interface Test',
        test: () => sendFuturisticMessage('analyze current workflow'),
        description: 'Tests futuristic AI interface responses'
      },
      {
        name: 'Enhanced Interface Test',
        test: () => sendMessage('Generate a detailed report', { interface: 'enhanced' }),
        description: 'Tests enhanced conversational interface'
      }
    ];

    for (const test of tests) {
      try {
        const startTime = Date.now();
        const result = await test.test();
        const endTime = Date.now();
        
        setTestResults(prev => [...prev, {
          name: test.name,
          description: test.description,
          status: 'success',
          response: result?.content || 'No response',
          enhanced: result?.enhanced || false,
          duration: endTime - startTime,
          timestamp: new Date()
        }]);
        
        // Wait a bit between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        setTestResults(prev => [...prev, {
          name: test.name,
          description: test.description,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          duration: 0,
          timestamp: new Date()
        }]);
      }
    }

    setIsRunningTests(false);
    
    toast({
      title: "LangChain Tests Complete",
      description: `Completed ${tests.length} tests. Check results below.`,
    });
  };

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return;
    
    try {
      await sendMessage(input);
      setInput('');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive"
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading) {
      handleSendMessage();
    }
  };

  return (
    <div className="space-y-6">
      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            LangChain Integration Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <Button 
              onClick={runTests} 
              disabled={isRunningTests}
              className="flex items-center gap-2"
            >
              {isRunningTests ? (
                <>
                  <Loader className="h-4 w-4 animate-spin" />
                  Running Tests...
                </>
              ) : (
                'Run LangChain Tests'
              )}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={clearMessages}
              disabled={isLoading}
            >
              Clear Messages
            </Button>
            
            <div className="flex items-center gap-2">
              <Badge variant={hasEnhancedMessages ? "default" : "secondary"}>
                {hasEnhancedMessages ? "LangChain Active" : "Basic AI"}
              </Badge>
            </div>
          </div>

          {/* Manual Test Input */}
          <div className="flex gap-2">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message to test LangChain..."
              disabled={isLoading}
            />
            <Button 
              onClick={handleSendMessage}
              disabled={isLoading || !input.trim()}
            >
              {isLoading ? <Loader className="h-4 w-4 animate-spin" /> : 'Send'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              <div className="space-y-3">
                {testResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {result.status === 'success' ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                        )}
                        <span className="font-medium">{result.name}</span>
                        {result.enhanced && (
                          <Badge variant="default" className="text-xs">Enhanced</Badge>
                        )}
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {result.duration}ms
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {result.description}
                    </p>
                    {result.status === 'success' ? (
                      <p className="text-sm bg-muted p-2 rounded">
                        {result.response}
                      </p>
                    ) : (
                      <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                        Error: {result.error}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Live Chat Test */}
      <Card>
        <CardHeader>
          <CardTitle>Live Chat Test</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64 mb-4">
            <div className="space-y-3">
              {messages.map((message) => (
                <div key={message.id} className="flex gap-3">
                  <div className="flex-shrink-0">
                    {message.role === 'user' ? (
                      <User className="h-6 w-6 text-blue-500" />
                    ) : (
                      <Bot className="h-6 w-6 text-green-500" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium capitalize">{message.role}</span>
                      {message.enhanced && (
                        <Badge variant="default" className="text-xs">Enhanced</Badge>
                      )}
                      <span className="text-xs text-muted-foreground">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="text-sm">{message.content}</p>
                  </div>
                </div>
              ))}
              {isLoading && (
                <div className="flex gap-3">
                  <Bot className="h-6 w-6 text-green-500" />
                  <div className="flex items-center gap-2">
                    <Loader className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Thinking...</span>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};
