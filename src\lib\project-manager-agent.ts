/**
 * Project Manager Assistant Agent for CTNL AI Workboard
 * Based on LangGraph workflow with structured output and self-reflection
 */

import { supabase } from '@/integrations/supabase/client';

// Data Models
export interface Task {
  id: string;
  task_name: string;
  task_description: string;
  estimated_days: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'not_started' | 'in_progress' | 'completed' | 'blocked';
}

export interface TaskDependency {
  task_id: string;
  dependent_task_ids: string[];
  dependency_type: 'finish_to_start' | 'start_to_start' | 'finish_to_finish';
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  skills: string[];
  availability: number; // 0-100 percentage
  current_workload: number; // hours per week
}

export interface TaskAllocation {
  task_id: string;
  team_member_id: string;
  allocated_hours: number;
  start_date: Date;
  end_date: Date;
}

export interface TaskSchedule {
  task_id: string;
  start_date: Date;
  end_date: Date;
  duration_days: number;
}

export interface Risk {
  task_id: string;
  risk_score: number; // 0-10
  risk_factors: string[];
  mitigation_strategies: string[];
}

export interface ProjectPlan {
  tasks: Task[];
  dependencies: TaskDependency[];
  schedule: TaskSchedule[];
  allocations: TaskAllocation[];
  risks: Risk[];
  total_duration_days: number;
  overall_risk_score: number;
}

// Agent State
export interface AgentState {
  project_description: string;
  team_members: TeamMember[];
  tasks: Task[];
  dependencies: TaskDependency[];
  schedule: TaskSchedule[];
  allocations: TaskAllocation[];
  risks: Risk[];
  iteration_number: number;
  max_iterations: number;
  insights: string[];
  project_plans: ProjectPlan[];
  overall_risk_scores: number[];
}

export class ProjectManagerAgent {
  private openaiApiKey: string;
  private useLangChain: boolean;

  constructor() {
    this.openaiApiKey = import.meta.env.VITE_OPENAI_API_KEY || '';
    this.useLangChain = import.meta.env.USE_LANGCHAIN === 'true';
  }

  /**
   * Generate tasks from project description
   */
  async generateTasks(projectDescription: string): Promise<Task[]> {
    const prompt = `
      You are an expert project manager. Analyze this project description and create actionable tasks:
      
      Project: ${projectDescription}
      
      Requirements:
      1. Extract all actionable and realistic tasks
      2. Provide estimated days for each task (max 5 days, break down larger tasks)
      3. Assign priority levels (low, medium, high, critical)
      4. Ensure tasks are clearly defined and achievable
      
      Return a JSON array of tasks with: id, task_name, task_description, estimated_days, priority, status
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseTasksResponse(response);
    } catch (error) {
      console.error('Error generating tasks:', error);
      return this.generateFallbackTasks(projectDescription);
    }
  }

  /**
   * Analyze task dependencies
   */
  async analyzeDependencies(tasks: Task[]): Promise<TaskDependency[]> {
    const prompt = `
      Analyze these tasks and identify dependencies between them:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      
      For each task, determine:
      1. Which tasks must be completed before it can start
      2. The type of dependency (finish_to_start, start_to_start, finish_to_finish)
      
      Return a JSON array of dependencies with: task_id, dependent_task_ids, dependency_type
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseDependenciesResponse(response);
    } catch (error) {
      console.error('Error analyzing dependencies:', error);
      return [];
    }
  }

  /**
   * Create project schedule
   */
  async createSchedule(
    tasks: Task[], 
    dependencies: TaskDependency[], 
    insights: string[] = []
  ): Promise<TaskSchedule[]> {
    const prompt = `
      Create an optimized project schedule for these tasks:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      Dependencies: ${JSON.stringify(dependencies, null, 2)}
      Previous Insights: ${insights.join('; ')}
      
      Requirements:
      1. Respect all task dependencies
      2. Minimize overall project duration
      3. Parallelize tasks where possible
      4. Consider task priorities
      
      Return a JSON array of schedules with: task_id, start_date, end_date, duration_days
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseScheduleResponse(response);
    } catch (error) {
      console.error('Error creating schedule:', error);
      return this.generateFallbackSchedule(tasks);
    }
  }

  /**
   * Allocate tasks to team members
   */
  async allocateTasks(
    tasks: Task[],
    schedule: TaskSchedule[],
    teamMembers: TeamMember[],
    insights: string[] = []
  ): Promise<TaskAllocation[]> {
    const prompt = `
      Allocate tasks to team members based on skills and availability:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      Schedule: ${JSON.stringify(schedule, null, 2)}
      Team Members: ${JSON.stringify(teamMembers, null, 2)}
      Previous Insights: ${insights.join('; ')}
      
      Requirements:
      1. Match tasks to team member skills
      2. Respect availability and current workload
      3. No overlapping task assignments
      4. Balance workload across team
      
      Return a JSON array of allocations with: task_id, team_member_id, allocated_hours, start_date, end_date
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseAllocationResponse(response);
    } catch (error) {
      console.error('Error allocating tasks:', error);
      return [];
    }
  }

  /**
   * Assess project risks
   */
  async assessRisks(
    tasks: Task[],
    schedule: TaskSchedule[],
    allocations: TaskAllocation[]
  ): Promise<Risk[]> {
    const prompt = `
      Assess risks for this project plan:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      Schedule: ${JSON.stringify(schedule, null, 2)}
      Allocations: ${JSON.stringify(allocations, null, 2)}
      
      For each task, analyze:
      1. Complexity and technical risks
      2. Resource availability risks
      3. Dependency and scheduling risks
      4. Assign risk score (0-10)
      5. Suggest mitigation strategies
      
      Return a JSON array of risks with: task_id, risk_score, risk_factors, mitigation_strategies
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseRiskResponse(response);
    } catch (error) {
      console.error('Error assessing risks:', error);
      return [];
    }
  }

  /**
   * Generate insights for improvement
   */
  async generateInsights(
    tasks: Task[],
    schedule: TaskSchedule[],
    allocations: TaskAllocation[],
    risks: Risk[]
  ): Promise<string> {
    const prompt = `
      Analyze this project plan and generate actionable insights:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      Schedule: ${JSON.stringify(schedule, null, 2)}
      Allocations: ${JSON.stringify(allocations, null, 2)}
      Risks: ${JSON.stringify(risks, null, 2)}
      
      Provide insights on:
      1. Potential bottlenecks and resource conflicts
      2. High-risk tasks that need attention
      3. Optimization opportunities
      4. Recommendations to reduce overall project risk
      
      Return clear, actionable recommendations.
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return response;
    } catch (error) {
      console.error('Error generating insights:', error);
      return 'Unable to generate insights at this time.';
    }
  }

  /**
   * Main workflow to create complete project plan
   */
  async createProjectPlan(
    projectDescription: string,
    teamMembers: TeamMember[],
    maxIterations: number = 3
  ): Promise<ProjectPlan> {
    const state: AgentState = {
      project_description: projectDescription,
      team_members: teamMembers,
      tasks: [],
      dependencies: [],
      schedule: [],
      allocations: [],
      risks: [],
      iteration_number: 0,
      max_iterations: maxIterations,
      insights: [],
      project_plans: [],
      overall_risk_scores: []
    };

    // Step 1: Generate tasks
    console.log('🎯 Generating tasks...');
    state.tasks = await this.generateTasks(projectDescription);

    // Step 2: Analyze dependencies
    console.log('🔗 Analyzing dependencies...');
    state.dependencies = await this.analyzeDependencies(state.tasks);

    // Iterative improvement loop
    for (let i = 0; i < maxIterations; i++) {
      state.iteration_number = i + 1;
      console.log(`🔄 Iteration ${state.iteration_number}/${maxIterations}`);

      // Step 3: Create schedule
      console.log('📅 Creating schedule...');
      state.schedule = await this.createSchedule(state.tasks, state.dependencies, state.insights);

      // Step 4: Allocate tasks
      console.log('👥 Allocating tasks...');
      state.allocations = await this.allocateTasks(
        state.tasks, 
        state.schedule, 
        state.team_members, 
        state.insights
      );

      // Step 5: Assess risks
      console.log('⚠️ Assessing risks...');
      state.risks = await this.assessRisks(state.tasks, state.schedule, state.allocations);

      // Calculate overall risk score
      const overallRiskScore = state.risks.reduce((sum, risk) => sum + risk.risk_score, 0);
      state.overall_risk_scores.push(overallRiskScore);

      // Create project plan for this iteration
      const projectPlan: ProjectPlan = {
        tasks: state.tasks,
        dependencies: state.dependencies,
        schedule: state.schedule,
        allocations: state.allocations,
        risks: state.risks,
        total_duration_days: this.calculateProjectDuration(state.schedule),
        overall_risk_score: overallRiskScore
      };
      state.project_plans.push(projectPlan);

      // Check if we should continue iterating
      if (i > 0 && state.overall_risk_scores[i] < state.overall_risk_scores[0]) {
        console.log('✅ Risk reduced, stopping iterations');
        break;
      }

      // Generate insights for next iteration
      if (i < maxIterations - 1) {
        console.log('💡 Generating insights...');
        const insight = await this.generateInsights(
          state.tasks, 
          state.schedule, 
          state.allocations, 
          state.risks
        );
        state.insights.push(insight);
      }
    }

    // Return the best project plan (lowest risk score)
    const bestPlan = state.project_plans.reduce((best, current) => 
      current.overall_risk_score < best.overall_risk_score ? current : best
    );

    console.log('🎉 Project plan created successfully!');
    return bestPlan;
  }

  /**
   * Save project plan to database
   */
  async saveProjectPlan(projectPlan: ProjectPlan, projectId: string): Promise<boolean> {
    try {
      // Save to projects table
      const { error: projectError } = await supabase
        .from('projects')
        .update({
          tasks: projectPlan.tasks,
          schedule: projectPlan.schedule,
          allocations: projectPlan.allocations,
          risks: projectPlan.risks,
          total_duration_days: projectPlan.total_duration_days,
          overall_risk_score: projectPlan.overall_risk_score,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId);

      if (projectError) throw projectError;

      // Log the AI interaction
      await supabase.from('ai_interactions').insert({
        user_id: (await supabase.auth.getUser()).data.user?.id,
        interaction_type: 'project_planning',
        input_data: { project_id: projectId },
        output_data: projectPlan,
        metadata: {
          agent_type: 'project_manager',
          iterations: projectPlan.risks.length,
          risk_score: projectPlan.overall_risk_score
        }
      });

      return true;
    } catch (error) {
      console.error('Error saving project plan:', error);
      return false;
    }
  }

  // Helper methods
  private async callOpenAI(prompt: string): Promise<string> {
    if (!this.useLangChain || !this.openaiApiKey) {
      throw new Error('OpenAI API not configured');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  private parseTasksResponse(response: string): Task[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return this.generateFallbackTasks('');
    }
  }

  private parseDependenciesResponse(response: string): TaskDependency[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  }

  private parseScheduleResponse(response: string): TaskSchedule[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed.map(item => ({
        ...item,
        start_date: new Date(item.start_date),
        end_date: new Date(item.end_date)
      })) : [];
    } catch {
      return [];
    }
  }

  private parseAllocationResponse(response: string): TaskAllocation[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed.map(item => ({
        ...item,
        start_date: new Date(item.start_date),
        end_date: new Date(item.end_date)
      })) : [];
    } catch {
      return [];
    }
  }

  private parseRiskResponse(response: string): Risk[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  }

  private generateFallbackTasks(description: string): Task[] {
    return [
      {
        id: '1',
        task_name: 'Project Planning',
        task_description: 'Initial project planning and requirements gathering',
        estimated_days: 3,
        priority: 'high',
        status: 'not_started'
      },
      {
        id: '2',
        task_name: 'Implementation',
        task_description: 'Core implementation work',
        estimated_days: 5,
        priority: 'high',
        status: 'not_started'
      },
      {
        id: '3',
        task_name: 'Testing',
        task_description: 'Testing and quality assurance',
        estimated_days: 2,
        priority: 'medium',
        status: 'not_started'
      }
    ];
  }

  private generateFallbackSchedule(tasks: Task[]): TaskSchedule[] {
    const startDate = new Date();
    return tasks.map((task, index) => ({
      task_id: task.id,
      start_date: new Date(startDate.getTime() + (index * task.estimated_days * 24 * 60 * 60 * 1000)),
      end_date: new Date(startDate.getTime() + ((index + 1) * task.estimated_days * 24 * 60 * 60 * 1000)),
      duration_days: task.estimated_days
    }));
  }

  private calculateProjectDuration(schedule: TaskSchedule[]): number {
    if (schedule.length === 0) return 0;
    
    const startDate = Math.min(...schedule.map(s => s.start_date.getTime()));
    const endDate = Math.max(...schedule.map(s => s.end_date.getTime()));
    
    return Math.ceil((endDate - startDate) / (24 * 60 * 60 * 1000));
  }
}
