import { supabase } from "@/integrations/supabase/client";

export interface ProjectFormData {
  name: string;
  description?: string;
  client_name?: string;
  budget?: number;
  budget_spent?: number;
  location?: string;
  start_date?: string;
  end_date?: string;
  actual_end_date?: string;
  status?: 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  manager_id?: string;
  department_id?: string;
  progress_percentage?: number;
  completion_percentage?: number;
  actual_hours?: number;
  estimated_hours?: number;
  health_score?: number;
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  category?: string;
  tags?: string[];
  template_id?: string;
  metadata?: any;
}

export interface ProjectAssignment {
  id: string;
  project_id: string;
  staff_id: string;
  role?: string;
  assigned_at: string;
}

export class ProjectAPI {
  /**
   * Get all projects with optional filters
   */
  static async getAllProjects(filters?: {
    status?: string;
    managerId?: string;
    departmentId?: string;
    limit?: number;
  }): Promise<{ data: any[]; error: any }> {
    try {
      // Try the new schema first (with foreign key relationships)
      let query = supabase
        .from('projects')
        .select(`
          *,
          manager:profiles!projects_manager_id_fkey(id, full_name, email, role),
          department:departments!projects_department_id_fkey(id, name),
          project_assignments(
            id,
            assigned_to,
            role,
            status,
            start_date,
            end_date,
            profiles!assigned_to(id, full_name, email, role)
          )
        `);

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      if (filters?.managerId) {
        query = query.eq('manager_id', filters.managerId);
      }
      if (filters?.departmentId) {
        query = query.eq('department_id', filters.departmentId);
      }
      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      query = query.order('created_at', { ascending: false });

      let { data, error } = await query;

      // If the new schema fails (400, 406, or PGRST116 errors), fall back to old schema
      if (error && (error.code === 'PGRST116' || error.status === 400 || error.status === 406)) {
        console.log('Falling back to old schema for projects, error:', error);
        let fallbackQuery = supabase
          .from('projects')
          .select(`
            *,
            manager:profiles!manager_id(id, full_name, email, role),
            department:departments!department_id(id, name)
          `);

        // Apply the same filters for fallback
        if (filters?.status) {
          fallbackQuery = fallbackQuery.eq('status', filters.status);
        }
        if (filters?.managerId) {
          fallbackQuery = fallbackQuery.eq('manager_id', filters.managerId);
        }
        if (filters?.departmentId) {
          fallbackQuery = fallbackQuery.eq('department_id', filters.departmentId);
        }
        if (filters?.limit) {
          fallbackQuery = fallbackQuery.limit(filters.limit);
        }

        fallbackQuery = fallbackQuery.order('created_at', { ascending: false });
        const result = await fallbackQuery;
        data = result.data;
        error = result.error;
      }

      // If both schemas fail, try a basic query without relationships
      if (error) {
        console.log('Both schemas failed, trying basic query without relationships');
        let basicQuery = supabase
          .from('projects')
          .select('*');

        // Apply the same filters for basic query
        if (filters?.status) {
          basicQuery = basicQuery.eq('status', filters.status);
        }
        if (filters?.managerId) {
          basicQuery = basicQuery.eq('manager_id', filters.managerId);
        }
        if (filters?.departmentId) {
          basicQuery = basicQuery.eq('department_id', filters.departmentId);
        }
        if (filters?.limit) {
          basicQuery = basicQuery.limit(filters.limit);
        }

        basicQuery = basicQuery.order('created_at', { ascending: false });
        const result = await basicQuery;
        data = result.data;
        error = result.error;
      }

      return { data: data || [], error };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }

  /**
   * Get project by ID with full details
   */
  static async getProjectById(projectId: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          manager:profiles!manager_id(id, full_name, email, role),
          department:departments!department_id(id, name),
          tasks(
            id,
            title,
            description,
            status,
            priority,
            assigned_to_id,
            due_date,
            assigned_to:profiles!assigned_to_id(id, full_name, email)
          )
        `)
        .eq('id', projectId)
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Create a new project
   */
  static async createProject(projectData: ProjectFormData, userId: string): Promise<{ data: any; error: any }> {
    try {
      // Validate required fields
      if (!projectData.name?.trim()) {
        throw new Error("Project name is required");
      }

      // First, let's try without created_by to avoid schema cache issues
      const insertData = {
        name: projectData.name.trim(),
        description: projectData.description?.trim() || null,
        client_name: projectData.client_name?.trim() || null,
        budget: projectData.budget || null,
        location: projectData.location?.trim() || null,
        start_date: projectData.start_date || null,
        end_date: projectData.end_date || null,
        status: projectData.status || 'planning',
        manager_id: projectData.manager_id || userId,
        department_id: projectData.department_id || null,
      };

      const { data, error } = await supabase
        .from('projects')
        .insert([insertData])
        .select(`
          *,
          manager:profiles!manager_id(id, full_name, email),
          department:departments!department_id(id, name)
        `)
        .single();

      // If insert was successful, update the created_by field
      if (data && !error) {
        await supabase
          .from('projects')
          .update({ created_by: userId })
          .eq('id', data.id);
      }

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Update project (PUT operation)
   */
  static async updateProject(projectId: string, projectData: Partial<ProjectFormData>): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .update({
          ...projectData,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId)
        .select(`
          *,
          manager:profiles!manager_id(id, full_name, email),
          department:departments!department_id(id, name)
        `)
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Patch project (PATCH operation for partial updates)
   */
  static async patchProject(projectId: string, patchData: Partial<ProjectFormData>): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .update({
          ...patchData,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Update project status
   */
  static async updateProjectStatus(projectId: string, status: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Delete project
   */
  static async deleteProject(projectId: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Assign staff member to project
   */
  static async assignStaffToProject(projectId: string, staffId: string): Promise<{ data: any; error: any }> {
    try {
      // First get the project details to get the project name
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('name, department_id')
        .eq('id', projectId)
        .single();

      if (projectError || !project) {
        return { data: null, error: { message: 'Project not found' } };
      }

      const { data, error } = await supabase
        .from('project_assignments')
        .insert([{
          project_name: project.name,
          assigned_to: staffId,
          department_id: project.department_id,
          start_date: new Date().toISOString().split('T')[0], // Today's date
          status: 'active'
        }])
        .select(`
          *,
          profiles:profiles!assigned_to(id, full_name, email, role)
        `)
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Remove staff member from project
   */
  static async removeStaffFromProject(assignmentId: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('project_assignments')
        .delete()
        .eq('id', assignmentId)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Get project statistics
   */
  static async getProjectStats(): Promise<{
    total: number;
    active: number;
    completed: number;
    planning: number;
    onHold: number;
  }> {
    try {
      const { data: projects } = await this.getAllProjects();
      
      const stats = {
        total: projects.length,
        active: projects.filter(p => p.status === 'active').length,
        completed: projects.filter(p => p.status === 'completed').length,
        planning: projects.filter(p => p.status === 'planning').length,
        onHold: projects.filter(p => p.status === 'on_hold').length,
      };

      return stats;
    } catch (error) {
      console.error('Error getting project stats:', error);
      return {
        total: 0,
        active: 0,
        completed: 0,
        planning: 0,
        onHold: 0,
      };
    }
  }

  /**
   * Get all departments for project assignment
   */
  static async getDepartments(): Promise<{ data: any[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('id, name, description')
        .order('name');

      return { data: data || [], error };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }

  /**
   * Get all profiles for project manager assignment
   */
  static async getManagers(): Promise<{ data: any[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .in('role', ['manager', 'admin', 'staff-admin'])
        .order('full_name');

      return { data: data || [], error };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }
}
