/**
 * LangChain Monitoring and Analytics
 * Comprehensive monitoring, logging, and analytics for LangChain operations
 */

import { supabase } from '../supabase';

export interface OperationMetrics {
  operationType: 'chat' | 'rag' | 'agent' | 'document_processing' | 'summarization';
  startTime: number;
  endTime: number;
  duration: number;
  success: boolean;
  error?: string;
  metadata: {
    userId?: string;
    sessionId?: string;
    model?: string;
    tokensUsed?: number;
    inputLength?: number;
    outputLength?: number;
    interface?: string;
    [key: string]: any;
  };
}

export interface UsageStats {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  averageResponseTime: number;
  totalTokensUsed: number;
  operationsByType: Record<string, number>;
  operationsByUser: Record<string, number>;
  operationsByInterface: Record<string, number>;
  errorsByType: Record<string, number>;
}

export interface PerformanceMetrics {
  responseTime: {
    p50: number;
    p95: number;
    p99: number;
    average: number;
  };
  throughput: {
    operationsPerMinute: number;
    operationsPerHour: number;
  };
  errorRate: number;
  availability: number;
}

/**
 * LangChain Monitoring Service
 */
export class LangChainMonitoringService {
  private static instance: LangChainMonitoringService;
  private operationBuffer: OperationMetrics[] = [];
  private bufferSize = 100;
  private flushInterval = 30000; // 30 seconds

  private constructor() {
    this.startPeriodicFlush();
  }

  public static getInstance(): LangChainMonitoringService {
    if (!LangChainMonitoringService.instance) {
      LangChainMonitoringService.instance = new LangChainMonitoringService();
    }
    return LangChainMonitoringService.instance;
  }

  /**
   * Start monitoring an operation
   */
  public startOperation(
    operationType: OperationMetrics['operationType'],
    metadata: OperationMetrics['metadata'] = {}
  ): string {
    const operationId = `op_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    
    // Store start time in metadata for later retrieval
    metadata._operationId = operationId;
    metadata._startTime = Date.now();
    
    return operationId;
  }

  /**
   * End monitoring an operation
   */
  public endOperation(
    operationId: string,
    operationType: OperationMetrics['operationType'],
    success: boolean,
    metadata: OperationMetrics['metadata'] = {},
    error?: string
  ): void {
    const endTime = Date.now();
    const startTime = metadata._startTime || endTime;
    
    const metrics: OperationMetrics = {
      operationType,
      startTime,
      endTime,
      duration: endTime - startTime,
      success,
      error,
      metadata: {
        ...metadata,
        operationId,
      },
    };

    this.recordOperation(metrics);
  }

  /**
   * Record operation metrics
   */
  public recordOperation(metrics: OperationMetrics): void {
    this.operationBuffer.push(metrics);
    
    // Flush if buffer is full
    if (this.operationBuffer.length >= this.bufferSize) {
      this.flushMetrics();
    }
  }

  /**
   * Get usage statistics
   */
  public async getUsageStats(
    startDate?: Date,
    endDate?: Date,
    userId?: string
  ): Promise<UsageStats> {
    try {
      let query = supabase
        .from('langchain_operations')
        .select('*');

      if (startDate) {
        query = query.gte('created_at', startDate.toISOString());
      }
      
      if (endDate) {
        query = query.lte('created_at', endDate.toISOString());
      }
      
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data: operations, error } = await query;
      
      if (error) throw error;

      return this.calculateUsageStats(operations || []);
    } catch (error) {
      console.error('Failed to get usage stats:', error);
      return this.getEmptyUsageStats();
    }
  }

  /**
   * Get performance metrics
   */
  public async getPerformanceMetrics(
    startDate?: Date,
    endDate?: Date
  ): Promise<PerformanceMetrics> {
    try {
      let query = supabase
        .from('langchain_operations')
        .select('duration, success, created_at');

      if (startDate) {
        query = query.gte('created_at', startDate.toISOString());
      }
      
      if (endDate) {
        query = query.lte('created_at', endDate.toISOString());
      }

      const { data: operations, error } = await query;
      
      if (error) throw error;

      return this.calculatePerformanceMetrics(operations || []);
    } catch (error) {
      console.error('Failed to get performance metrics:', error);
      return this.getEmptyPerformanceMetrics();
    }
  }

  /**
   * Get error analytics
   */
  public async getErrorAnalytics(
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalErrors: number;
    errorsByType: Record<string, number>;
    errorsByOperation: Record<string, number>;
    recentErrors: Array<{
      timestamp: Date;
      operationType: string;
      error: string;
      userId?: string;
    }>;
  }> {
    try {
      let query = supabase
        .from('langchain_operations')
        .select('*')
        .eq('success', false);

      if (startDate) {
        query = query.gte('created_at', startDate.toISOString());
      }
      
      if (endDate) {
        query = query.lte('created_at', endDate.toISOString());
      }

      const { data: errors, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;

      return this.analyzeErrors(errors || []);
    } catch (error) {
      console.error('Failed to get error analytics:', error);
      return {
        totalErrors: 0,
        errorsByType: {},
        errorsByOperation: {},
        recentErrors: [],
      };
    }
  }

  /**
   * Get real-time metrics
   */
  public async getRealTimeMetrics(): Promise<{
    activeOperations: number;
    operationsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
    systemHealth: 'healthy' | 'warning' | 'critical';
  }> {
    try {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60000);
      const fiveMinutesAgo = new Date(now.getTime() - 300000);

      // Get operations from last minute
      const { data: recentOps, error: recentError } = await supabase
        .from('langchain_operations')
        .select('duration, success')
        .gte('created_at', oneMinuteAgo.toISOString());

      if (recentError) throw recentError;

      // Get operations from last 5 minutes for error rate
      const { data: fiveMinOps, error: fiveMinError } = await supabase
        .from('langchain_operations')
        .select('success')
        .gte('created_at', fiveMinutesAgo.toISOString());

      if (fiveMinError) throw fiveMinError;

      const operationsPerMinute = recentOps?.length || 0;
      const averageResponseTime = recentOps?.length 
        ? recentOps.reduce((sum, op) => sum + op.duration, 0) / recentOps.length
        : 0;
      
      const totalFiveMin = fiveMinOps?.length || 0;
      const errorsFiveMin = fiveMinOps?.filter(op => !op.success).length || 0;
      const errorRate = totalFiveMin > 0 ? errorsFiveMin / totalFiveMin : 0;

      // Determine system health
      let systemHealth: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (errorRate > 0.1 || averageResponseTime > 10000) {
        systemHealth = 'warning';
      }
      if (errorRate > 0.25 || averageResponseTime > 30000) {
        systemHealth = 'critical';
      }

      return {
        activeOperations: this.operationBuffer.length,
        operationsPerMinute,
        averageResponseTime,
        errorRate,
        systemHealth,
      };
    } catch (error) {
      console.error('Failed to get real-time metrics:', error);
      return {
        activeOperations: 0,
        operationsPerMinute: 0,
        averageResponseTime: 0,
        errorRate: 1,
        systemHealth: 'critical',
      };
    }
  }

  /**
   * Flush metrics to database
   */
  private async flushMetrics(): Promise<void> {
    if (this.operationBuffer.length === 0) return;

    try {
      const metricsToFlush = [...this.operationBuffer];
      this.operationBuffer = [];

      const records = metricsToFlush.map(metrics => ({
        operation_type: metrics.operationType,
        duration: metrics.duration,
        success: metrics.success,
        error_message: metrics.error,
        user_id: metrics.metadata.userId,
        session_id: metrics.metadata.sessionId,
        model: metrics.metadata.model,
        tokens_used: metrics.metadata.tokensUsed,
        input_length: metrics.metadata.inputLength,
        output_length: metrics.metadata.outputLength,
        interface_type: metrics.metadata.interface,
        metadata: metrics.metadata,
        created_at: new Date(metrics.startTime).toISOString(),
      }));

      const { error } = await supabase
        .from('langchain_operations')
        .insert(records);

      if (error) {
        console.error('Failed to flush metrics:', error);
        // Put metrics back in buffer for retry
        this.operationBuffer.unshift(...metricsToFlush);
      }
    } catch (error) {
      console.error('Error flushing metrics:', error);
    }
  }

  /**
   * Start periodic flush
   */
  private startPeriodicFlush(): void {
    setInterval(() => {
      this.flushMetrics();
    }, this.flushInterval);
  }

  /**
   * Calculate usage statistics
   */
  private calculateUsageStats(operations: any[]): UsageStats {
    const stats: UsageStats = {
      totalOperations: operations.length,
      successfulOperations: operations.filter(op => op.success).length,
      failedOperations: operations.filter(op => !op.success).length,
      averageResponseTime: 0,
      totalTokensUsed: 0,
      operationsByType: {},
      operationsByUser: {},
      operationsByInterface: {},
      errorsByType: {},
    };

    if (operations.length > 0) {
      stats.averageResponseTime = operations.reduce((sum, op) => sum + (op.duration || 0), 0) / operations.length;
      stats.totalTokensUsed = operations.reduce((sum, op) => sum + (op.tokens_used || 0), 0);

      // Group by type
      operations.forEach(op => {
        stats.operationsByType[op.operation_type] = (stats.operationsByType[op.operation_type] || 0) + 1;
        
        if (op.user_id) {
          stats.operationsByUser[op.user_id] = (stats.operationsByUser[op.user_id] || 0) + 1;
        }
        
        if (op.interface_type) {
          stats.operationsByInterface[op.interface_type] = (stats.operationsByInterface[op.interface_type] || 0) + 1;
        }
        
        if (!op.success && op.error_message) {
          const errorType = this.categorizeError(op.error_message);
          stats.errorsByType[errorType] = (stats.errorsByType[errorType] || 0) + 1;
        }
      });
    }

    return stats;
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(operations: any[]): PerformanceMetrics {
    if (operations.length === 0) {
      return this.getEmptyPerformanceMetrics();
    }

    const durations = operations.map(op => op.duration || 0).sort((a, b) => a - b);
    const successCount = operations.filter(op => op.success).length;
    
    // Calculate percentiles
    const p50Index = Math.floor(durations.length * 0.5);
    const p95Index = Math.floor(durations.length * 0.95);
    const p99Index = Math.floor(durations.length * 0.99);

    const now = Date.now();
    const oneHourAgo = now - 3600000;
    const oneMinuteAgo = now - 60000;
    
    const recentOps = operations.filter(op => new Date(op.created_at).getTime() > oneHourAgo);
    const veryRecentOps = operations.filter(op => new Date(op.created_at).getTime() > oneMinuteAgo);

    return {
      responseTime: {
        p50: durations[p50Index] || 0,
        p95: durations[p95Index] || 0,
        p99: durations[p99Index] || 0,
        average: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      },
      throughput: {
        operationsPerMinute: veryRecentOps.length,
        operationsPerHour: recentOps.length,
      },
      errorRate: operations.length > 0 ? (operations.length - successCount) / operations.length : 0,
      availability: operations.length > 0 ? successCount / operations.length : 1,
    };
  }

  /**
   * Analyze errors
   */
  private analyzeErrors(errors: any[]): any {
    const errorsByType: Record<string, number> = {};
    const errorsByOperation: Record<string, number> = {};
    
    errors.forEach(error => {
      const errorType = this.categorizeError(error.error_message);
      errorsByType[errorType] = (errorsByType[errorType] || 0) + 1;
      errorsByOperation[error.operation_type] = (errorsByOperation[error.operation_type] || 0) + 1;
    });

    return {
      totalErrors: errors.length,
      errorsByType,
      errorsByOperation,
      recentErrors: errors.slice(0, 10).map(error => ({
        timestamp: new Date(error.created_at),
        operationType: error.operation_type,
        error: error.error_message,
        userId: error.user_id,
      })),
    };
  }

  /**
   * Categorize error by type
   */
  private categorizeError(errorMessage: string): string {
    if (!errorMessage) return 'unknown';
    
    const message = errorMessage.toLowerCase();
    
    if (message.includes('timeout') || message.includes('time out')) return 'timeout';
    if (message.includes('rate limit') || message.includes('quota')) return 'rate_limit';
    if (message.includes('authentication') || message.includes('unauthorized')) return 'auth';
    if (message.includes('network') || message.includes('connection')) return 'network';
    if (message.includes('validation') || message.includes('invalid')) return 'validation';
    if (message.includes('not found') || message.includes('404')) return 'not_found';
    
    return 'other';
  }

  /**
   * Get empty usage stats
   */
  private getEmptyUsageStats(): UsageStats {
    return {
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      averageResponseTime: 0,
      totalTokensUsed: 0,
      operationsByType: {},
      operationsByUser: {},
      operationsByInterface: {},
      errorsByType: {},
    };
  }

  /**
   * Get empty performance metrics
   */
  private getEmptyPerformanceMetrics(): PerformanceMetrics {
    return {
      responseTime: {
        p50: 0,
        p95: 0,
        p99: 0,
        average: 0,
      },
      throughput: {
        operationsPerMinute: 0,
        operationsPerHour: 0,
      },
      errorRate: 0,
      availability: 1,
    };
  }
}

// Export singleton instance
export const langChainMonitoring = LangChainMonitoringService.getInstance();
