import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  Users, 
  Clock, 
  DollarSign, 
  TrendingUp, 
  Activity,
  Zap,
  Sparkles,
  Palette,
  Eye,
  <PERSON>Pointer,
  Smartphone
} from 'lucide-react';

export const EnhancedCardsShowcase = () => {
  const statsData = [
    { title: "Total Users", value: "2,847", change: "+12%", icon: Users, color: "text-blue-400" },
    { title: "Revenue", value: "$45,231", change: "****%", icon: DollarSign, color: "text-green-400" },
    { title: "Active Projects", value: "156", change: "+23%", icon: BarChart3, color: "text-purple-400" },
    { title: "Time Logged", value: "1,247h", change: "****%", icon: Clock, color: "text-orange-400" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="h-12 w-12 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center shadow-lg">
              <Palette className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent">
              Enhanced Card System
            </h1>
          </div>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Beautiful neumorphism cards with CTNL AI colors, enhanced shadows, and interactive animations
          </p>
          <div className="flex justify-center gap-2 mt-4">
            <Badge variant="secondary" className="bg-red-500/20 text-red-300 border-red-500/30">
              <Sparkles className="h-3 w-3 mr-1" />
              Neumorphism Design
            </Badge>
            <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/30">
              <Eye className="h-3 w-3 mr-1" />
              Enhanced Shadows
            </Badge>
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
              <MousePointer className="h-3 w-3 mr-1" />
              Interactive Hover
            </Badge>
            <Badge variant="secondary" className="bg-purple-500/20 text-purple-300 border-purple-500/30">
              <Smartphone className="h-3 w-3 mr-1" />
              Mobile Responsive
            </Badge>
          </div>
        </div>

        {/* Stats Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsData.map((stat, index) => (
            <div key={index} className="stats-card">
              <div className="flex items-center justify-between mb-4">
                <div className={`h-10 w-10 rounded-full bg-gradient-to-r from-gray-700 to-gray-600 flex items-center justify-center ${stat.color}`}>
                  <stat.icon className="h-5 w-5" />
                </div>
                <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/30 text-xs">
                  {stat.change}
                </Badge>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
                <p className="text-gray-400 text-sm">{stat.title}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Card Types Showcase */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-8">
          {/* Glass Card */}
          <div className="glass-card p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                <Eye className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">Glass Card</h3>
            </div>
            <p className="text-gray-300 mb-4">
              Enhanced glassmorphism design with backdrop blur, gradient backgrounds, and smooth animations.
            </p>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Backdrop Blur</span>
                <span className="text-green-400">20px</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Shadow Depth</span>
                <span className="text-blue-400">Multi-layer</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Animation</span>
                <span className="text-purple-400">Cubic Bezier</span>
              </div>
            </div>
          </div>

          {/* Modern Card */}
          <div className="modern-card p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center">
                <Zap className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">Modern Card</h3>
            </div>
            <p className="text-gray-300 mb-4">
              Neumorphism design with CTNL AI colors, enhanced shadows, and interactive hover effects.
            </p>
            <Button className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700">
              <Sparkles className="h-4 w-4 mr-2" />
              Interactive Button
            </Button>
          </div>

          {/* Dashboard Card */}
          <div className="dashboard-card p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center">
                <BarChart3 className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">Dashboard Card</h3>
            </div>
            <p className="text-gray-300 mb-4">
              Specialized dashboard cards with enhanced gradients and performance metrics display.
            </p>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-green-500 to-green-400 w-3/4 rounded-full"></div>
                </div>
                <p className="text-xs text-gray-400 mt-1">Performance: 75%</p>
              </div>
              <TrendingUp className="h-5 w-5 text-green-400" />
            </div>
          </div>

          {/* Time Card */}
          <div className="time-card p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center">
                <Clock className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">Time Card</h3>
            </div>
            <p className="text-gray-300 mb-4">
              Time tracking cards with green accent colors and specialized hover effects.
            </p>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-white">8:45</p>
                <p className="text-sm text-gray-400">Hours Today</p>
              </div>
              <Activity className="h-8 w-8 text-green-400" />
            </div>
          </div>

          {/* Glass Modal Card */}
          <div className="glass-card-modal p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center">
                <Sparkles className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Modal Card</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Enhanced modal cards with light/dark theme support and premium neumorphism effects.
            </p>
            <div className="flex gap-2">
              <Button size="sm" variant="outline">Cancel</Button>
              <Button size="sm" className="bg-gradient-to-r from-purple-500 to-purple-600">
                Confirm
              </Button>
            </div>
          </div>

          {/* Feature Comparison */}
          <div className="glass-card p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-yellow-500 to-yellow-600 flex items-center justify-center">
                <Palette className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">Enhanced Features</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-green-400"></div>
                <span className="text-sm text-gray-300">Neumorphism shadows</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                <span className="text-sm text-gray-300">CTNL AI color scheme</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-purple-400"></div>
                <span className="text-sm text-gray-300">Interactive animations</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-red-400"></div>
                <span className="text-sm text-gray-300">Mobile responsive</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-orange-400"></div>
                <span className="text-sm text-gray-300">Gradient overlays</span>
              </div>
            </div>
          </div>
        </div>

        {/* Implementation Details */}
        <Card className="glass-card-modal">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-white">
              <Zap className="h-5 w-5 text-red-400" />
              Implementation Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3 text-gray-900 dark:text-white">CSS Enhancements</h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                  <li>• Neumorphism shadow effects (15px/30px)</li>
                  <li>• CTNL AI gradient backgrounds</li>
                  <li>• Cubic-bezier transitions (0.4s)</li>
                  <li>• Multi-layer box shadows</li>
                  <li>• Inset highlights and lowlights</li>
                  <li>• Gradient border animations</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3 text-gray-900 dark:text-white">Interactive Features</h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                  <li>• Transform scale and translate effects</li>
                  <li>• Gradient sweep animations</li>
                  <li>• Enhanced hover states</li>
                  <li>• Mobile-optimized touch responses</li>
                  <li>• Responsive breakpoint adjustments</li>
                  <li>• Theme-aware color schemes</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
