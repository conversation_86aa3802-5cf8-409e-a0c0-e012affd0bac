import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/components/auth/AuthProvider';
import { useToast } from '@/hooks/use-toast';
import { JWTUtils } from '@/utils/jwtUtils';

interface TokenExpiryWarningOptions {
  warningThresholds?: number[];  // Warning thresholds in minutes [15, 5, 1]
  enableNotifications?: boolean;  // Show toast notifications
  enableAutoRefresh?: boolean;   // Attempt auto refresh
  onTokenExpiring?: (minutesLeft: number) => void;
  onTokenExpired?: () => void;
  onRefreshAttempt?: () => void;
  onRefreshSuccess?: () => void;
  onRefreshFailed?: (error: any) => void;
}

interface TokenExpiryState {
  isExpired: boolean;
  isExpiring: boolean;
  timeToExpiry: number;
  minutesToExpiry: number;
  shouldRefresh: boolean;
  lastWarningAt: number | null;
  warningsShown: number[];
}

/**
 * Hook to monitor JWT token expiry and provide warnings
 */
export const useTokenExpiryWarning = (options: TokenExpiryWarningOptions = {}) => {
  const {
    warningThresholds = [15, 5, 1], // Default warnings at 15, 5, and 1 minute
    enableNotifications = true,
    enableAutoRefresh = true,
    onTokenExpiring,
    onTokenExpired,
    onRefreshAttempt,
    onRefreshSuccess,
    onRefreshFailed
  } = options;

  const { session, signOut } = useAuth();
  const { toast } = useToast();

  const [state, setState] = useState<TokenExpiryState>({
    isExpired: false,
    isExpiring: false,
    timeToExpiry: 0,
    minutesToExpiry: 0,
    shouldRefresh: false,
    lastWarningAt: null,
    warningsShown: []
  });

  /**
   * Check token expiry status
   */
  const checkTokenExpiry = useCallback(() => {
    if (!session?.access_token) {
      setState(prev => ({
        ...prev,
        isExpired: false,
        isExpiring: false,
        timeToExpiry: 0,
        minutesToExpiry: 0,
        shouldRefresh: false
      }));
      return;
    }

    const token = session.access_token;
    const isExpired = JWTUtils.isExpired(token);
    const timeToExpiry = JWTUtils.getTimeToExpiry(token);
    const minutesToExpiry = Math.floor(timeToExpiry / (1000 * 60));
    const shouldRefresh = JWTUtils.shouldRefresh(token);
    const isExpiring = minutesToExpiry <= Math.max(...warningThresholds);

    setState(prev => ({
      ...prev,
      isExpired,
      isExpiring,
      timeToExpiry,
      minutesToExpiry,
      shouldRefresh
    }));

    // Handle expired token
    if (isExpired) {
      console.warn('🔐 JWT token has expired');
      onTokenExpired?.();
      
      if (enableNotifications) {
        toast({
          title: "Session Expired",
          description: "Your session has expired. Please log in again.",
          variant: "destructive",
        });
      }
      
      // Auto sign out on expiry
      setTimeout(() => signOut(), 1000);
      return;
    }

    // Handle expiring token warnings
    if (isExpiring) {
      onTokenExpiring?.(minutesToExpiry);
      
      // Show warnings at specified thresholds
      const shouldShowWarning = warningThresholds.some(threshold => {
        const isAtThreshold = minutesToExpiry <= threshold && minutesToExpiry > threshold - 1;
        const notShownYet = !state.warningsShown.includes(threshold);
        return isAtThreshold && notShownYet;
      });

      if (shouldShowWarning && enableNotifications) {
        const currentThreshold = warningThresholds.find(t => 
          minutesToExpiry <= t && !state.warningsShown.includes(t)
        );
        
        if (currentThreshold) {
          setState(prev => ({
            ...prev,
            warningsShown: [...prev.warningsShown, currentThreshold],
            lastWarningAt: Date.now()
          }));

          const message = minutesToExpiry <= 1 
            ? "Your session expires in less than 1 minute"
            : `Your session expires in ${minutesToExpiry} minute${minutesToExpiry > 1 ? 's' : ''}`;

          toast({
            title: "Session Expiring Soon",
            description: message,
            variant: "destructive",
          });
        }
      }
    }

    // Handle auto refresh
    if (shouldRefresh && enableAutoRefresh) {
      handleAutoRefresh();
    }

  }, [session, warningThresholds, enableNotifications, enableAutoRefresh, onTokenExpiring, onTokenExpired, toast, signOut, state.warningsShown]);

  /**
   * Attempt automatic token refresh
   */
  const handleAutoRefresh = useCallback(async () => {
    if (!session) return;

    try {
      console.log('🔐 Attempting automatic token refresh...');
      onRefreshAttempt?.();

      // Supabase handles token refresh automatically through the client
      // We just need to trigger a session refresh
      const { data: { session: newSession }, error } = await session.supabase?.auth.refreshSession() || {};

      if (error) {
        throw error;
      }

      if (newSession) {
        console.log('🔐 Token refresh successful');
        onRefreshSuccess?.();
        
        // Reset warning state for new token
        setState(prev => ({
          ...prev,
          warningsShown: [],
          lastWarningAt: null
        }));

        if (enableNotifications) {
          toast({
            title: "Session Refreshed",
            description: "Your session has been automatically renewed.",
            variant: "default",
          });
        }
      }
    } catch (error) {
      console.error('🔐 Token refresh failed:', error);
      onRefreshFailed?.(error);
      
      if (enableNotifications) {
        toast({
          title: "Session Refresh Failed",
          description: "Unable to refresh your session. Please log in again.",
          variant: "destructive",
        });
      }
    }
  }, [session, onRefreshAttempt, onRefreshSuccess, onRefreshFailed, enableNotifications, toast]);

  /**
   * Manual refresh trigger
   */
  const refreshToken = useCallback(() => {
    handleAutoRefresh();
  }, [handleAutoRefresh]);

  /**
   * Get formatted time until expiry
   */
  const getFormattedTimeToExpiry = useCallback(() => {
    if (!session?.access_token) return 'No session';
    return JWTUtils.getFormattedTimeToExpiry(session.access_token);
  }, [session]);

  /**
   * Check if token is in warning period
   */
  const isInWarningPeriod = useCallback((minutes: number = 5) => {
    return state.minutesToExpiry <= minutes && state.minutesToExpiry > 0;
  }, [state.minutesToExpiry]);

  // Set up interval to check token expiry
  useEffect(() => {
    if (!session?.access_token) return;

    // Check immediately
    checkTokenExpiry();

    // Set up interval to check every 30 seconds
    const interval = setInterval(checkTokenExpiry, 30 * 1000);

    return () => clearInterval(interval);
  }, [session, checkTokenExpiry]);

  // Reset warnings when session changes
  useEffect(() => {
    setState(prev => ({
      ...prev,
      warningsShown: [],
      lastWarningAt: null
    }));
  }, [session?.access_token]);

  return {
    // State
    ...state,
    
    // Actions
    refreshToken,
    
    // Utilities
    getFormattedTimeToExpiry,
    isInWarningPeriod,
    
    // Token info
    hasValidToken: !!session?.access_token && !state.isExpired,
    tokenAge: session?.access_token ? JWTUtils.getTokenAge(session.access_token) : 0,
  };
};

/**
 * Simple hook for basic token expiry checking
 */
export const useTokenExpiry = () => {
  const { session } = useAuth();
  
  const isExpired = session?.access_token ? JWTUtils.isExpired(session.access_token) : true;
  const timeToExpiry = session?.access_token ? JWTUtils.getTimeToExpiry(session.access_token) : 0;
  const shouldRefresh = session?.access_token ? JWTUtils.shouldRefresh(session.access_token) : false;
  
  return {
    isExpired,
    timeToExpiry,
    shouldRefresh,
    minutesToExpiry: Math.floor(timeToExpiry / (1000 * 60)),
    formattedTimeToExpiry: session?.access_token ? JWTUtils.getFormattedTimeToExpiry(session.access_token) : 'No session'
  };
};
