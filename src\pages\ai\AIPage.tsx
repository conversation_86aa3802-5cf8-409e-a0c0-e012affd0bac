import { ErrorBoundary } from "@/components/ErrorBoundary";
import { FuturisticAIInterface } from "@/components/ai/FuturisticAIInterface";
import { EnhancedRAGSystem } from "@/components/ai/EnhancedRAGSystem";
import { AIRealtimeUpdates } from "@/components/ai/AIRealtimeUpdates";
import { AISystemController } from "@/components/ai/AISystemController";
import { AITestComponent } from "@/components/ai/AITestComponent";
import { HackerAIInterface } from "@/components/ai/HackerAIInterface";
import { ProjectManagerAssistant } from "@/components/project/ProjectManagerAssistant";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Brain, Database, Activity, Zap, Settings, Terminal, Users } from "lucide-react";

const AIPage = () => {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-background p-4 md:p-6" data-aos="fade-in">
        {/* Enhanced Header with System Theme */}
        <div className="text-center mb-8" data-aos="fade-down" data-aos-duration="800">
          <div className="relative inline-block">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-primary via-secondary to-primary bg-clip-text text-transparent mb-4" data-aos="zoom-in" data-aos-delay="100">
              AI COMMAND CENTER
            </h1>
            <div className="absolute -inset-4 border border-primary/30 rounded-full animate-pulse"></div>
          </div>
          <p className="text-xl text-muted-foreground mb-2" data-aos="fade-up" data-aos-delay="200">
            Advanced Intelligence • RAG Enhanced • Multi-LLM Powered
          </p>
          <div className="flex justify-center gap-4 text-sm text-muted-foreground" data-aos="fade-up" data-aos-delay="300">
            <span data-aos="fade-right" data-aos-delay="400">🧠 Neural Networks Active</span>
            <span data-aos="fade-right" data-aos-delay="500">🚀 Real-time Processing</span>
            <span data-aos="fade-right" data-aos-delay="600">🔒 Role-Based Security</span>
            <span data-aos="fade-right" data-aos-delay="700">⚡ Continuous Learning</span>
          </div>
        </div>

        <Tabs defaultValue="hacker" className="space-y-6" data-aos="fade-up" data-aos-delay="400">
          <TabsList className="grid w-full grid-cols-6 bg-card border border-border backdrop-blur-xl rounded-2xl p-2" data-aos="slide-down" data-aos-delay="500">
            <TabsTrigger
              value="hacker"
              className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300"
              data-aos="flip-left" data-aos-delay="550"
            >
              <Terminal className="h-4 w-4" />
              Hacker Terminal
            </TabsTrigger>
            <TabsTrigger 
              value="commander" 
              className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300"
              data-aos="flip-left" data-aos-delay="600"
            >
              <Brain className="h-4 w-4" />
              AI Commander
            </TabsTrigger>
            <TabsTrigger 
              value="rag" 
              className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300"
              data-aos="flip-left" data-aos-delay="700"
            >
              <Database className="h-4 w-4" />
              RAG System
            </TabsTrigger>
            <TabsTrigger 
              value="realtime" 
              className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300"
              data-aos="flip-left" data-aos-delay="800"
            >
              <Activity className="h-4 w-4" />
              Live Neural Feed
            </TabsTrigger>
            <TabsTrigger
              value="controller"
              className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300"
              data-aos="flip-left" data-aos-delay="900"
            >
              <Settings className="h-4 w-4" />
              System Controller
            </TabsTrigger>
            <TabsTrigger
              value="tests"
              className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300"
              data-aos="flip-left" data-aos-delay="1000"
            >
              <Zap className="h-4 w-4" />
              Database Tests
            </TabsTrigger>
            <TabsTrigger
              value="project-manager"
              className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300"
              data-aos="flip-left" data-aos-delay="1100"
            >
              <Users className="h-4 w-4" />
              Project Manager AI
            </TabsTrigger>
          </TabsList>

          <TabsContent value="hacker" data-aos="zoom-in" data-aos-delay="600">
            <ErrorBoundary>
              <HackerAIInterface />
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="commander" data-aos="zoom-in" data-aos-delay="600">
            <ErrorBoundary>
              <FuturisticAIInterface />
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="rag" data-aos="zoom-in" data-aos-delay="600">
            <ErrorBoundary>
              <EnhancedRAGSystem />
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="realtime" data-aos="zoom-in" data-aos-delay="600">
            <ErrorBoundary>
              <div className="bg-card/80 rounded-[30px] border border-border backdrop-blur-xl p-6" data-aos="slide-up" data-aos-delay="700">
                <AIRealtimeUpdates />
              </div>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="controller" data-aos="zoom-in" data-aos-delay="600">
            <ErrorBoundary>
              <div className="bg-card/80 rounded-[30px] border border-border backdrop-blur-xl p-6" data-aos="slide-up" data-aos-delay="700">
                <AISystemController />
              </div>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="tests" data-aos="zoom-in" data-aos-delay="600">
            <ErrorBoundary>
              <div className="bg-card/80 rounded-[30px] border border-border backdrop-blur-xl p-6" data-aos="slide-up" data-aos-delay="700">
                <AITestComponent />
              </div>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="project-manager" data-aos="zoom-in" data-aos-delay="600">
            <ErrorBoundary>
              <div className="bg-card/80 rounded-[30px] border border-border backdrop-blur-xl p-6" data-aos="slide-up" data-aos-delay="700">
                <ProjectManagerAssistant />
              </div>
            </ErrorBoundary>
          </TabsContent>
        </Tabs>
      </div>
    </ErrorBoundary>
  );
};

export default AIPage;
