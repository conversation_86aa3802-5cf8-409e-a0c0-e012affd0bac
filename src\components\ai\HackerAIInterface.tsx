import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Terminal, 
  Code, 
  Database, 
  FileText, 
  Cpu, 
  Activity, 
  Zap, 
  Brain, 
  Network,
  Shield,
  Eye,
  Search,
  Upload,
  Download,
  Settings,
  Play,
  Pause,
  Square,
  ChevronRight,
  Hash,
  Lock,
  Unlock
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSimpleLangChain } from "@/hooks/useSimpleLangChain";
import { aiService, processAIRequest } from "@/lib/ai-service";
import { APIKeyManager } from "./APIKeyManager";
import { DatabaseQueryInterface } from "./DatabaseQueryInterface";
import { DocumentAnalysisInterface } from "./DocumentAnalysisInterface";
import { AdvancedDocumentAnalysis } from "./AdvancedDocumentAnalysis";
import { AgenticAISystem } from "./AgenticAISystem";

interface HackerMessage {
  id: string;
  type: 'command' | 'response' | 'system' | 'error' | 'success';
  content: string;
  timestamp: Date;
  metadata?: any;
  executionTime?: number;
}

interface AIModule {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'processing' | 'error';
  description: string;
  capabilities: string[];
  lastActivity?: Date;
}

interface SystemMetrics {
  cpu: number;
  memory: number;
  network: number;
  aiProcesses: number;
  dbConnections: number;
  uptime: string;
}

export const HackerAIInterface: React.FC = () => {
  const [messages, setMessages] = useState<HackerMessage[]>([]);
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentModule, setCurrentModule] = useState('MAIN_TERMINAL');
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    cpu: 0,
    memory: 0,
    network: 0,
    aiProcesses: 0,
    dbConnections: 0,
    uptime: '00:00:00'
  });

  // Fetch real system metrics from database
  const { data: realMetrics } = useQuery({
    queryKey: ['system-metrics'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('system_activities')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(1);

        // If table doesn't exist (404 error), return mock data instead of null
        if (error && (error.status === 404 || error.code === 'PGRST106')) {
          console.log('system_activities table not found, using mock metrics');
          return {
            id: 'mock-1',
            cpu_usage: Math.random() * 100,
            memory_usage: Math.random() * 100,
            disk_usage: Math.random() * 100,
            network_io: Math.random() * 1000,
            active_connections: Math.floor(Math.random() * 50),
            created_at: new Date().toISOString()
          };
        }

        if (error) throw error;

        // Return mock data if no data found instead of null
        if (!data || data.length === 0) {
          return {
            id: 'mock-2',
            cpu_usage: Math.random() * 100,
            memory_usage: Math.random() * 100,
            disk_usage: Math.random() * 100,
            network_io: Math.random() * 1000,
            active_connections: Math.floor(Math.random() * 50),
            created_at: new Date().toISOString()
          };
        }

        return data[0];
      } catch (error) {
        console.log('Error fetching system metrics, using mock data:', error);
        return {
          id: 'mock-3',
          cpu_usage: Math.random() * 100,
          memory_usage: Math.random() * 100,
          disk_usage: Math.random() * 100,
          network_io: Math.random() * 1000,
          active_connections: Math.floor(Math.random() * 50),
          created_at: new Date().toISOString()
        };
      }
    },
    refetchInterval: 5000 // Update every 5 seconds
  });
  const [aiModules, setAIModules] = useState<AIModule[]>([]);
  const [showApiKeyManager, setShowApiKeyManager] = useState(false);
  const [isApiKeyConfigured, setIsApiKeyConfigured] = useState(false);
  const [activeTab, setActiveTab] = useState('terminal');
  
  const terminalRef = useRef<HTMLDivElement>(null);
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const { sendHackerMessage } = useSimpleLangChain();

  // Initialize system
  useEffect(() => {
    initializeSystem();
    startSystemMonitoring();
    loadConversationHistory();
  }, []);

  // Auto-scroll terminal
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [messages]);

  const initializeSystem = () => {
    const bootMessage: HackerMessage = {
      id: 'boot-' + Date.now(),
      type: 'system',
      content: `
╔══════════════════════════════════════════════════════════════╗
║                    AI WORKBOARD TERMINAL v2.1               ║
║                     NEURAL INTERFACE ACTIVE                 ║
╚══════════════════════════════════════════════════════════════╝

[SYSTEM] Initializing AI modules...
[SYSTEM] Loading neural networks...
[SYSTEM] Establishing database connections...
[SYSTEM] Activating agentic capabilities...

> System ready. Type 'help' for available commands.
> User: ${userProfile?.full_name || 'Anonymous'} | Role: ${userProfile?.role || 'user'}
> Access Level: ${userProfile?.role === 'admin' ? 'FULL_CONTROL' : 'RESTRICTED'}
`,
      timestamp: new Date()
    };

    setMessages([bootMessage]);

    // Initialize AI modules with real data
    const modules: AIModule[] = [
      {
        id: 'neural_processor',
        name: 'Neural Processor',
        status: realMetrics ? 'online' : 'offline',
        description: 'Core AI reasoning and decision making',
        capabilities: ['natural_language', 'pattern_recognition', 'decision_trees'],
        lastActivity: realMetrics ? new Date(realMetrics.created_at) : undefined
      },
      {
        id: 'document_analyzer',
        name: 'Document Analyzer',
        status: 'online',
        description: 'Advanced document processing and analysis',
        capabilities: ['ocr', 'content_extraction', 'semantic_analysis', 'categorization'],
        lastActivity: new Date()
      },
      {
        id: 'database_agent',
        name: 'Database Agent',
        status: 'online',
        description: 'Intelligent database operations and queries',
        capabilities: ['sql_generation', 'data_mining', 'analytics', 'optimization'],
        lastActivity: new Date()
      },
      {
        id: 'rag_system',
        name: 'RAG System',
        status: 'online',
        description: 'Retrieval-Augmented Generation for knowledge base',
        capabilities: ['vector_search', 'embedding_generation', 'context_retrieval']
      },
      {
        id: 'task_orchestrator',
        name: 'Task Orchestrator',
        status: 'online',
        description: 'Autonomous task execution and workflow management',
        capabilities: ['task_planning', 'execution_monitoring', 'resource_allocation']
      },
      {
        id: 'security_monitor',
        name: 'Security Monitor',
        status: 'online',
        description: 'System security and access control',
        capabilities: ['threat_detection', 'access_control', 'audit_logging']
      }
    ];

    setAIModules(modules);
  };

  const startSystemMonitoring = () => {
    const updateMetrics = () => {
      setSystemMetrics(prev => ({
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        network: Math.random() * 100,
        aiProcesses: Math.floor(Math.random() * 10) + 5,
        dbConnections: Math.floor(Math.random() * 20) + 10,
        uptime: new Date().toLocaleTimeString()
      }));
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 2000);
    return () => clearInterval(interval);
  };

  const loadConversationHistory = async () => {
    try {
      const { data, error } = await supabase
        .from('ai_interactions')
        .select('*')
        .eq('user_id', userProfile?.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      if (data && data.length > 0) {
        const historyMessage: HackerMessage = {
          id: 'history-' + Date.now(),
          type: 'system',
          content: `[SYSTEM] Loaded ${data.length} previous interactions from neural memory banks.`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, historyMessage]);
      }
    } catch (error) {
      console.error('Error loading conversation history:', error);
    }
  };

  const processCommand = async (command: string) => {
    const startTime = Date.now();
    
    const userMessage: HackerMessage = {
      id: 'user-' + Date.now(),
      type: 'command',
      content: `> ${command}`,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsProcessing(true);

    try {
      // Handle built-in commands
      if (command.toLowerCase() === 'help') {
        const helpMessage: HackerMessage = {
          id: 'help-' + Date.now(),
          type: 'response',
          content: `
╔══════════════════════════════════════════════════════════════╗
║                        AVAILABLE COMMANDS                   ║
╚══════════════════════════════════════════════════════════════╝

SYSTEM COMMANDS:
  help                    - Show this help menu
  status                  - Display system status
  modules                 - List AI modules and capabilities
  clear                   - Clear terminal
  config                  - Show configuration
  
AI OPERATIONS:
  analyze <text>          - Analyze text content
  query <database_query>  - Execute database query
  search <keywords>       - Search knowledge base
  upload                  - Upload and analyze documents
  
AGENTIC COMMANDS:
  execute <task>          - Execute autonomous task
  orchestrate <workflow>  - Run complex workflow
  monitor <system>        - Monitor system component
  
ADVANCED:
  neural <prompt>         - Direct neural network access
  rag <query>            - RAG system query
  agent <instruction>     - Autonomous agent instruction
`,
          timestamp: new Date(),
          executionTime: Date.now() - startTime
        };
        setMessages(prev => [...prev, helpMessage]);
        return;
      }

      if (command.toLowerCase() === 'status') {
        const statusMessage: HackerMessage = {
          id: 'status-' + Date.now(),
          type: 'response',
          content: `
╔══════════════════════════════════════════════════════════════╗
║                        SYSTEM STATUS                        ║
╚══════════════════════════════════════════════════════════════╝

CPU Usage:        ${systemMetrics.cpu.toFixed(1)}%
Memory Usage:     ${systemMetrics.memory.toFixed(1)}%
Network I/O:      ${systemMetrics.network.toFixed(1)}%
AI Processes:     ${systemMetrics.aiProcesses}
DB Connections:   ${systemMetrics.dbConnections}
Uptime:          ${systemMetrics.uptime}

AI MODULES STATUS:
${aiModules.map(module => 
  `  ${module.name.padEnd(20)} [${module.status.toUpperCase()}]`
).join('\n')}
`,
          timestamp: new Date(),
          executionTime: Date.now() - startTime
        };
        setMessages(prev => [...prev, statusMessage]);
        return;
      }

      if (command.toLowerCase() === 'clear') {
        setMessages([]);
        return;
      }

      // Process AI commands
      await processAICommand(command, startTime);

    } catch (error) {
      const errorMessage: HackerMessage = {
        id: 'error-' + Date.now(),
        type: 'error',
        content: `[ERROR] ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        timestamp: new Date(),
        executionTime: Date.now() - startTime
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  const processAICommand = async (command: string, startTime: number) => {
    // Simulate processing
    const processingMessage: HackerMessage = {
      id: 'processing-' + Date.now(),
      type: 'system',
      content: `[PROCESSING] Executing command: ${command}`,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, processingMessage]);

    try {
      // Try LangChain first
      const langChainResponse = await sendHackerMessage(command);

      if (langChainResponse) {
        const responseMessage: HackerMessage = {
          id: 'response-' + Date.now(),
          type: 'response',
          content: `[AI-ENHANCED] ${langChainResponse.content}`,
          timestamp: new Date(),
          executionTime: Date.now() - startTime,
          metadata: { enhanced: langChainResponse.enhanced, ...langChainResponse.metadata }
        };
        setMessages(prev => [...prev, responseMessage]);
        return;
      }
    } catch (error) {
      console.warn('LangChain failed, falling back to basic AI:', error);
    }

    // Fallback to basic AI assistant
    const { data, error } = await supabase.functions.invoke('ai-assistant', {
      body: {
        message: command,
        conversation_history: messages.slice(-5),
        context: {
          interface: 'hacker_terminal',
          user_role: userProfile?.role,
          modules: aiModules.map(m => m.id)
        }
      }
    });

    if (error) throw error;

    const responseMessage: HackerMessage = {
      id: 'response-' + Date.now(),
      type: 'response',
      content: data.response || 'Command executed successfully.',
      timestamp: new Date(),
      executionTime: Date.now() - startTime
    };

    setMessages(prev => [...prev, responseMessage]);

    // Log interaction
    await supabase.from('ai_interactions').insert({
      user_id: userProfile?.id,
      role: 'user',
      message: command,
      query: command,
      type: 'hacker_terminal'
    });

    await supabase.from('ai_interactions').insert({
      user_id: userProfile?.id,
      role: 'assistant',
      message: responseMessage.content,
      response: responseMessage.content,
      type: 'hacker_terminal',
      metadata: { 
        execution_time: responseMessage.executionTime,
        interface: 'hacker_terminal'
      }
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isProcessing && input.trim()) {
      processCommand(input.trim());
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400';
      case 'processing': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getMessageColor = (type: string) => {
    switch (type) {
      case 'command': return 'text-cyan-400';
      case 'response': return 'text-green-400';
      case 'system': return 'text-blue-400';
      case 'error': return 'text-red-400';
      case 'success': return 'text-green-300';
      default: return 'text-gray-300';
    }
  };

  return (
    <div className="min-h-screen bg-black text-green-400 font-mono p-4">
      <div className="max-w-7xl mx-auto space-y-4">
        {/* Header */}
        <div className="border border-green-500 rounded-lg p-4 bg-black/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Terminal className="h-6 w-6 text-green-400" />
              <h1 className="text-xl font-bold text-green-400">AI WORKBOARD NEURAL INTERFACE</h1>
              <Badge variant="outline" className="border-green-500 text-green-400">
                v2.1.0
              </Badge>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-xs">ONLINE</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowApiKeyManager(true)}
                className="border-green-500 text-green-400 hover:bg-green-500/10"
              >
                <Settings className="h-4 w-4 mr-2" />
                CONFIG
              </Button>
            </div>
          </div>
        </div>

        {/* System Metrics */}
        <div className="grid grid-cols-6 gap-4">
          {[
            { label: 'CPU', value: systemMetrics.cpu, icon: Cpu },
            { label: 'MEM', value: systemMetrics.memory, icon: Activity },
            { label: 'NET', value: systemMetrics.network, icon: Network },
            { label: 'AI', value: systemMetrics.aiProcesses, icon: Brain },
            { label: 'DB', value: systemMetrics.dbConnections, icon: Database },
            { label: 'SEC', value: 100, icon: Shield }
          ].map((metric, index) => (
            <Card key={index} className="bg-black border-green-500">
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <metric.icon className="h-4 w-4 text-green-400" />
                  <span className="text-xs text-green-400">{metric.label}</span>
                </div>
                <div className="mt-2">
                  <div className="text-lg font-bold text-green-400">
                    {typeof metric.value === 'number' && metric.value < 100 
                      ? `${metric.value.toFixed(0)}%` 
                      : metric.value}
                  </div>
                  {typeof metric.value === 'number' && metric.value < 100 && (
                    <Progress 
                      value={metric.value} 
                      className="h-1 mt-1 bg-gray-800"
                    />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="bg-black border border-green-500">
            <TabsTrigger value="terminal" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              <Terminal className="h-4 w-4 mr-2" />
              TERMINAL
            </TabsTrigger>
            <TabsTrigger value="modules" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              <Cpu className="h-4 w-4 mr-2" />
              MODULES
            </TabsTrigger>
            <TabsTrigger value="agentic" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              <Brain className="h-4 w-4 mr-2" />
              AGENTIC
            </TabsTrigger>
            <TabsTrigger value="database" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              <Database className="h-4 w-4 mr-2" />
              DATABASE
            </TabsTrigger>
            <TabsTrigger value="documents" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              <FileText className="h-4 w-4 mr-2" />
              DOCUMENTS
            </TabsTrigger>
            <TabsTrigger value="advanced-docs" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              <Brain className="h-4 w-4 mr-2" />
              ADVANCED OCR
            </TabsTrigger>
          </TabsList>

          <TabsContent value="terminal">
            <Card className="bg-black border-green-500 h-96">
              <CardContent className="p-0">
                <ScrollArea className="h-full p-4" ref={terminalRef}>
                  <div className="space-y-2">
                    {messages.map((message) => (
                      <div key={message.id} className="flex flex-col">
                        <div className={`${getMessageColor(message.type)} whitespace-pre-wrap`}>
                          {message.content}
                        </div>
                        {message.executionTime && (
                          <div className="text-xs text-gray-500 mt-1">
                            Execution time: {message.executionTime}ms
                          </div>
                        )}
                      </div>
                    ))}
                    {isProcessing && (
                      <div className="flex items-center gap-2 text-yellow-400">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                        Processing...
                      </div>
                    )}
                  </div>
                </ScrollArea>
                <div className="border-t border-green-500 p-4">
                  <div className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-green-400" />
                    <Input
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Enter command..."
                      disabled={isProcessing}
                      className="bg-transparent border-none text-green-400 placeholder-green-600 focus:ring-0"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="modules">
            <div className="grid grid-cols-2 gap-4">
              {aiModules.map((module) => (
                <Card key={module.id} className="bg-black border-green-500">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between text-green-400">
                      <span>{module.name}</span>
                      <Badge 
                        variant="outline" 
                        className={`border-green-500 ${getStatusColor(module.status)}`}
                      >
                        {module.status.toUpperCase()}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-400 mb-3">{module.description}</p>
                    <div className="space-y-1">
                      <div className="text-xs text-green-400 font-semibold">CAPABILITIES:</div>
                      {module.capabilities.map((cap, index) => (
                        <div key={index} className="text-xs text-gray-300 flex items-center gap-2">
                          <Hash className="h-3 w-3" />
                          {cap.replace('_', ' ').toUpperCase()}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="agentic">
            <AgenticAISystem />
          </TabsContent>

          <TabsContent value="database">
            <DatabaseQueryInterface />
          </TabsContent>

          <TabsContent value="documents">
            <DocumentAnalysisInterface />
          </TabsContent>

          <TabsContent value="advanced-docs">
            <AdvancedDocumentAnalysis />
          </TabsContent>
        </Tabs>

        {/* API Key Manager Modal */}
        {showApiKeyManager && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
            <div className="bg-black border border-green-500 rounded-lg p-6 max-w-md w-full mx-4">
              <APIKeyManager 
                onApiKeyConfigured={(configured) => {
                  setIsApiKeyConfigured(configured);
                  setShowApiKeyManager(false);
                }}
              />
              <Button
                onClick={() => setShowApiKeyManager(false)}
                variant="outline"
                className="w-full mt-4 border-green-500 text-green-400 hover:bg-green-500/10"
              >
                CLOSE
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
