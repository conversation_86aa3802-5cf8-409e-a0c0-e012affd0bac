import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Clock,
  MapPin,
  Smartphone,
  Monitor,
  Tablet,
  Play,
  Square,
  MoreHorizontal,
  Timer,
  Laptop,
  Phone
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTimeTracking } from '@/hooks/useTimeTracking';
import { locationService } from '@/services/locationService';
import { deviceService } from '@/services/deviceService';
import { TimeCardProps, LocationData, DeviceInfo } from '@/types/timeTracking';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

export const CompactTimeCard: React.FC<TimeCardProps> = ({
  userRole,
  showControls = true,
}) => {
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [locationAddress, setLocationAddress] = useState<string>('Getting location...');

  const {
    currentSession,
    isLoading,
    actions,
    isClockingIn,
    isClockingOut,
    isUpdatingStatus
  } = useTimeTracking({
    realtime: true,
    refetchInterval: 30000
  });

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Get device info and location on mount
  useEffect(() => {
    const getDeviceInfo = async () => {
      const info = deviceService.getDeviceInfo();
      const ipAddress = await deviceService.getIPAddress();
      setDeviceInfo({ ...info, ipAddress });
    };

    getDeviceInfo();

    // Get current location for display
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const address = await locationService.reverseGeocode(
              position.coords.latitude,
              position.coords.longitude
            );
            setLocationAddress(address);
          } catch (error) {
            setLocationAddress('Location unavailable');
          }
        },
        () => {
          setLocationAddress('Location access denied');
        }
      );
    }
  }, []);

  const isCurrentlyClockedIn = !!currentSession && !currentSession.clock_out;
  
  const getWorkDuration = () => {
    if (!currentSession?.clock_in) return '00:00';
    
    const clockInTime = new Date(currentSession.clock_in);
    const now = currentTime;
    const diff = now.getTime() - clockInTime.getTime();
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  const getDeviceIcon = () => {
    if (!deviceInfo) return <Monitor className="h-3 w-3" />;
    
    switch (deviceInfo.type) {
      case 'mobile':
        return <Smartphone className="h-3 w-3" />;
      case 'tablet':
        return <Tablet className="h-3 w-3" />;
      default:
        return <Monitor className="h-3 w-3" />;
    }
  };

  const getStatusColor = () => {
    if (!isCurrentlyClockedIn) return 'bg-gray-500/20 text-gray-500';
    
    switch (currentSession?.status) {
      case 'break':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'lunch':
        return 'bg-orange-500/20 text-orange-500';
      case 'overtime':
        return 'bg-purple-500/20 text-purple-500';
      default:
        return 'bg-green-500/20 text-green-500';
    }
  };

  const handleClockIn = async () => {
    setIsGettingLocation(true);
    setIsAnimating(true);

    try {
      const location: LocationData = await locationService.getCurrentLocation();
      const device: DeviceInfo = deviceInfo || deviceService.getDeviceInfo();

      await actions.clockIn({
        location,
        device,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        method: 'manual',
      });

      // Update location display
      setLocationAddress(location.address || 'Location captured');
    } catch (error) {
      console.error('Clock in failed:', error);
    } finally {
      setIsGettingLocation(false);
      setTimeout(() => setIsAnimating(false), 2000);
    }
  };

  const handleClockOut = async () => {
    if (!currentSession?.id) return;

    setIsAnimating(true);

    try {
      const location: LocationData = await locationService.getCurrentLocation();
      const device: DeviceInfo = deviceInfo || deviceService.getDeviceInfo();

      await actions.clockOut(currentSession.id, {
        location,
        device,
        method: 'manual',
      });
    } catch (error) {
      console.error('Clock out failed:', error);
    } finally {
      setTimeout(() => setIsAnimating(false), 2000);
    }
  };

  const handleStatusChange = async (status: 'active' | 'break' | 'lunch') => {
    if (!currentSession?.id) return;
    
    try {
      await actions.updateStatus(currentSession.id, status);
    } catch (error) {
      console.error('Status update failed:', error);
    }
  };

  if (isLoading) {
    return (
      <Card className="relative overflow-hidden bg-gradient-to-br from-red-500/10 to-red-600/20 border-red-500/20">
        <CardContent className="p-4">
          <div className="animate-pulse space-y-2">
            <div className="h-3 bg-red-500/20 rounded w-3/4"></div>
            <div className="h-6 bg-red-500/20 rounded w-1/2"></div>
            <div className="h-3 bg-red-500/20 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "relative overflow-hidden transition-all duration-500",
      "bg-gradient-to-br from-[#ff1c04]/10 to-[#0FA0CE]/10 border-[#ff1c04]/20",
      "shadow-2xl hover:shadow-3xl",
      "backdrop-blur-sm"
    )}>
      {/* Enhanced Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Orbs */}
        <div className="absolute -top-20 -right-20 w-48 h-48 bg-gradient-to-br from-[#ff1c04]/20 to-[#0FA0CE]/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-20 -left-20 w-48 h-48 bg-gradient-to-tr from-[#0FA0CE]/20 to-[#ff1c04]/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>

        {/* Floating Particles */}
        <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-[#ff1c04]/40 rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-[#0FA0CE]/40 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-[#ff1c04]/60 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      <CardContent className="relative z-10 p-6 text-center">
        {/* 3D Circle Animation with Layers */}
        <div className="relative mb-6 flex items-center justify-center">
          <div className={`relative w-32 h-32 ${isAnimating ? 'animate-spin' : ''}`}>
            {/* Outer Ring */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#ff1c04]/30 via-[#0FA0CE]/20 to-[#ff1c04]/30 animate-pulse blur-sm"></div>

            {/* Middle Ring */}
            <div className="absolute inset-2 rounded-full bg-gradient-to-r from-[#0FA0CE]/40 via-[#ff1c04]/30 to-[#0FA0CE]/40 animate-pulse" style={{ animationDelay: '0.5s' }}></div>

            {/* Inner Circle - Main Button */}
            <div
              className={cn(
                "absolute inset-4 rounded-full shadow-2xl hover:shadow-3xl cursor-pointer flex items-center justify-center transform hover:scale-110 transition-all duration-500 pulse-glow",
                isCurrentlyClockedIn
                  ? "bg-gradient-to-br from-[#ff1c04] via-[#e01703] to-[#cc1502]"
                  : "bg-gradient-to-br from-[#0FA0CE] via-[#0d8bb5] to-[#0a759c]"
              )}
              onClick={showControls ? (isCurrentlyClockedIn ? handleClockOut : handleClockIn) : undefined}
            >
              <div className="text-center text-white relative z-10">
                {isGettingLocation ? (
                  <MapPin className="h-6 w-6 mx-auto mb-1 animate-pulse drop-shadow-lg" />
                ) : isClockingIn || isClockingOut ? (
                  <Timer className="h-6 w-6 mx-auto mb-1 animate-spin drop-shadow-lg" />
                ) : (
                  <Clock className="h-6 w-6 mx-auto mb-1 drop-shadow-lg" />
                )}
                <span className="text-xs font-bold tracking-wide">
                  {isGettingLocation ? 'LOCATING' :
                   isClockingIn ? 'CLOCKING IN' :
                   isClockingOut ? 'CLOCKING OUT' :
                   isCurrentlyClockedIn ? 'CLOCK OUT' : 'CLOCK IN'}
                </span>
              </div>

              {/* Inner Glow Effect */}
              <div className="absolute inset-1 rounded-full bg-gradient-to-br from-white/20 to-transparent"></div>
            </div>

            {/* Floating particles around button */}
            <div className="absolute -top-1 -left-1 w-1 h-1 bg-[#ff1c04]/60 rounded-full animate-pulse"></div>
            <div className="absolute -bottom-1 -right-1 w-1 h-1 bg-[#0FA0CE]/60 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
          </div>
        </div>

        {/* Status Badge */}
        <div className="flex justify-center mb-4">
          <Badge className={cn("text-xs font-medium px-3 py-1", getStatusColor())}>
            {isCurrentlyClockedIn ? (
              currentSession?.status?.toUpperCase() || 'ACTIVE'
            ) : (
              'CLOCKED OUT'
            )}
          </Badge>
        </div>

        {/* Current Time Display */}
        <div className="text-center mb-4">
          <div className="text-lg font-bold text-foreground">
            {format(currentTime, 'HH:mm:ss')}
          </div>
          <div className="text-xs text-muted-foreground">
            {format(currentTime, 'EEEE, MMMM d')}
          </div>
        </div>

        {/* Work Duration */}
        {isCurrentlyClockedIn && (
          <div className="text-center mb-4">
            <div className="text-xs text-muted-foreground mb-1">Work Duration</div>
            <div className="text-lg font-bold text-[#ff1c04]">
              {getWorkDuration()}
            </div>
          </div>
        )}

        {/* Micro Data Display */}
        <div className="space-y-2 text-xs">
          {/* Location */}
          <div className="flex items-center gap-2 p-2 rounded-lg bg-gradient-to-r from-[#ff1c04]/5 to-[#0FA0CE]/5 border border-[#ff1c04]/10">
            <MapPin className="h-3 w-3 text-[#ff1c04] flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <div className="text-[#ff1c04] font-medium">Location</div>
              <div className="text-muted-foreground truncate">{locationAddress}</div>
            </div>
          </div>

          {/* Device Info */}
          {deviceInfo && (
            <div className="flex items-center gap-2 p-2 rounded-lg bg-gradient-to-r from-[#0FA0CE]/5 to-[#ff1c04]/5 border border-[#0FA0CE]/10">
              {getDeviceIcon()}
              <div className="flex-1 min-w-0">
                <div className="text-[#0FA0CE] font-medium">Device</div>
                <div className="text-muted-foreground truncate">
                  {deviceInfo.brand} {deviceInfo.model} • {deviceInfo.os}
                </div>
              </div>
            </div>
          )}

          {/* Clock In Time */}
          {isCurrentlyClockedIn && currentSession?.clock_in && (
            <div className="flex items-center gap-2 p-2 rounded-lg bg-gradient-to-r from-green-500/5 to-blue-500/5 border border-green-500/10">
              <Clock className="h-3 w-3 text-green-500 flex-shrink-0" />
              <div className="flex-1">
                <div className="text-green-500 font-medium">Clocked In</div>
                <div className="text-muted-foreground">
                  {format(new Date(currentSession.clock_in), 'HH:mm')}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Status Change Controls */}
        {showControls && isCurrentlyClockedIn && (
          <div className="mt-4 pt-4 border-t border-[#ff1c04]/20">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="w-full text-xs">
                  <MoreHorizontal className="h-3 w-3 mr-1" />
                  Change Status
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center">
                <DropdownMenuItem onClick={() => handleStatusChange('active')}>
                  Set Active
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('break')}>
                  Take Break
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('lunch')}>
                  Lunch Break
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </CardContent>

      {/* Animated Border Effect */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-red-500/20 via-transparent to-red-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </Card>
  );
};
