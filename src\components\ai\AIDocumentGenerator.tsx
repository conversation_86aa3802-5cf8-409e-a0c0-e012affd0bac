import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  <PERSON>Text, 
  Download, 
  <PERSON>rkles, 
  BarChart3, 
  Table, 
  FileSpreadsheet,
  Brain,
  Zap,
  Settings,
  Plus,
  Eye,
  Trash2,
  RefreshCw,
  TrendingUp,
  PieChart,
  LineChart,
  BarChart,
  Activity
} from "lucide-react";

interface DocumentTemplate {
  id: string;
  name: string;
  description: string;
  template_type: string;
  category: string;
  template_config: any;
  ai_prompts: any;
  chart_configs: any[];
  table_configs: any[];
  styling_config: any;
  data_sources: any[];
  variables: any[];
  is_public: boolean;
  created_by: string;
  created_at: string;
}

interface DocumentGeneration {
  id: string;
  template_id: string;
  document_name: string;
  document_type: string;
  generation_type: string;
  status: string;
  file_path: string;
  file_size: number;
  processing_time: number;
  download_count: number;
  generated_at: string;
  template?: DocumentTemplate;
}

interface GenerationConfig {
  template_id: string;
  document_name: string;
  ai_enhancement: boolean;
  data_sources: string[];
  variables: Record<string, any>;
  chart_types: string[];
  include_analysis: boolean;
  output_format: string;
}

export const AIDocumentGenerator = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [isCreateTemplateOpen, setIsCreateTemplateOpen] = useState(false);
  const [isGenerateOpen, setIsGenerateOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  
  const [generationConfig, setGenerationConfig] = useState<GenerationConfig>({
    template_id: '',
    document_name: '',
    ai_enhancement: true,
    data_sources: [],
    variables: {},
    chart_types: [],
    include_analysis: true,
    output_format: 'excel'
  });

  // Fetch document templates
  const { data: templates = [], isLoading: templatesLoading } = useQuery({
    queryKey: ['document-templates'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('document_templates')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching templates:', error);
        return [];
      }
      
      return data || [];
    },
  });

  // Fetch recent generations
  const { data: recentGenerations = [], isLoading: generationsLoading } = useQuery({
    queryKey: ['recent-generations'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('document_generations')
        .select(`
          *,
          template:document_templates(name, template_type)
        `)
        .order('generated_at', { ascending: false })
        .limit(10);
      
      if (error) {
        console.error('Error fetching generations:', error);
        return [];
      }
      
      return data || [];
    },
  });

  // Generate document mutation
  const generateDocumentMutation = useMutation({
    mutationFn: async (config: GenerationConfig) => {
      if (!userProfile?.id) throw new Error('User not authenticated');

      setIsGenerating(true);
      setGenerationProgress(10);

      // Create generation record
      const { data: generation, error: genError } = await supabase
        .from('document_generations')
        .insert([{
          template_id: config.template_id,
          document_name: config.document_name,
          document_type: config.output_format,
          generation_type: config.ai_enhancement ? 'ai_enhanced' : 'manual',
          status: 'processing',
          generation_config: config,
          generated_by: userProfile.id
        }])
        .select()
        .single();

      if (genError) throw genError;

      setGenerationProgress(30);

      // Call AI document generation function
      const { data: result, error: aiError } = await supabase.functions.invoke('generate-ai-document', {
        body: {
          generation_id: generation.id,
          config: config,
          user_id: userProfile.id
        }
      });

      if (aiError) throw aiError;

      setGenerationProgress(100);
      return result;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['recent-generations'] });
      setIsGenerateOpen(false);
      setIsGenerating(false);
      setGenerationProgress(0);
      
      toast({
        title: "Document Generated Successfully",
        description: `Your ${generationConfig.output_format} document has been generated with AI enhancements.`,
      });
    },
    onError: (error: any) => {
      setIsGenerating(false);
      setGenerationProgress(0);
      
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate document. Please try again.",
        variant: "destructive",
      });
    }
  });

  const handleGenerate = () => {
    if (!generationConfig.template_id || !generationConfig.document_name) {
      toast({
        title: "Validation Error",
        description: "Please select a template and enter a document name.",
        variant: "destructive",
      });
      return;
    }

    generateDocumentMutation.mutate(generationConfig);
  };

  const downloadDocument = async (generation: DocumentGeneration) => {
    try {
      if (!generation.file_path) {
        toast({
          title: "Download Error",
          description: "File not available for download.",
          variant: "destructive",
        });
        return;
      }

      // Get download URL from Supabase Storage
      const { data } = supabase.storage
        .from('generated-documents')
        .getPublicUrl(generation.file_path);

      if (data?.publicUrl) {
        // Create download link
        const link = document.createElement('a');
        link.href = data.publicUrl;
        link.download = generation.document_name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Update download count
        await supabase
          .from('document_generations')
          .update({ download_count: generation.download_count + 1 })
          .eq('id', generation.id);

        // Log analytics
        await supabase
          .from('document_analytics')
          .insert({
            document_generation_id: generation.id,
            event_type: 'downloaded',
            user_id: userProfile?.id
          });

        toast({
          title: "Download Started",
          description: "Your document is being downloaded.",
        });
      }
    } catch (error: any) {
      toast({
        title: "Download Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'excel': return <FileSpreadsheet className="h-4 w-4" />;
      case 'pdf': return <FileText className="h-4 w-4" />;
      case 'dashboard': return <BarChart3 className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6 text-purple-600" />
            AI Document Generator
          </h2>
          <p className="text-muted-foreground">Generate intelligent documents with AI-powered insights</p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isCreateTemplateOpen} onOpenChange={setIsCreateTemplateOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create Template
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl">
              <DialogHeader>
                <DialogTitle>Create Document Template</DialogTitle>
              </DialogHeader>
              {/* Template creation form will be added here */}
              <div className="p-4 text-center text-muted-foreground">
                Template creation form coming soon...
              </div>
            </DialogContent>
          </Dialog>
          
          <Dialog open={isGenerateOpen} onOpenChange={setIsGenerateOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Generate Document
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Generate AI Document</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="template">Select Template</Label>
                  <Select 
                    value={generationConfig.template_id} 
                    onValueChange={(value) => setGenerationConfig(prev => ({ ...prev, template_id: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          <div className="flex items-center gap-2">
                            {getTypeIcon(template.template_type)}
                            {template.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="document_name">Document Name</Label>
                  <Input
                    id="document_name"
                    value={generationConfig.document_name}
                    onChange={(e) => setGenerationConfig(prev => ({ ...prev, document_name: e.target.value }))}
                    placeholder="Enter document name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="output_format">Output Format</Label>
                  <Select 
                    value={generationConfig.output_format} 
                    onValueChange={(value) => setGenerationConfig(prev => ({ ...prev, output_format: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excel">Excel with Charts</SelectItem>
                      <SelectItem value="pdf">PDF Report</SelectItem>
                      <SelectItem value="dashboard">Interactive Dashboard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="ai_enhancement"
                    checked={generationConfig.ai_enhancement}
                    onChange={(e) => setGenerationConfig(prev => ({ ...prev, ai_enhancement: e.target.checked }))}
                    className="rounded"
                  />
                  <Label htmlFor="ai_enhancement" className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-purple-500" />
                    Enable AI Enhancement
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="include_analysis"
                    checked={generationConfig.include_analysis}
                    onChange={(e) => setGenerationConfig(prev => ({ ...prev, include_analysis: e.target.checked }))}
                    className="rounded"
                  />
                  <Label htmlFor="include_analysis" className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-blue-500" />
                    Include Data Analysis
                  </Label>
                </div>

                {isGenerating && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      <span className="text-sm">Generating document...</span>
                    </div>
                    <Progress value={generationProgress} className="w-full" />
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsGenerateOpen(false)}
                    disabled={isGenerating}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleGenerate}
                    disabled={isGenerating}
                    className="flex items-center gap-2"
                  >
                    {isGenerating ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4" />
                        Generate
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">Templates</p>
                <p className="text-2xl font-bold">{templates.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Generated</p>
                <p className="text-2xl font-bold">{recentGenerations.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-muted-foreground">AI Enhanced</p>
                <p className="text-2xl font-bold">
                  {recentGenerations.filter(g => g.generation_type === 'ai_enhanced').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Download className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm text-muted-foreground">Downloads</p>
                <p className="text-2xl font-bold">
                  {recentGenerations.reduce((sum, g) => sum + (g.download_count || 0), 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Templates Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Templates
          </CardTitle>
        </CardHeader>
        <CardContent>
          {templatesLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Loading templates...</p>
            </div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-semibold mb-2">No Templates Found</h3>
              <p className="text-muted-foreground mb-4">Create your first template to get started.</p>
              <Button onClick={() => setIsCreateTemplateOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <Card key={template.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(template.template_type)}
                        <h4 className="font-semibold">{template.name}</h4>
                      </div>
                      <Badge variant="outline">{template.category}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                      {template.description}
                    </p>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={() => {
                          setGenerationConfig(prev => ({ ...prev, template_id: template.id }));
                          setIsGenerateOpen(true);
                        }}
                        className="flex-1"
                      >
                        <Zap className="h-3 w-3 mr-1" />
                        Generate
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Generations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Generations
          </CardTitle>
        </CardHeader>
        <CardContent>
          {generationsLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Loading generations...</p>
            </div>
          ) : recentGenerations.length === 0 ? (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-semibold mb-2">No Documents Generated</h3>
              <p className="text-muted-foreground mb-4">Generate your first document to see it here.</p>
              <Button onClick={() => setIsGenerateOpen(true)}>
                <Sparkles className="h-4 w-4 mr-2" />
                Generate Document
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {recentGenerations.map((generation) => (
                <div key={generation.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getTypeIcon(generation.document_type)}
                    <div>
                      <h4 className="font-medium">{generation.document_name}</h4>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{generation.template?.name}</span>
                        <span>•</span>
                        <span>{new Date(generation.generated_at).toLocaleDateString()}</span>
                        {generation.processing_time && (
                          <>
                            <span>•</span>
                            <span>{generation.processing_time}ms</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(generation.status)}>
                      {generation.status}
                    </Badge>
                    {generation.status === 'completed' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => downloadDocument(generation)}
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
