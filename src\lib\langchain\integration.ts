/**
 * LangChain Integration Service
 * Integrates LangChain capabilities with existing AI components
 */

import { langChainConfig } from './config';
import { ragSystem } from './rag-system';
import { aiWorkboardAgent } from './agents';
import { memoryManager } from './memory';
import { summarization<PERSON>hain, reportAnalysis<PERSON>hain, qa<PERSON>hain } from './chains';
import { documentProcessor } from './document-processor';

export interface AIRequest {
  message: string;
  userId?: string;
  sessionId?: string;
  context?: {
    interface?: 'hacker_terminal' | 'futuristic' | 'standard' | 'enhanced';
    role?: string;
    department?: string;
    modules?: string[];
    previousMessages?: any[];
    actions?: any[];
  };
  options?: {
    useRAG?: boolean;
    useMemory?: boolean;
    useAgent?: boolean;
    maxTokens?: number;
    temperature?: number;
  };
}

export interface AIResponse {
  response: string;
  confidence?: number;
  sources?: any[];
  actions?: any[];
  metadata?: {
    processingTime: number;
    model: string;
    tokensUsed?: number;
    memoryUsed?: boolean;
    ragUsed?: boolean;
    agentUsed?: boolean;
  };
}

export interface StreamingAIResponse {
  stream: AsyncGenerator<string, void, unknown>;
  metadata: {
    sessionId: string;
    startTime: number;
  };
}

/**
 * Main LangChain Integration Service
 */
export class LangChainIntegrationService {
  private static instance: LangChainIntegrationService;
  private initialized = false;

  private constructor() {}

  public static getInstance(): LangChainIntegrationService {
    if (!LangChainIntegrationService.instance) {
      LangChainIntegrationService.instance = new LangChainIntegrationService();
    }
    return LangChainIntegrationService.instance;
  }

  /**
   * Initialize all LangChain components
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('Initializing LangChain Integration Service...');
      
      // Validate configuration
      const validation = langChainConfig.validateConfig();
      if (!validation.isValid) {
        console.warn('LangChain configuration issues:', validation.errors);
      }

      // Initialize RAG system
      await ragSystem.initialize();
      
      // Initialize AI agent
      await aiWorkboardAgent.initialize();
      
      this.initialized = true;
      console.log('LangChain Integration Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize LangChain Integration Service:', error);
      throw error;
    }
  }

  /**
   * Process AI request with LangChain capabilities
   */
  public async processRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();
    
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const {
        message,
        userId = 'anonymous',
        sessionId = `session_${Date.now()}`,
        context = {},
        options = {}
      } = request;

      let response = '';
      let confidence = 0;
      let sources: any[] = [];
      let actions: any[] = [];
      let ragUsed = false;
      let memoryUsed = false;
      let agentUsed = false;

      // Determine processing strategy based on interface and options
      const strategy = this.determineProcessingStrategy(context, options);

      switch (strategy) {
        case 'agent':
          response = await aiWorkboardAgent.execute(message);
          agentUsed = true;
          break;

        case 'rag':
          const ragResponse = await ragSystem.query({
            question: message,
            context: this.buildContextString(context),
            maxResults: 5,
          });
          response = ragResponse.answer;
          confidence = ragResponse.confidence;
          sources = ragResponse.sources;
          ragUsed = true;
          break;

        case 'qa':
          const qaResponse = await qaChain.call({
            context: this.buildContextString(context),
            question: message,
            userRole: context.role || 'user',
          });
          response = qaResponse.answer;
          break;

        case 'memory_enhanced':
          // Use memory-enhanced conversation
          if (userId && sessionId) {
            const conversationContext = { userId, sessionId, metadata: context };
            const memoryContext = await memoryManager.getConversationContext(conversationContext);
            
            const enhancedMessage = memoryContext 
              ? `Context: ${memoryContext}\n\nCurrent message: ${message}`
              : message;

            const model = langChainConfig.getDefaultModel();
            const modelResponse = await model.invoke([{ role: 'user', content: enhancedMessage }]);
            response = modelResponse.content as string;

            // Save conversation turn
            await memoryManager.saveConversationTurn(conversationContext, message, response);
            memoryUsed = true;
          }
          break;

        default:
          // Standard processing
          const model = langChainConfig.getDefaultModel();
          const modelResponse = await model.invoke([{ role: 'user', content: message }]);
          response = modelResponse.content as string;
      }

      const processingTime = Date.now() - startTime;

      return {
        response,
        confidence,
        sources,
        actions,
        metadata: {
          processingTime,
          model: langChainConfig.getDefaultModel().constructor.name,
          memoryUsed,
          ragUsed,
          agentUsed,
        },
      };
    } catch (error) {
      console.error('Error processing AI request:', error);
      throw new Error(`AI processing failed: ${error}`);
    }
  }

  /**
   * Stream AI response
   */
  public async streamRequest(request: AIRequest): Promise<StreamingAIResponse> {
    const startTime = Date.now();
    const sessionId = request.sessionId || `stream_${Date.now()}`;

    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const strategy = this.determineProcessingStrategy(request.context || {}, request.options || {});
      
      let stream: AsyncGenerator<string, void, unknown>;

      switch (strategy) {
        case 'agent':
          stream = aiWorkboardAgent.streamExecute(request.message);
          break;

        case 'rag':
          stream = ragSystem.streamQuery({
            question: request.message,
            context: this.buildContextString(request.context || {}),
          });
          break;

        default:
          // Standard streaming
          stream = this.createStandardStream(request.message);
      }

      return {
        stream,
        metadata: {
          sessionId,
          startTime,
        },
      };
    } catch (error) {
      console.error('Error streaming AI request:', error);
      throw new Error(`AI streaming failed: ${error}`);
    }
  }

  /**
   * Process document with LangChain
   */
  public async processDocument(filePath: string, options: any = {}): Promise<any> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const processedDoc = await documentProcessor.processDocument(filePath, options);
      
      // Add to RAG system if requested
      if (options.addToRAG !== false) {
        await ragSystem.addDocuments(processedDoc.chunks);
      }

      return processedDoc;
    } catch (error) {
      console.error('Error processing document:', error);
      throw error;
    }
  }

  /**
   * Generate report using LangChain
   */
  public async generateReport(reportType: string, data: any, timePeriod: string): Promise<string> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const result = await reportAnalysisChain.call({
        reportType,
        data: JSON.stringify(data),
        timePeriod,
      });

      return `${result.analysis}\n\n${result.insights}`;
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }

  /**
   * Summarize document
   */
  public async summarizeDocument(title: string, content: string): Promise<string> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const result = await summarizationChain.call({ title, content });
      return result.summary;
    } catch (error) {
      console.error('Error summarizing document:', error);
      throw error;
    }
  }

  /**
   * Determine processing strategy based on context and options
   */
  private determineProcessingStrategy(context: any, options: any): string {
    // Force specific strategy if requested
    if (options.useAgent) return 'agent';
    if (options.useRAG) return 'rag';

    // Determine based on interface type
    if (context.interface === 'hacker_terminal') {
      return 'agent'; // Use agent for terminal commands
    }

    if (context.interface === 'futuristic' && context.actions?.length > 0) {
      return 'agent'; // Use agent for action-based interfaces
    }

    // Use RAG for document-related queries
    const message = context.message || '';
    if (message.toLowerCase().includes('document') || 
        message.toLowerCase().includes('report') ||
        message.toLowerCase().includes('analyze')) {
      return 'rag';
    }

    // Use memory-enhanced for conversational interfaces
    if (context.previousMessages?.length > 0) {
      return 'memory_enhanced';
    }

    // Default to QA chain
    return 'qa';
  }

  /**
   * Build context string from context object
   */
  private buildContextString(context: any): string {
    const parts: string[] = [];

    if (context.role) parts.push(`User Role: ${context.role}`);
    if (context.department) parts.push(`Department: ${context.department}`);
    if (context.interface) parts.push(`Interface: ${context.interface}`);
    if (context.modules) parts.push(`Available Modules: ${context.modules.join(', ')}`);
    
    if (context.previousMessages?.length > 0) {
      const recentMessages = context.previousMessages.slice(-3);
      parts.push(`Recent conversation: ${JSON.stringify(recentMessages)}`);
    }

    return parts.join('\n');
  }

  /**
   * Create standard streaming response
   */
  private async *createStandardStream(message: string): AsyncGenerator<string, void, unknown> {
    const model = langChainConfig.getDefaultModel();
    const response = await model.stream([{ role: 'user', content: message }]);
    
    for await (const chunk of response) {
      yield chunk.content as string;
    }
  }
}

// Export singleton instance
export const langChainIntegration = LangChainIntegrationService.getInstance();
