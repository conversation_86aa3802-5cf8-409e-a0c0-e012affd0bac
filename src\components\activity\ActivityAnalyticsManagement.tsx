
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Activity, TrendingUp, Users, Clock, BarChart3, Download } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { useState } from "react";

interface ProjectAnalytics {
  total_projects: number;
  active_projects: number;
  completed_projects: number;
  overdue_projects: number;
  total_budget: number;
  total_spent: number;
  avg_health_score: number;
  high_risk_projects: number;
}

export const ActivityAnalyticsManagement = () => {
  const [timeRange, setTimeRange] = useState("7");

  const { data: activityLogs, isLoading } = useQuery({
    queryKey: ['activity-logs', timeRange],
    queryFn: async () => {
      const daysAgo = new Date(Date.now() - parseInt(timeRange) * 24 * 60 * 60 * 1000);

      const { data, error } = await supabase
        .from('system_activities')
        .select(`
          *,
          profiles:user_id (
            full_name,
            department:department_id (
              name
            )
          )
        `)
        .gte('created_at', daysAgo.toISOString())
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;
      return data;
    },
  });

  const { data: userWorkload } = useQuery({
    queryKey: ['user-workload-analytics'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_user_workload_analytics');
      if (error) throw error;
      return data;
    },
  });

  const { data: projectAnalytics } = useQuery({
    queryKey: ['project-analytics'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_project_analytics');
      if (error) throw error;
      return data?.[0] as ProjectAnalytics || {
        total_projects: 0,
        active_projects: 0,
        completed_projects: 0,
        overdue_projects: 0,
        total_budget: 0,
        total_spent: 0,
        avg_health_score: 0,
        high_risk_projects: 0
      };
    },
  });

  const getActionColor = (action: string) => {
    switch (action?.toLowerCase()) {
      case 'create':
      case 'clock_in':
        return 'bg-green-500/20 text-green-500';
      case 'update':
      case 'edit':
        return 'bg-blue-500/20 text-blue-500';
      case 'delete':
      case 'clock_out':
        return 'bg-red-500/20 text-red-500';
      case 'view':
      case 'access':
        return 'bg-purple-500/20 text-purple-500';
      default:
        return 'bg-gray-500/20 text-gray-500';
    }
  };

  const activityByType = activityLogs?.reduce((acc: Record<string, number>, log) => {
    acc[log.action] = (acc[log.action] || 0) + 1;
    return acc;
  }, {}) || {};

  const topActivities = Object.entries(activityByType)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Activity & Analytics</h2>
          <p className="text-muted-foreground">Monitor system activity and performance metrics</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">Last Day</SelectItem>
              <SelectItem value="7">Last Week</SelectItem>
              <SelectItem value="30">Last Month</SelectItem>
              <SelectItem value="90">Last Quarter</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <Activity className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Activities</p>
              <p className="text-2xl font-bold">{activityLogs?.length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Users className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Active Users</p>
              <p className="text-2xl font-bold">
                {new Set(activityLogs?.map(log => log.user_id).filter(Boolean)).size || 0}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <BarChart3 className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Projects</p>
              <p className="text-2xl font-bold">{projectAnalytics?.total_projects || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <TrendingUp className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Avg Health Score</p>
              <p className="text-2xl font-bold">
                {projectAnalytics?.avg_health_score ? Math.round(projectAnalytics.avg_health_score) : 0}%
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="activity" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
          <TabsTrigger value="workload">User Workload</TabsTrigger>
          <TabsTrigger value="projects">Project Analytics</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="activity" className="space-y-4">
          <div className="grid gap-6 lg:grid-cols-3">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Recent System Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {isLoading ? (
                      <div className="text-center py-8">Loading activity logs...</div>
                    ) : activityLogs?.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        No activity found for the selected period
                      </div>
                    ) : (
                      activityLogs?.slice(0, 20).map((log) => (
                        <div key={log.id} className="flex justify-between items-start p-3 border rounded-lg">
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Badge className={getActionColor(log.action)}>
                                {log.action}
                              </Badge>
                              <span className="font-medium">
                                {log.profiles?.full_name || 'System'}
                              </span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {log.description || `${log.action} ${log.entity_type || 'item'}`}
                            </p>
                            {log.entity_type && (
                              <p className="text-xs text-muted-foreground">
                                Entity: {log.entity_type}
                              </p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-muted-foreground">
                              {new Date(log.created_at).toLocaleString()}
                            </p>
                            {log.profiles?.department?.name && (
                              <p className="text-xs text-muted-foreground">
                                {log.profiles.department.name}
                              </p>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Top Activities</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {topActivities.map(([action, count]) => (
                      <div key={action} className="flex justify-between items-center">
                        <Badge className={getActionColor(action)}>
                          {action}
                        </Badge>
                        <span className="font-bold">{count}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="workload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Workload Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userWorkload?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No workload data available
                  </div>
                ) : (
                  userWorkload?.map((user) => (
                    <Card key={user.user_id} className="p-4">
                      <div className="space-y-3">
                        <div className="flex justify-between items-start">
                          <h3 className="font-semibold">{user.full_name}</h3>
                          <Badge 
                            className={user.completion_rate >= 80 ? 'bg-green-500/20 text-green-500' : 
                                     user.completion_rate >= 60 ? 'bg-yellow-500/20 text-yellow-500' : 
                                     'bg-red-500/20 text-red-500'}
                          >
                            {user.completion_rate}% Complete
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Active Tasks</p>
                            <p className="font-bold">{user.active_tasks_count}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Est. Hours</p>
                            <p className="font-bold">{user.total_estimated_hours}h</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Overdue</p>
                            <p className="font-bold text-red-600">{user.overdue_tasks_count}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Completion Rate</p>
                            <p className="font-bold">{user.completion_rate}%</p>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projects" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Project Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Projects</span>
                    <span className="font-bold">{projectAnalytics?.total_projects || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Projects</span>
                    <span className="font-bold text-green-600">{projectAnalytics?.active_projects || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Completed Projects</span>
                    <span className="font-bold text-blue-600">{projectAnalytics?.completed_projects || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Overdue Projects</span>
                    <span className="font-bold text-red-600">{projectAnalytics?.overdue_projects || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>High Risk Projects</span>
                    <span className="font-bold text-orange-600">{projectAnalytics?.high_risk_projects || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Financial Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Budget</span>
                    <span className="font-bold">₦{(projectAnalytics?.total_budget || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Spent</span>
                    <span className="font-bold">₦{(projectAnalytics?.total_spent || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Budget Utilization</span>
                    <span className="font-bold">
                      {(projectAnalytics?.total_budget || 0) > 0 
                        ? Math.round(((projectAnalytics?.total_spent || 0) / projectAnalytics.total_budget) * 100)
                        : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg Health Score</span>
                    <span className="font-bold">
                      {projectAnalytics?.avg_health_score ? Math.round(projectAnalytics.avg_health_score) : 0}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 border-l-4 border-green-500 bg-green-50">
                    <p className="font-medium text-green-800">High Activity Period</p>
                    <p className="text-sm text-green-600">
                      Peak activity hours: 9AM - 11AM
                    </p>
                  </div>
                  <div className="p-3 border-l-4 border-blue-500 bg-blue-50">
                    <p className="font-medium text-blue-800">User Engagement</p>
                    <p className="text-sm text-blue-600">
                      {new Set(activityLogs?.map(log => log.user_id).filter(Boolean)).size} active users in selected period
                    </p>
                  </div>
                  <div className="p-3 border-l-4 border-yellow-500 bg-yellow-50">
                    <p className="font-medium text-yellow-800">Attention Needed</p>
                    <p className="text-sm text-yellow-600">
                      {projectAnalytics?.overdue_projects || 0} projects are overdue
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-gray-50 rounded">
                    <p className="text-sm">
                      • Consider redistributing workload for users with completion rates below 60%
                    </p>
                  </div>
                  <div className="p-3 bg-gray-50 rounded">
                    <p className="text-sm">
                      • Review high-risk projects to prevent further delays
                    </p>
                  </div>
                  <div className="p-3 bg-gray-50 rounded">
                    <p className="text-sm">
                      • Peak activity hours suggest optimal meeting times
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
