/**
 * <PERSON><PERSON><PERSON><PERSON> RAG (Retrieval-Augmented Generation) System
 * Implements comprehensive RAG capabilities with context-aware responses
 */

import { Document } from '@langchain/core/documents';
import { BaseRetriever } from '@langchain/core/retrievers';
import { VectorStoreRetriever } from '@langchain/core/vectorstores';
import { PromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence, RunnablePassthrough } from '@langchain/core/runnables';
import { StringOutputParser } from '@langchain/core/output_parsers';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { langChainConfig } from './config';
import { documentProcessor } from './document-processor';

export interface RAGQuery {
  question: string;
  context?: string;
  filters?: Record<string, any>;
  maxResults?: number;
  scoreThreshold?: number;
}

export interface RAGResponse {
  answer: string;
  sources: Document[];
  confidence: number;
  metadata: {
    retrievedDocs: number;
    processingTime: number;
    model: string;
  };
}

export interface RAGSystemOptions {
  model?: BaseChatModel;
  retriever?: BaseRetriever;
  promptTemplate?: PromptTemplate;
  topK?: number;
  scoreThreshold?: number;
}

export class LangChainRAGSystem {
  private model: BaseChatModel;
  private retriever: BaseRetriever | null = null;
  private promptTemplate: PromptTemplate;
  private ragChain: RunnableSequence | null = null;

  constructor(options: RAGSystemOptions = {}) {
    this.model = options.model || langChainConfig.getDefaultModel();
    this.retriever = options.retriever || null;
    this.promptTemplate = options.promptTemplate || this.createDefaultPrompt();
  }

  /**
   * Initialize the RAG system
   */
  public async initialize(): Promise<void> {
    try {
      // Initialize vector store if not provided
      if (!this.retriever) {
        const vectorStore = await documentProcessor.initializeVectorStore();
        const config = langChainConfig.getConfig();
        
        this.retriever = new VectorStoreRetriever({
          vectorStore,
          k: config.rag.topK,
          searchType: 'similarity',
        });
      }

      // Create the RAG chain
      this.ragChain = RunnableSequence.from([
        {
          context: this.retriever.pipe(this.formatDocuments),
          question: new RunnablePassthrough(),
        },
        this.promptTemplate,
        this.model,
        new StringOutputParser(),
      ]);

      console.log('RAG system initialized successfully');
    } catch (error) {
      throw new Error(`Failed to initialize RAG system: ${error}`);
    }
  }

  /**
   * Query the RAG system
   */
  public async query(query: RAGQuery): Promise<RAGResponse> {
    const startTime = Date.now();

    try {
      if (!this.ragChain) {
        await this.initialize();
      }

      if (!this.ragChain) {
        throw new Error('RAG chain not initialized');
      }

      // Retrieve relevant documents
      const retrievedDocs = await this.retrieveDocuments(query);
      
      // Generate response
      const answer = await this.ragChain.invoke(query.question);
      
      // Calculate confidence based on retrieval scores
      const confidence = this.calculateConfidence(retrievedDocs);
      
      const processingTime = Date.now() - startTime;

      return {
        answer,
        sources: retrievedDocs,
        confidence,
        metadata: {
          retrievedDocs: retrievedDocs.length,
          processingTime,
          model: this.model.constructor.name,
        },
      };
    } catch (error) {
      throw new Error(`RAG query failed: ${error}`);
    }
  }

  /**
   * Stream RAG response
   */
  public async *streamQuery(query: RAGQuery): AsyncGenerator<string, void, unknown> {
    try {
      if (!this.ragChain) {
        await this.initialize();
      }

      if (!this.ragChain) {
        throw new Error('RAG chain not initialized');
      }

      const stream = await this.ragChain.stream(query.question);
      
      for await (const chunk of stream) {
        yield chunk;
      }
    } catch (error) {
      throw new Error(`RAG streaming failed: ${error}`);
    }
  }

  /**
   * Add documents to the knowledge base
   */
  public async addDocuments(documents: Document[]): Promise<void> {
    try {
      await documentProcessor.addDocumentsToVectorStore(documents);
      console.log(`Added ${documents.length} documents to knowledge base`);
    } catch (error) {
      throw new Error(`Failed to add documents: ${error}`);
    }
  }

  /**
   * Add document from file
   */
  public async addDocumentFromFile(filePath: string): Promise<void> {
    try {
      const processedDoc = await documentProcessor.processDocument(filePath);
      await this.addDocuments(processedDoc.chunks);
      console.log(`Added document from ${filePath} to knowledge base`);
    } catch (error) {
      throw new Error(`Failed to add document from file: ${error}`);
    }
  }

  /**
   * Search knowledge base
   */
  public async searchKnowledgeBase(
    query: string, 
    k: number = 5
  ): Promise<Document[]> {
    try {
      return await documentProcessor.searchSimilarDocuments(query, k);
    } catch (error) {
      throw new Error(`Knowledge base search failed: ${error}`);
    }
  }

  /**
   * Get retriever instance
   */
  public getRetriever(): BaseRetriever | null {
    return this.retriever;
  }

  /**
   * Update prompt template
   */
  public updatePromptTemplate(template: PromptTemplate): void {
    this.promptTemplate = template;
    this.ragChain = null; // Force re-initialization
  }

  /**
   * Create default prompt template
   */
  private createDefaultPrompt(): PromptTemplate {
    const template = `You are an AI assistant for the CTNL AI Workboard system. Use the following context to answer the question accurately and helpfully.

Context:
{context}

Question: {question}

Instructions:
- Provide accurate, helpful answers based on the context
- If the context doesn't contain enough information, say so clearly
- Include relevant details from the context
- Be concise but comprehensive
- If appropriate, suggest follow-up actions or related information

Answer:`;

    return PromptTemplate.fromTemplate(template);
  }

  /**
   * Retrieve documents for query
   */
  private async retrieveDocuments(query: RAGQuery): Promise<Document[]> {
    if (!this.retriever) {
      throw new Error('Retriever not initialized');
    }

    const config = langChainConfig.getConfig();
    const k = query.maxResults || config.rag.topK;
    
    return await this.retriever.getRelevantDocuments(query.question);
  }

  /**
   * Format documents for context
   */
  private formatDocuments = (docs: Document[]): string => {
    return docs
      .map((doc, index) => {
        const source = doc.metadata.source || `Document ${index + 1}`;
        return `Source: ${source}\nContent: ${doc.pageContent}\n`;
      })
      .join('\n---\n');
  };

  /**
   * Calculate confidence score
   */
  private calculateConfidence(documents: Document[]): number {
    if (documents.length === 0) return 0;
    
    // Simple confidence calculation based on number of retrieved documents
    // In a real implementation, you'd use actual similarity scores
    const maxDocs = langChainConfig.getConfig().rag.topK;
    const ratio = documents.length / maxDocs;
    
    return Math.min(ratio * 0.8 + 0.2, 1.0); // Scale between 0.2 and 1.0
  }
}

// Export singleton instance
export const ragSystem = new LangChainRAGSystem();
