import { ThemeProvider } from '@/components/theme-provider'
import '@/utils/cacheFixer'
import { cacheManager } from '@/utils/cacheManager'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
// @ts-ignore - AOS types not available
import AOS from 'aos'
import 'aos/dist/aos.css'

// Initialize AOS with custom settings
AOS.init({
  duration: 800,
  easing: 'ease-in-out',
  once: true,
  mirror: false,
  anchorPlacement: 'top-bottom',
  offset: 50,
});

// Create a client with optimized performance settings and aggressive cache clearing
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1 * 60 * 1000, // 1 minute for faster cache invalidation
      gcTime: 2 * 60 * 1000, // 2 minutes cache retention for faster clearing (renamed from cacheTime in v5)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 2 times for other errors
        return failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false, // Disable refetch on window focus for better performance
      refetchOnReconnect: true, // Refetch when connection is restored
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
})

// Initialize cache manager with query client
cacheManager.setQueryClient(queryClient);

// Clear cache on page load/refresh to handle server restarts
const clearCacheOnLoad = () => {
  // Check if cache should be cleared (30 minutes threshold)
  if (cacheManager.shouldClearCache(30)) {
    if (process.env.NODE_ENV === 'development') {
      console.log('🧹 Clearing React Query cache due to server restart or time threshold');
    }
    cacheManager.clearAll();
  }

  // Handle server restart detection
  cacheManager.handleServerRestart();
};

// Clear cache immediately on load
clearCacheOnLoad();

// Start periodic cache cleanup (every hour)
cacheManager.startPeriodicCleanup(60);



// Make cache clearing available globally for console access
if (typeof window !== 'undefined') {
  (window as any).clearCache = () => {
    console.log('🧹 Clearing cache from console...');
    cacheManager.clearAll();
    localStorage.removeItem('lastCacheClear');
    localStorage.removeItem('serverStartTime');
    console.log('✅ Cache cleared! Reload the page for best results.');
    return 'Cache cleared successfully!';
  };

  (window as any).clearDashboardCache = () => {
    console.log('🧹 Clearing dashboard cache from console...');
    cacheManager.clearDashboardCache();
    console.log('✅ Dashboard cache cleared!');
    return 'Dashboard cache cleared successfully!';
  };

  (window as any).clearAuthCache = () => {
    console.log('🧹 Clearing authentication cache from console...');
    // Clear localStorage auth data
    localStorage.removeItem('userRole');
    localStorage.removeItem('accountType');
    localStorage.removeItem('supabase.auth.token');
    // Clear sessionStorage
    sessionStorage.clear();
    // Clear all Supabase keys from localStorage
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('supabase.auth.')) {
        localStorage.removeItem(key);
      }
    });
    console.log('✅ Auth cache cleared! Please reload the page.');
    return 'Auth cache cleared successfully!';
  };
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <ThemeProvider defaultTheme="dark" storageKey="ctnl-theme">
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </ThemeProvider>
);
