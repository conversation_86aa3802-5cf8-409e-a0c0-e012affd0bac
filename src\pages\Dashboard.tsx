import React from "react";
import { useAuth } from "@/components/auth/AuthProvider";
import { UnifiedDashboard } from "@/components/dashboard/UnifiedDashboard";
import EnhancedStaffDashboard from "@/components/staff/EnhancedStaffDashboard";
import { ManagerDashboard } from "@/components/manager/ManagerDashboard";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { DashboardSkeleton } from "@/components/dashboard/DashboardSkeleton";
import { ErrorState } from "@/components/dashboard/ErrorState";
import { RoleBasedWelcome } from "@/components/dashboard/RoleBasedWelcome";
import { useNavigate } from "react-router-dom";

const Dashboard = () => {
  const { userProfile, loading, initialized } = useAuth();
  const navigate = useNavigate();

  // Show loading state while checking authentication
  if (loading || !initialized) {
    return <DashboardSkeleton />;
  }

  // Show error if not authenticated
  if (!userProfile) {
    return (
      <ErrorState 
        error={new Error("Authentication required")} 
        onRetry={() => navigate('/auth')} 
      />
    );
  }

  // Determine which dashboard to render based on user role
  const renderDashboard = () => {
    if (userProfile.role === 'staff') {
      return <EnhancedStaffDashboard />;
    }
    if (userProfile.role === 'manager') {
      return <ManagerDashboard />;
    }
    return <UnifiedDashboard />;
  };

  // Main dashboard content - layout handled by DashboardLayout wrapper
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900/20">
        {/* Role-based welcome header */}
        <div className="mb-8">
          <RoleBasedWelcome />
        </div>

        {/* Role-specific dashboard content */}
        <div className="px-4 md:px-6 lg:px-8">
          <ErrorBoundary 
            fallback={
              <ErrorState 
                error={new Error("Dashboard failed to load")} 
                onRetry={() => navigate('/auth')} 
              />
            }
          >
            {renderDashboard()}
          </ErrorBoundary>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default Dashboard;
