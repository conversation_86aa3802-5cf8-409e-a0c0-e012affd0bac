/**
 * Lang<PERSON>hain Memory Management
 * Handles conversation memory, entity memory, and knowledge graph memory
 */

import { BaseMemory } from '@langchain/core/memory';
import { BufferMemory } from 'langchain/memory';
import { ConversationSummaryMemory } from 'langchain/memory';
import { EntityMemory } from 'langchain/memory';
import { ChatMessageHistory } from '@langchain/core/chat_history';
import { AIMessage, HumanMessage, BaseMessage } from '@langchain/core/messages';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { langChainConfig } from './config';
import { supabase } from '../supabase';

export interface ConversationContext {
  userId: string;
  sessionId: string;
  metadata?: Record<string, any>;
}

export interface MemoryEntry {
  id: string;
  userId: string;
  sessionId: string;
  type: 'human' | 'ai';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Persistent Chat Message History
 */
export class PersistentChatMessageHistory extends ChatMessageHistory {
  private userId: string;
  private sessionId: string;

  constructor(userId: string, sessionId: string) {
    super();
    this.userId = userId;
    this.sessionId = sessionId;
  }

  async getMessages(): Promise<BaseMessage[]> {
    try {
      const { data, error } = await supabase
        .from('conversation_history')
        .select('*')
        .eq('user_id', this.userId)
        .eq('session_id', this.sessionId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      return data?.map(entry => {
        if (entry.type === 'human') {
          return new HumanMessage(entry.content);
        } else {
          return new AIMessage(entry.content);
        }
      }) || [];
    } catch (error) {
      console.error('Failed to load conversation history:', error);
      return [];
    }
  }

  async addMessage(message: BaseMessage): Promise<void> {
    try {
      const { error } = await supabase
        .from('conversation_history')
        .insert({
          user_id: this.userId,
          session_id: this.sessionId,
          type: message instanceof HumanMessage ? 'human' : 'ai',
          content: message.content as string,
          metadata: {},
        });

      if (error) throw error;
    } catch (error) {
      console.error('Failed to save message:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      const { error } = await supabase
        .from('conversation_history')
        .delete()
        .eq('user_id', this.userId)
        .eq('session_id', this.sessionId);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to clear conversation history:', error);
    }
  }
}

/**
 * Enhanced Memory Manager
 */
export class LangChainMemoryManager {
  private memories: Map<string, BaseMemory> = new Map();
  private model: BaseChatModel;

  constructor(model?: BaseChatModel) {
    this.model = model || langChainConfig.getDefaultModel();
  }

  /**
   * Get or create memory for a conversation
   */
  public async getMemory(context: ConversationContext): Promise<BaseMemory> {
    const memoryKey = `${context.userId}_${context.sessionId}`;
    
    if (this.memories.has(memoryKey)) {
      return this.memories.get(memoryKey)!;
    }

    const memory = await this.createMemory(context);
    this.memories.set(memoryKey, memory);
    return memory;
  }

  /**
   * Create memory based on configuration
   */
  private async createMemory(context: ConversationContext): Promise<BaseMemory> {
    const config = langChainConfig.getConfig().memory;
    const chatHistory = new PersistentChatMessageHistory(context.userId, context.sessionId);

    switch (config.type) {
      case 'buffer':
        return new BufferMemory({
          chatHistory,
          returnMessages: config.returnMessages,
          memoryKey: 'chat_history',
        });

      case 'summary':
        return new ConversationSummaryMemory({
          llm: this.model,
          chatHistory,
          returnMessages: config.returnMessages,
          memoryKey: 'chat_history',
        });

      case 'entity':
        return new EntityMemory({
          llm: this.model,
          chatHistory,
          returnMessages: config.returnMessages,
          memoryKey: 'chat_history',
        });

      default:
        return new BufferMemory({
          chatHistory,
          returnMessages: config.returnMessages,
          memoryKey: 'chat_history',
        });
    }
  }

  /**
   * Save conversation turn
   */
  public async saveConversationTurn(
    context: ConversationContext,
    humanMessage: string,
    aiMessage: string
  ): Promise<void> {
    try {
      const memory = await this.getMemory(context);
      
      await memory.chatHistory.addUserMessage(humanMessage);
      await memory.chatHistory.addAIMessage(aiMessage);

      // Also save to our custom table for analytics
      await this.saveToAnalytics(context, humanMessage, aiMessage);
    } catch (error) {
      console.error('Failed to save conversation turn:', error);
    }
  }

  /**
   * Get conversation context
   */
  public async getConversationContext(context: ConversationContext): Promise<string> {
    try {
      const memory = await this.getMemory(context);
      const variables = await memory.loadMemoryVariables({});
      
      return variables.chat_history || '';
    } catch (error) {
      console.error('Failed to get conversation context:', error);
      return '';
    }
  }

  /**
   * Clear conversation memory
   */
  public async clearMemory(context: ConversationContext): Promise<void> {
    try {
      const memoryKey = `${context.userId}_${context.sessionId}`;
      const memory = this.memories.get(memoryKey);
      
      if (memory) {
        await memory.clear();
        this.memories.delete(memoryKey);
      }

      // Also clear from database
      const chatHistory = new PersistentChatMessageHistory(context.userId, context.sessionId);
      await chatHistory.clear();
    } catch (error) {
      console.error('Failed to clear memory:', error);
    }
  }

  /**
   * Get conversation summary
   */
  public async getConversationSummary(context: ConversationContext): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('conversation_history')
        .select('content, type, created_at')
        .eq('user_id', context.userId)
        .eq('session_id', context.sessionId)
        .order('created_at', { ascending: true })
        .limit(20);

      if (error) throw error;

      if (!data || data.length === 0) {
        return 'No conversation history found.';
      }

      const messages = data.map(entry => 
        `${entry.type === 'human' ? 'User' : 'AI'}: ${entry.content}`
      ).join('\n');

      // Use the model to create a summary
      const summaryPrompt = `Please provide a concise summary of this conversation:

${messages}

Summary:`;

      const response = await this.model.invoke([new HumanMessage(summaryPrompt)]);
      return response.content as string;
    } catch (error) {
      console.error('Failed to get conversation summary:', error);
      return 'Failed to generate conversation summary.';
    }
  }

  /**
   * Get user conversation statistics
   */
  public async getUserConversationStats(userId: string): Promise<{
    totalConversations: number;
    totalMessages: number;
    averageMessagesPerConversation: number;
    lastActivity: Date | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('conversation_history')
        .select('session_id, created_at')
        .eq('user_id', userId);

      if (error) throw error;

      if (!data || data.length === 0) {
        return {
          totalConversations: 0,
          totalMessages: 0,
          averageMessagesPerConversation: 0,
          lastActivity: null,
        };
      }

      const uniqueSessions = new Set(data.map(entry => entry.session_id));
      const totalConversations = uniqueSessions.size;
      const totalMessages = data.length;
      const averageMessagesPerConversation = totalMessages / totalConversations;
      const lastActivity = new Date(Math.max(...data.map(entry => new Date(entry.created_at).getTime())));

      return {
        totalConversations,
        totalMessages,
        averageMessagesPerConversation,
        lastActivity,
      };
    } catch (error) {
      console.error('Failed to get user conversation stats:', error);
      return {
        totalConversations: 0,
        totalMessages: 0,
        averageMessagesPerConversation: 0,
        lastActivity: null,
      };
    }
  }

  /**
   * Save to analytics table
   */
  private async saveToAnalytics(
    context: ConversationContext,
    humanMessage: string,
    aiMessage: string
  ): Promise<void> {
    try {
      await supabase
        .from('conversation_analytics')
        .insert({
          user_id: context.userId,
          session_id: context.sessionId,
          human_message_length: humanMessage.length,
          ai_message_length: aiMessage.length,
          metadata: context.metadata || {},
        });
    } catch (error) {
      console.error('Failed to save conversation analytics:', error);
    }
  }
}

// Export singleton instance
export const memoryManager = new LangChainMemoryManager();
