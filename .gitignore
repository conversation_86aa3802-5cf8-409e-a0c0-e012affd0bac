# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Package manager lock files (keep only package-lock.json)
yarn.lock
pnpm-lock.yaml
bun.lockb

# Cache directories
.vite
.cache
node_modules/.cache

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Omni-tools (external git repository)
omni-tools/

# Supabase
.branches
.temp
.import

# Local Netlify folder
.netlify
