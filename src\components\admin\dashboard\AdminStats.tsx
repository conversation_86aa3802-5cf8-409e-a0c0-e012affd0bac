import { Card, CardContent } from "@/components/ui/card";
import { ArrowUpRight, Users, FileText, BarChart, Archive } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const AdminStats = () => {
  // Fetch real data from database
  const { data: userCount = 0 } = useQuery({
    queryKey: ['admin-stats-users'],
    queryFn: async () => {
      const { count, error } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });
      if (error) throw error;
      return count || 0;
    }
  });

  const { data: documentCount = 0 } = useQuery({
    queryKey: ['admin-stats-documents'],
    queryFn: async () => {
      const { count, error } = await supabase
        .from('document_archive')
        .select('*', { count: 'exact', head: true });
      if (error) throw error;
      return count || 0;
    }
  });

  const { data: activityCount = 0 } = useQuery({
    queryKey: ['admin-stats-activities'],
    queryFn: async () => {
      const { count, error } = await supabase
        .from('system_activities')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());
      if (error) throw error;
      return count || 0;
    }
  });

  const { data: projectCount = 0 } = useQuery({
    queryKey: ['admin-stats-projects'],
    queryFn: async () => {
      const { count, error } = await supabase
        .from('projects')
        .select('*', { count: 'exact', head: true });
      if (error) throw error;
      return count || 0;
    }
  });

  const stats = [
    {
      title: "Today's Activities",
      value: activityCount.toString(),
      change: "+14.5%",
      icon: BarChart,
    },
    {
      title: "Active Users",
      value: userCount.toString(),
      change: "+12.4%",
      icon: Users,
    },
    {
      title: "Documents",
      value: documentCount.toString(),
      change: "+8.2%",
      icon: FileText,
    },
    {
      title: "Projects",
      value: projectCount.toString(),
      change: "+6.5%",
      icon: Archive,
    }
  ];

  return (
    <>
      {stats.map((stat) => (
        <Card key={stat.title} className="modern-3d-card border-admin-accent bg-white/50 backdrop-blur-sm transition-all hover:border-admin-primary/50 dark:bg-black/50">
          <CardContent className="p-4 sm:p-6">
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">{stat.title}</p>
                <div className="flex items-baseline space-x-3">
                  <p className="text-xl sm:text-2xl font-bold text-admin-primary">{stat.value}</p>
                  <span className="flex items-center text-xs sm:text-sm text-admin-secondary">
                    {stat.change}
                    <ArrowUpRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1" />
                  </span>
                </div>
              </div>
              <div className="p-2 bg-admin-accent rounded-lg">
                <stat.icon className="h-4 w-4 sm:h-5 sm:w-5 text-admin-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </>
  );
};