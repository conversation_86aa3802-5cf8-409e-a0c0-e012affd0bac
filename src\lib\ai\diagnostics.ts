import { supabase } from '@/integrations/supabase/client';

export interface AIServiceStatus {
  service: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  environment: {
    openAI: 'configured' | 'missing';
    supabase: 'configured' | 'missing';
  };
  timestamp: string;
  error?: string;
}

export class AIDiagnostics {
  /**
   * Test the health of AI services
   */
  static async checkAIServices(): Promise<AIServiceStatus[]> {
    const services = ['analyze-document', 'ai-file-analyzer', 'advanced-document-analysis'];
    const results: AIServiceStatus[] = [];

    for (const service of services) {
      try {
        const { data, error } = await supabase.functions.invoke(service, {
          body: { healthCheck: true }
        });

        if (error) {
          results.push({
            service,
            status: 'unhealthy',
            environment: { openAI: 'unknown', supabase: 'unknown' },
            timestamp: new Date().toISOString(),
            error: error.message
          });
        } else {
          results.push({
            service,
            status: data?.status === 'healthy' ? 'healthy' : 'unhealthy',
            environment: data?.environment || { openAI: 'unknown', supabase: 'unknown' },
            timestamp: data?.timestamp || new Date().toISOString()
          });
        }
      } catch (error) {
        results.push({
          service,
          status: 'unhealthy',
          environment: { openAI: 'unknown', supabase: 'unknown' },
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Test document analysis with a simple text
   */
  static async testDocumentAnalysis(): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      const testContent = "This is a test document for AI analysis. It contains sample text to verify the document analysis functionality is working correctly.";
      
      const { data, error } = await supabase.functions.invoke('analyze-document', {
        body: {
          content: testContent,
          fileName: 'test-document.txt',
          type: 'test'
        }
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Test file analyzer with sample data
   */
  static async testFileAnalyzer(): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      const testContent = "Name,Age,Department\nJohn Doe,30,Engineering\nJane Smith,25,Marketing\nBob Johnson,35,Sales";
      
      const { data, error } = await supabase.functions.invoke('ai-file-analyzer', {
        body: {
          fileName: 'test-data.csv',
          fileType: 'text/csv',
          content: testContent,
          userId: (await supabase.auth.getUser()).data.user?.id
        }
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Get comprehensive AI system status
   */
  static async getSystemStatus(): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy';
    services: AIServiceStatus[];
    recommendations: string[];
  }> {
    const services = await this.checkAIServices();
    const healthyServices = services.filter(s => s.status === 'healthy').length;
    const totalServices = services.length;

    let overall: 'healthy' | 'degraded' | 'unhealthy';
    const recommendations: string[] = [];

    if (healthyServices === totalServices) {
      overall = 'healthy';
    } else if (healthyServices > 0) {
      overall = 'degraded';
      recommendations.push('Some AI services are experiencing issues. Check service logs for details.');
    } else {
      overall = 'unhealthy';
      recommendations.push('All AI services are down. Check OpenAI API key configuration.');
    }

    // Check for common issues
    const missingOpenAI = services.some(s => s.environment.openAI === 'missing');
    const missingSupabase = services.some(s => s.environment.supabase === 'missing');

    if (missingOpenAI) {
      recommendations.push('OpenAI API key is not configured. Add OPENAI_API_KEY to Supabase Edge Functions secrets.');
    }

    if (missingSupabase) {
      recommendations.push('Supabase configuration is missing. Check SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.');
    }

    return {
      overall,
      services,
      recommendations
    };
  }

  /**
   * Run all diagnostic tests
   */
  static async runFullDiagnostics(): Promise<{
    systemStatus: Awaited<ReturnType<typeof this.getSystemStatus>>;
    documentAnalysisTest: Awaited<ReturnType<typeof this.testDocumentAnalysis>>;
    fileAnalyzerTest: Awaited<ReturnType<typeof this.testFileAnalyzer>>;
  }> {
    const [systemStatus, documentAnalysisTest, fileAnalyzerTest] = await Promise.all([
      this.getSystemStatus(),
      this.testDocumentAnalysis(),
      this.testFileAnalyzer()
    ]);

    return {
      systemStatus,
      documentAnalysisTest,
      fileAnalyzerTest
    };
  }
}

/**
 * Enhanced error handler for AI operations
 */
export function handleAIError(error: any): {
  title: string;
  message: string;
  actionable: boolean;
  action?: string;
} {
  const errorMessage = error?.message || error?.error || error || 'Unknown error occurred';
  const errorString = typeof errorMessage === 'string' ? errorMessage.toLowerCase() : '';

  // API Key related errors
  if (errorString.includes('api key') || errorString.includes('authentication')) {
    return {
      title: 'AI Service Configuration Error',
      message: 'The AI service is not properly configured. Please contact your administrator.',
      actionable: true,
      action: 'Contact administrator to configure OpenAI API key'
    };
  }

  // Network errors
  if (errorString.includes('network') || errorString.includes('fetch') || errorString.includes('connection')) {
    return {
      title: 'Network Error',
      message: 'Unable to connect to AI services. Please check your internet connection and try again.',
      actionable: true,
      action: 'Check internet connection and retry'
    };
  }

  // Service unavailable
  if (errorString.includes('service unavailable') || errorString.includes('503')) {
    return {
      title: 'Service Temporarily Unavailable',
      message: 'The AI analysis service is temporarily unavailable. Please try again in a few minutes.',
      actionable: true,
      action: 'Wait a few minutes and try again'
    };
  }

  // File size errors
  if (errorString.includes('file size') || errorString.includes('too large')) {
    return {
      title: 'File Too Large',
      message: 'The selected file is too large for analysis. Please choose a smaller file.',
      actionable: true,
      action: 'Select a smaller file (under 10MB)'
    };
  }

  // Rate limiting
  if (errorString.includes('rate limit') || errorString.includes('quota')) {
    return {
      title: 'Service Limit Reached',
      message: 'The AI service has reached its usage limit. Please try again later.',
      actionable: true,
      action: 'Wait and try again later'
    };
  }

  // Generic error
  return {
    title: 'AI Analysis Error',
    message: errorMessage,
    actionable: false
  };
}
