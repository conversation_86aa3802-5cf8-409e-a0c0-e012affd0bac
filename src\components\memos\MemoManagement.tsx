
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { FileUploadComponent } from "@/components/files/FileUploadComponent";
import {
  Plus,
  Send,
  Edit,
  Trash2,
  Eye,
  Pin,
  Calendar,
  Users,
  FileText,
  Paperclip,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";

interface MemoFormData {
  title: string;
  content: string;
  memo_type: string;
  priority: string;
  visibility: string;
  target_audience: any;
  department_id?: string;
  effective_date?: string;
  expiry_date?: string;
  tags: string[];
}

interface Memo {
  id: string;
  title: string;
  content: string;
  memo_type: string;
  priority: string;
  status: string;
  visibility: string;
  target_audience: any;
  author_id: string;
  department_id?: string;
  effective_date?: string;
  expiry_date?: string;
  is_pinned: boolean;
  read_count: number;
  tags: string[];
  created_at: string;
  updated_at: string;
  author?: {
    full_name: string;
    role: string;
  };
  department?: {
    name: string;
  };
}

interface UploadedFile {
  id: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  file_path: string;
  upload_status: string;
}

export const MemoManagement = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [selectedMemo, setSelectedMemo] = useState<Memo | null>(null);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [currentMemoId, setCurrentMemoId] = useState<string | null>(null);

  const [formData, setFormData] = useState<MemoFormData>({
    title: '',
    content: '',
    memo_type: 'general',
    priority: 'medium',
    visibility: 'department',
    target_audience: {},
    tags: []
  });

  // Fetch memos
  const { data: memos = [], isLoading, refetch } = useQuery({
    queryKey: ['memos'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('memos')
        .select(`
          *,
          author:profiles!memos_author_id_fkey(full_name, role),
          department:departments(name)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching memos:', error);
        return [];
      }

      return data || [];
    },
  });

  // Create memo mutation
  const createMemoMutation = useMutation({
    mutationFn: async (data: MemoFormData) => {
      if (!userProfile?.id) throw new Error('User not authenticated');

      const { data: memoData, error } = await supabase
        .from('memos')
        .insert([{
          title: data.title,
          content: data.content,
          memo_type: data.memo_type,
          priority: data.priority,
          visibility: data.visibility,
          target_audience: data.target_audience,
          author_id: userProfile.id,
          department_id: data.department_id || userProfile.department_id,
          effective_date: data.effective_date || null,
          expiry_date: data.expiry_date || null,
          tags: data.tags,
          status: 'published'
        }])
        .select()
        .single();

      if (error) throw error;

      // Send notifications based on visibility
      await sendMemoNotifications(memoData);

      return memoData;
    },
    onSuccess: (data) => {
      setCurrentMemoId(data.id);
      queryClient.invalidateQueries({ queryKey: ['memos'] });
      setIsCreateOpen(false);
      resetForm();

      toast({
        title: "Memo Created Successfully",
        description: `Your ${data.memo_type} memo has been published and notifications sent.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to Create Memo",
        description: error.message || "Failed to create memo. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Delete memo mutation
  const deleteMemoMutation = useMutation({
    mutationFn: async (memoId: string) => {
      const { error } = await supabase
        .from('memos')
        .delete()
        .eq('id', memoId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['memos'] });
      toast({
        title: "Memo Deleted",
        description: "The memo has been successfully deleted.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to Delete Memo",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const sendMemoNotifications = async (memoData: any) => {
    try {
      let recipients: any[] = [];

      // Determine recipients based on visibility
      if (memoData.visibility === 'public') {
        const { data } = await supabase
          .from('profiles')
          .select('id, email, full_name')
          .eq('status', 'active');
        recipients = data || [];
      } else if (memoData.visibility === 'department' && memoData.department_id) {
        const { data } = await supabase
          .from('profiles')
          .select('id, email, full_name')
          .eq('department_id', memoData.department_id)
          .eq('status', 'active');
        recipients = data || [];
      }

      if (recipients.length > 0) {
        // Create in-app notifications
        const notifications = recipients.map(recipient => ({
          user_id: recipient.id,
          type: 'memo_published',
          title: 'New Memo Published',
          message: `${userProfile?.full_name || 'A team member'} published a ${memoData.memo_type} memo: ${memoData.title}`,
          data: {
            memo_id: memoData.id,
            memo_type: memoData.memo_type,
            author: userProfile?.full_name,
            priority: memoData.priority
          }
        }));

        await supabase.from('notifications').insert(notifications);

        // Send email notifications for high priority memos
        if (memoData.priority === 'high' || memoData.priority === 'urgent') {
          await supabase.functions.invoke('send-notification', {
            body: {
              type: 'memo_published',
              recipients: recipients.map(r => r.email).filter(Boolean),
              data: {
                memoTitle: memoData.title,
                memoType: memoData.memo_type,
                author: userProfile?.full_name,
                priority: memoData.priority,
                publishDate: new Date().toLocaleDateString()
              }
            }
          });
        }
      }
    } catch (error) {
      console.error('Error sending memo notifications:', error);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.content) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    createMemoMutation.mutate(formData);
  };

  const handleInputChange = (field: keyof MemoFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileUploadComplete = (files: UploadedFile[]) => {
    setUploadedFiles(files);
    toast({
      title: "Files Uploaded",
      description: `${files.length} file(s) attached to memo`,
    });
  };

  const handleFileUploadError = (error: string) => {
    toast({
      title: "Upload Error",
      description: error,
      variant: "destructive",
    });
  };

  const resetForm = () => {
    setFormData({
      title: '',
      content: '',
      memo_type: 'general',
      priority: 'medium',
      visibility: 'department',
      target_audience: {},
      tags: []
    });
    setUploadedFiles([]);
    setCurrentMemoId(null);
  };

  const viewMemo = (memo: Memo) => {
    setSelectedMemo(memo);
    setIsViewOpen(true);

    // Mark as read
    if (userProfile?.id) {
      supabase
        .from('memo_reads')
        .upsert({
          memo_id: memo.id,
          user_id: userProfile.id
        })
        .then(() => {
          // Update read count
          supabase
            .from('memos')
            .update({ read_count: memo.read_count + 1 })
            .eq('id', memo.id)
            .then(() => refetch());
        });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'policy': return 'bg-blue-100 text-blue-800';
      case 'announcement': return 'bg-purple-100 text-purple-800';
      case 'directive': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Memo Management</h2>
          <p className="text-muted-foreground">Create and manage organizational memos</p>
        </div>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Memo
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Memo</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Memo Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter memo title"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="memo_type">Memo Type</Label>
                  <Select value={formData.memo_type} onValueChange={(value) => handleInputChange('memo_type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select memo type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                      <SelectItem value="policy">Policy</SelectItem>
                      <SelectItem value="announcement">Announcement</SelectItem>
                      <SelectItem value="meeting_notes">Meeting Notes</SelectItem>
                      <SelectItem value="directive">Directive</SelectItem>
                      <SelectItem value="reminder">Reminder</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="visibility">Visibility</Label>
                  <Select value={formData.visibility} onValueChange={(value) => handleInputChange('visibility', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select visibility" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">Public (All Users)</SelectItem>
                      <SelectItem value="department">Department Only</SelectItem>
                      <SelectItem value="role_specific">Role Specific</SelectItem>
                      <SelectItem value="private">Private</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="effective_date">Effective Date</Label>
                  <Input
                    id="effective_date"
                    type="date"
                    value={formData.effective_date || ''}
                    onChange={(e) => handleInputChange('effective_date', e.target.value)}
                  />
                </div>
              </div>

              {/* Content */}
              <div className="space-y-2">
                <Label htmlFor="content">Memo Content *</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  placeholder="Write your memo content here..."
                  rows={8}
                  required
                />
              </div>

              {/* File Upload */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Paperclip className="h-4 w-4" />
                  Attachments (Optional)
                </Label>
                <FileUploadComponent
                  uploadType="memo_attachment"
                  entityType="memo"
                  entityId={currentMemoId || undefined}
                  maxFiles={5}
                  maxSize={10 * 1024 * 1024} // 10MB
                  onUploadComplete={handleFileUploadComplete}
                  onUploadError={handleFileUploadError}
                  className="mt-2"
                />
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsCreateOpen(false);
                    resetForm();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createMemoMutation.isPending}
                  className="flex items-center gap-2"
                >
                  {createMemoMutation.isPending ? (
                    <>
                      <Clock className="h-4 w-4 animate-spin" />
                      Publishing...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      Publish Memo
                    </>
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Memos List */}
      <div className="grid gap-4">
        {isLoading ? (
          <div className="text-center py-8">
            <Clock className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading memos...</p>
          </div>
        ) : memos.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-semibold mb-2">No Memos Found</h3>
              <p className="text-muted-foreground mb-4">Create your first memo to get started.</p>
              <Button onClick={() => setIsCreateOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Memo
              </Button>
            </CardContent>
          </Card>
        ) : (
          memos.map((memo) => (
            <Card key={memo.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold">{memo.title}</h3>
                      {memo.is_pinned && <Pin className="h-4 w-4 text-yellow-500" />}
                    </div>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge className={getPriorityColor(memo.priority)}>
                        {memo.priority.toUpperCase()}
                      </Badge>
                      <Badge className={getTypeColor(memo.memo_type)}>
                        {memo.memo_type.replace('_', ' ').toUpperCase()}
                      </Badge>
                      <Badge variant="outline">
                        {memo.visibility.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      By {memo.author?.full_name} • {new Date(memo.created_at).toLocaleDateString()}
                    </p>
                    <p className="text-sm line-clamp-2">{memo.content}</p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <div className="text-right text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {memo.read_count} reads
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => viewMemo(memo)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    {(userProfile?.id === memo.author_id || userProfile?.role === 'admin') && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteMemoMutation.mutate(memo.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* View Memo Dialog */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedMemo?.title}
              {selectedMemo?.is_pinned && <Pin className="h-4 w-4 text-yellow-500" />}
            </DialogTitle>
          </DialogHeader>
          {selectedMemo && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Badge className={getPriorityColor(selectedMemo.priority)}>
                  {selectedMemo.priority.toUpperCase()}
                </Badge>
                <Badge className={getTypeColor(selectedMemo.memo_type)}>
                  {selectedMemo.memo_type.replace('_', ' ').toUpperCase()}
                </Badge>
                <Badge variant="outline">
                  {selectedMemo.visibility.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground">
                <p>By {selectedMemo.author?.full_name} • {new Date(selectedMemo.created_at).toLocaleDateString()}</p>
                {selectedMemo.department?.name && <p>Department: {selectedMemo.department.name}</p>}
                {selectedMemo.effective_date && <p>Effective: {new Date(selectedMemo.effective_date).toLocaleDateString()}</p>}
              </div>
              <div className="prose max-w-none">
                <div className="whitespace-pre-wrap">{selectedMemo.content}</div>
              </div>
              {selectedMemo.tags && selectedMemo.tags.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Tags:</span>
                  {selectedMemo.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">{tag}</Badge>
                  ))}
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
