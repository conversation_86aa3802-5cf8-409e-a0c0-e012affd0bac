import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  MapPin, 
  Smartphone, 
  Monitor, 
  Tablet, 
  Wifi, 
  WifiOff,
  Play,
  Square,
  Pause,
  Coffee
} from 'lucide-react';
import { useTimeTracking } from '@/hooks/useTimeTracking';
import { locationService } from '@/services/locationService';
import { deviceService } from '@/services/deviceService';
import { TimeCardProps, LocationData, DeviceInfo } from '@/types/timeTracking';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

export const TimeAttendanceCard: React.FC<TimeCardProps> = ({
  userRole,
  showTeamData = false,
  compact = false,
  showControls = true,
}) => {
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  
  const { 
    currentSession, 
    isLoading, 
    actions, 
    isClockingIn, 
    isClockingOut,
    isUpdatingStatus 
  } = useTimeTracking({ 
    realtime: true, 
    refetchInterval: 30000 
  });

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Get device info on mount
  useEffect(() => {
    const getDeviceInfo = async () => {
      const info = deviceService.getDeviceInfo();
      const ipAddress = await deviceService.getIPAddress();
      setDeviceInfo({ ...info, ipAddress });
    };
    
    getDeviceInfo();
  }, []);

  const isCurrentlyClockedIn = !!currentSession && !currentSession.clock_out;
  
  const getWorkDuration = () => {
    if (!currentSession?.clock_in) return '00:00:00';
    
    const clockInTime = new Date(currentSession.clock_in);
    const now = currentTime;
    const diff = now.getTime() - clockInTime.getTime();
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const getDeviceIcon = () => {
    if (!deviceInfo) return <Monitor className="h-4 w-4" />;
    
    switch (deviceInfo.type) {
      case 'mobile':
        return <Smartphone className="h-4 w-4" />;
      case 'tablet':
        return <Tablet className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const getStatusColor = () => {
    if (!isCurrentlyClockedIn) return 'bg-gray-500/20 text-gray-500';
    
    switch (currentSession?.status) {
      case 'break':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'lunch':
        return 'bg-orange-500/20 text-orange-500';
      case 'overtime':
        return 'bg-purple-500/20 text-purple-500';
      default:
        return 'bg-green-500/20 text-green-500';
    }
  };

  const handleClockIn = async () => {
    setIsGettingLocation(true);
    
    try {
      const location: LocationData = await locationService.getCurrentLocation();
      const device: DeviceInfo = deviceInfo || deviceService.getDeviceInfo();
      
      await actions.clockIn({
        location,
        device,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        method: 'manual',
      });
    } catch (error) {
      console.error('Clock in failed:', error);
    } finally {
      setIsGettingLocation(false);
    }
  };

  const handleClockOut = async () => {
    if (!currentSession?.id) return;
    
    try {
      const location: LocationData = await locationService.getCurrentLocation();
      const device: DeviceInfo = deviceInfo || deviceService.getDeviceInfo();
      
      await actions.clockOut(currentSession.id, {
        location,
        device,
        method: 'manual',
      });
    } catch (error) {
      console.error('Clock out failed:', error);
    }
  };

  const handleStatusChange = async (status: 'active' | 'break' | 'lunch') => {
    if (!currentSession?.id) return;
    
    try {
      await actions.updateStatus(currentSession.id, status);
    } catch (error) {
      console.error('Status update failed:', error);
    }
  };

  if (isLoading) {
    return (
      <Card className="relative overflow-hidden bg-gradient-to-br from-red-500/10 to-red-600/20 border-red-500/20">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-red-500/20 rounded w-3/4"></div>
            <div className="h-8 bg-red-500/20 rounded w-1/2"></div>
            <div className="h-4 bg-red-500/20 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "relative overflow-hidden transition-all duration-300 hover:scale-105",
      "bg-gradient-to-br from-red-500/10 to-red-600/20 border-red-500/20",
      "shadow-lg hover:shadow-xl",
      "before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/10 before:to-transparent before:pointer-events-none",
      compact ? "p-4" : "p-6"
    )}>
      {/* 3D Effect Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />
      
      <CardContent className={cn("relative z-10", compact ? "p-4" : "p-6")}>
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-red-500" />
            <span className="font-semibold text-foreground">Time Clock</span>
          </div>
          
          <Badge className={cn("text-xs font-medium", getStatusColor())}>
            {isCurrentlyClockedIn ? (
              currentSession?.status?.toUpperCase() || 'ACTIVE'
            ) : (
              'CLOCKED OUT'
            )}
          </Badge>
        </div>

        {/* Current Time Display */}
        <div className="text-center mb-4">
          <div className="text-2xl font-bold text-foreground mb-1">
            {format(currentTime, 'HH:mm:ss')}
          </div>
          <div className="text-sm text-muted-foreground">
            {format(currentTime, 'EEEE, MMMM d, yyyy')}
          </div>
        </div>

        {/* Work Duration */}
        {isCurrentlyClockedIn && (
          <div className="text-center mb-4">
            <div className="text-lg font-semibold text-red-500">
              {getWorkDuration()}
            </div>
            <div className="text-xs text-muted-foreground">
              Work Duration
            </div>
          </div>
        )}

        {/* Location & Device Info */}
        <div className="space-y-2 mb-4">
          {currentSession?.location_address && (
            <div className="flex items-center gap-2 text-sm">
              <MapPin className="h-4 w-4 text-red-500 flex-shrink-0" />
              <span className="text-muted-foreground truncate">
                {currentSession.location_address}
              </span>
            </div>
          )}
          
          {deviceInfo && (
            <div className="flex items-center gap-2 text-sm">
              {getDeviceIcon()}
              <span className="text-muted-foreground">
                {deviceInfo.browser} on {deviceInfo.platform}
              </span>
            </div>
          )}
          
          {currentSession?.is_remote !== undefined && (
            <div className="flex items-center gap-2 text-sm">
              {currentSession.is_remote ? (
                <WifiOff className="h-4 w-4 text-orange-500" />
              ) : (
                <Wifi className="h-4 w-4 text-green-500" />
              )}
              <span className="text-muted-foreground">
                {currentSession.is_remote ? 'Remote Work' : 'On-site'}
              </span>
            </div>
          )}
        </div>

        {/* Controls */}
        {showControls && (
          <div className="space-y-3">
            {/* Main Clock In/Out Button */}
            <Button
              onClick={isCurrentlyClockedIn ? handleClockOut : handleClockIn}
              disabled={isClockingIn || isClockingOut || isGettingLocation}
              className={cn(
                "w-full font-semibold transition-all duration-200",
                isCurrentlyClockedIn
                  ? "bg-red-500 hover:bg-red-600 text-white"
                  : "bg-green-500 hover:bg-green-600 text-white"
              )}
            >
              {isGettingLocation ? (
                <>
                  <MapPin className="h-4 w-4 mr-2 animate-pulse" />
                  Getting Location...
                </>
              ) : isClockingIn ? (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Clocking In...
                </>
              ) : isClockingOut ? (
                <>
                  <Square className="h-4 w-4 mr-2" />
                  Clocking Out...
                </>
              ) : isCurrentlyClockedIn ? (
                <>
                  <Square className="h-4 w-4 mr-2" />
                  Clock Out
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Clock In
                </>
              )}
            </Button>

            {/* Status Controls */}
            {isCurrentlyClockedIn && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusChange('break')}
                  disabled={isUpdatingStatus}
                  className="flex-1"
                >
                  <Pause className="h-3 w-3 mr-1" />
                  Break
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusChange('lunch')}
                  disabled={isUpdatingStatus}
                  className="flex-1"
                >
                  <Coffee className="h-3 w-3 mr-1" />
                  Lunch
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusChange('active')}
                  disabled={isUpdatingStatus}
                  className="flex-1"
                >
                  <Play className="h-3 w-3 mr-1" />
                  Active
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Clock In Time */}
        {isCurrentlyClockedIn && currentSession?.clock_in && (
          <div className="mt-4 pt-4 border-t border-red-500/20">
            <div className="text-xs text-muted-foreground text-center">
              Clocked in at {format(new Date(currentSession.clock_in), 'HH:mm')}
            </div>
          </div>
        )}
      </CardContent>

      {/* Animated Border Effect */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-red-500/20 via-transparent to-red-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </Card>
  );
};
