
import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Users, UserPlus, Trash2, DollarSign, Eye, Edit, Calendar, MapPin } from 'lucide-react';
import { useProjects } from '@/hooks/useProjects';
import { useUserManagement } from '@/hooks/useUserManagement';
import { ProjectAPI, ProjectFormData } from '@/lib/project-api';
import { SuccessModal } from '@/components/ui/success-modal';
import { supabase } from '@/integrations/supabase/client';

export const ProjectManagement = () => {
  const { users } = useUserManagement();
  const [searchParams, setSearchParams] = useSearchParams();
  const [projects, setProjects] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [managers, setManagers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('list');
  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    description: '',
    client_name: '',
    budget: 0,
    location: '',
    start_date: '',
    end_date: '',
    status: 'planning' as 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled',
    manager_id: '',
    department_id: ''
  });

  // Computed value for team management
  const selectedProjectForTeam = selectedProjectId
    ? projects.find(p => p.id === selectedProjectId)
    : null;

  // Load data on component mount
  useEffect(() => {
    loadProjects();
    loadDepartments();
    loadManagers();
  }, []);

  // Handle URL parameters for direct project access
  useEffect(() => {
    const projectId = searchParams.get('project');
    if (projectId && projects.length > 0) {
      const project = projects.find(p => p.id === projectId);
      if (project) {
        setSelectedProject(project);
        setShowViewModal(true);
        // Clear the URL parameter after opening the modal
        setSearchParams(prev => {
          const newParams = new URLSearchParams(prev);
          newParams.delete('project');
          return newParams;
        });
      }
    }
  }, [projects, searchParams, setSearchParams]);

  const loadProjects = async () => {
    setLoading(true);
    try {
      const { data } = await ProjectAPI.getAllProjects();
      setProjects(data);
    } catch (error) {
      console.error('Error loading projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDepartments = async () => {
    try {
      const { data } = await ProjectAPI.getDepartments();
      setDepartments(data);
    } catch (error) {
      console.error('Error loading departments:', error);
    }
  };

  const loadManagers = async () => {
    try {
      const { data } = await ProjectAPI.getManagers();
      setManagers(data);
    } catch (error) {
      console.error('Error loading managers:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Validate required fields
      if (!formData.name?.trim()) {
        throw new Error('Project name is required');
      }

      const { data, error } = await ProjectAPI.createProject(formData, user.id);

      if (error) {
        throw new Error(error.message);
      }

      // Reset form and close modal
      setShowCreateForm(false);
      setFormData({
        name: '',
        description: '',
        client_name: '',
        budget: 0,
        location: '',
        start_date: '',
        end_date: '',
        status: 'planning',
        manager_id: '',
        department_id: ''
      });

      // Show success modal
      setShowSuccessModal(true);

      // Reload projects
      await loadProjects();

      console.log('Project created successfully:', data);
    } catch (error: any) {
      console.error('Error creating project:', error);
      // Use toast instead of alert for better UX
      if (typeof window !== 'undefined') {
        const event = new CustomEvent('show-toast', {
          detail: {
            title: "Error",
            description: error.message || "Failed to create project",
            variant: "destructive"
          }
        });
        window.dispatchEvent(event);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleViewProject = async (projectId: string) => {
    try {
      const { data } = await ProjectAPI.getProjectById(projectId);
      setSelectedProject(data);
      setShowViewModal(true);
    } catch (error) {
      console.error('Error loading project details:', error);
    }
  };

  const handleAssignMember = async (projectId: string, staffId: string) => {
    try {
      const result = await ProjectAPI.assignStaffToProject(projectId, staffId);
      if (result.error) {
        throw new Error(result.error.message);
      }
      await loadProjects(); // Reload to show updated assignments

      // Show success message
      if (typeof window !== 'undefined') {
        const event = new CustomEvent('show-toast', {
          detail: {
            title: "Success",
            description: "Member assigned to project successfully"
          }
        });
        window.dispatchEvent(event);
      }
    } catch (error: any) {
      console.error('Error assigning member:', error);
      if (typeof window !== 'undefined') {
        const event = new CustomEvent('show-toast', {
          detail: {
            title: "Error",
            description: error.message || "Failed to assign member",
            variant: "destructive"
          }
        });
        window.dispatchEvent(event);
      }
    }
  };

  const handleRemoveMember = async (assignmentId: string) => {
    try {
      const result = await ProjectAPI.removeStaffFromProject(assignmentId);
      if (result.error) {
        throw new Error(result.error.message);
      }
      await loadProjects(); // Reload to show updated assignments

      // Show success message
      if (typeof window !== 'undefined') {
        const event = new CustomEvent('show-toast', {
          detail: {
            title: "Success",
            description: "Member removed from project successfully"
          }
        });
        window.dispatchEvent(event);
      }
    } catch (error: any) {
      console.error('Error removing member:', error);
      if (typeof window !== 'undefined') {
        const event = new CustomEvent('show-toast', {
          detail: {
            title: "Error",
            description: error.message || "Failed to remove member",
            variant: "destructive"
          }
        });
        window.dispatchEvent(event);
      }
    }
  };

  const handleUpdateStatus = async (projectId: string, status: string) => {
    try {
      const result = await ProjectAPI.updateProjectStatus(projectId, status);
      if (result.error) {
        throw new Error(result.error.message);
      }
      await loadProjects(); // Reload to show updated status

      // Show success message
      if (typeof window !== 'undefined') {
        const event = new CustomEvent('show-toast', {
          detail: {
            title: "Success",
            description: "Project status updated successfully"
          }
        });
        window.dispatchEvent(event);
      }
    } catch (error: any) {
      console.error('Error updating project status:', error);
      if (typeof window !== 'undefined') {
        const event = new CustomEvent('show-toast', {
          detail: {
            title: "Error",
            description: error.message || "Failed to update project status",
            variant: "destructive"
          }
        });
        window.dispatchEvent(event);
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'active': return 'bg-blue-100 text-blue-800';
      case 'on_hold': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };



  if (loading) {
    return <div className="text-center">Loading projects...</div>;
  }

  return (
    <div className="space-y-6 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">Project Management</h2>
        <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
          <DialogTrigger asChild>
            <Button className="w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Create Project
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle>Create New Project</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Project Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="client_name">Client Name</Label>
                  <Input
                    id="client_name"
                    value={formData.client_name}
                    onChange={(e) => setFormData({ ...formData, client_name: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="budget">Budget</Label>
                  <Input
                    id="budget"
                    type="number"
                    step="0.01"
                    value={formData.budget || ''}
                    onChange={(e) => setFormData({ ...formData, budget: parseFloat(e.target.value) || 0 })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="start_date">Start Date</Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_date">End Date</Label>
                  <Input
                    id="end_date"
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={formData.location || ''}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  placeholder="Project location"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="manager">Project Manager</Label>
                  <Select value={formData.manager_id || ''} onValueChange={(value) => setFormData({ ...formData, manager_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select manager" />
                    </SelectTrigger>
                    <SelectContent>
                      {managers.map((manager) => (
                        <SelectItem key={manager.id} value={manager.id}>
                          {manager.full_name || manager.email} ({manager.role})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="department">Department</Label>
                  <Select value={formData.department_id || ''} onValueChange={(value) => setFormData({ ...formData, department_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status || 'planning'} onValueChange={(value: 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled') => setFormData({ ...formData, status: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="planning">Planning</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="on_hold">On Hold</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2">
                <Button type="submit">Create Project</Button>
                <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="list">Project List</TabsTrigger>
          <TabsTrigger value="assignments">Team Assignments</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader>
              <CardTitle>All Projects</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[200px]">Project</TableHead>
                      <TableHead className="min-w-[120px]">Client</TableHead>
                      <TableHead className="min-w-[100px]">Status</TableHead>
                      <TableHead className="min-w-[120px]">Budget</TableHead>
                      <TableHead className="min-w-[150px]">Timeline</TableHead>
                      <TableHead className="min-w-[80px]">Team</TableHead>
                      <TableHead className="min-w-[150px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {projects.map((project) => (
                      <TableRow key={project.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{project.name}</p>
                            {project.description && (
                              <p className="text-sm text-muted-foreground">{project.description}</p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{project.client_name || '-'}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(project.status || 'planning')}>
                            {project.status || 'planning'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {project.budget ? (
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              {project.budget.toLocaleString()}
                            </div>
                          ) : '-'}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {project.start_date && (
                              <div>Start: {new Date(project.start_date).toLocaleDateString()}</div>
                            )}
                            {project.end_date && (
                              <div>End: {new Date(project.end_date).toLocaleDateString()}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            {project.tasks?.length || 0}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleViewProject(project.id)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedProjectId(project.id);
                                setActiveTab('assignments');
                              }}
                            >
                              <Users className="h-4 w-4 mr-1" />
                              Team
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assignments">
          <Card>
            <CardHeader>
              <CardTitle>Team Assignments</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedProjectForTeam ? (
                <ProjectTeamManagement
                  project={selectedProjectForTeam}
                  users={users}
                  onAssignMember={handleAssignMember}
                  onRemoveMember={handleRemoveMember}
                />
              ) : (
                <p className="text-muted-foreground">Select a project to manage team assignments</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Success Modal */}
      <SuccessModal
        open={showSuccessModal}
        onOpenChange={setShowSuccessModal}
        title="Project Created Successfully!"
        description="Your project has been created and is ready for team assignments and task management."
        variant="success"
        showConfetti={true}
      />

      {/* Project View Modal */}
      <Dialog open={showViewModal} onOpenChange={setShowViewModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto mx-4">
          <DialogHeader>
            <DialogTitle>Project Details</DialogTitle>
          </DialogHeader>
          {selectedProject && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">{selectedProject.name}</h3>
                  <p className="text-muted-foreground mb-4">{selectedProject.description}</p>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>Client: {selectedProject.client_name || 'Not specified'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>Location: {selectedProject.location || 'Not specified'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      <span>Budget: ${selectedProject.budget?.toLocaleString() || 'Not set'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>Duration: {selectedProject.start_date} to {selectedProject.end_date || 'Ongoing'}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Project Status</h4>
                  <Badge className={getStatusColor(selectedProject.status)}>
                    {selectedProject.status?.replace('_', ' ').toUpperCase()}
                  </Badge>

                  <h4 className="font-semibold mt-4 mb-2">Team Members</h4>
                  {selectedProject.project_assignments?.length > 0 ? (
                    <div className="space-y-2">
                      {selectedProject.project_assignments.map((assignment: any) => (
                        <div key={assignment.id} className="flex items-center justify-between p-2 border rounded">
                          <span>{assignment.profiles?.full_name}</span>
                          <Badge variant="outline">{assignment.role || 'Member'}</Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No team members assigned</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

interface ProjectTeamManagementProps {
  project: any;
  users: any[];
  onAssignMember: (projectId: string, staffId: string) => void;
  onRemoveMember: (taskId: string) => void;
}

const ProjectTeamManagement = ({ project, users, onAssignMember, onRemoveMember }: ProjectTeamManagementProps) => {
  const [selectedUser, setSelectedUser] = useState('');

  const assignedUserIds = project.project_assignments?.map((assignment: any) => assignment.assigned_to) || [];
  const unassignedUsers = users.filter(user => !assignedUserIds.includes(user.id));

  const handleAssign = () => {
    if (selectedUser) {
      onAssignMember(project.id, selectedUser);
      setSelectedUser('');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Project: {project.name}</h3>
        
        <div className="flex gap-4 mb-6">
          <Select value={selectedUser} onValueChange={setSelectedUser}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Select user to assign" />
            </SelectTrigger>
            <SelectContent>
              {unassignedUsers.map((user) => (
                <SelectItem key={user.id} value={user.id}>
                  <div className="flex items-center gap-2">
                    <span>{user.full_name || user.email}</span>
                    <Badge variant="outline" className="text-xs">
                      {user.role}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={handleAssign} disabled={!selectedUser}>
            <UserPlus className="h-4 w-4 mr-2" />
            Assign
          </Button>
        </div>
      </div>

      <div>
        <h4 className="font-medium mb-3">Current Team Members</h4>
        {project.project_assignments?.length > 0 ? (
          <div className="grid gap-3">
            {project.project_assignments.map((assignment: any) => (
              <div key={assignment.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div>
                    <p className="font-medium">{assignment.profiles?.full_name || 'Unknown User'}</p>
                    <p className="text-sm text-muted-foreground">{assignment.profiles?.role || 'No Role'}</p>
                    <p className="text-xs text-muted-foreground">Role: {assignment.role || 'Member'}</p>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onRemoveMember(assignment.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-muted-foreground">No team members assigned yet</p>
        )}
      </div>


    </div>
  );
};
