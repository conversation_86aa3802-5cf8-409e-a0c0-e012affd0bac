/**
 * Lang<PERSON>hain Integration - Main Export File
 * Centralized exports for all LangChain functionality
 */

// Configuration
export { 
  langChainConfig, 
  LangChainConfigService,
  type LangChainConfig 
} from './config';

// Document Processing
export { 
  documentProcessor, 
  LangChainDocumentProcessor,
  type ProcessedDocument,
  type DocumentProcessingOptions 
} from './document-processor';

// RAG System
export { 
  ragSystem, 
  LangChainRAGSystem,
  type RAGQuery,
  type RAGResponse,
  type RAGSystemOptions 
} from './rag-system';

// Agents and Tools
export { 
  aiWorkboardAgent, 
  AIWorkboardAgent,
  DatabaseQueryTool,
  DocumentAnalysisTool,
  ReportGenerationTool,
  SystemManagementTool 
} from './agents';

// Memory Management
export { 
  memoryManager, 
  LangChainMemoryManager,
  PersistentChatMessageHistory,
  type ConversationContext,
  type MemoryEntry 
} from './memory';

// Chains and Workflows
export { 
  summarizationChain,
  reportAnalysis<PERSON>hain,
  qa<PERSON>hain,
  DocumentSummarizationChain,
  ReportAnalysisChain,
  ContextualQAChain,
  WorkflowChain,
  ChainFactory 
} from './chains';

// Integration Service
export { 
  langChainIntegration, 
  LangChainIntegrationService,
  type AIRequest,
  type AIResponse,
  type StreamingAIResponse 
} from './integration';

// Monitoring and Analytics
export { 
  langChainMonitoring, 
  LangChainMonitoringService,
  type OperationMetrics,
  type UsageStats,
  type PerformanceMetrics 
} from './monitoring';

/**
 * Initialize LangChain System
 * Convenience function to initialize all LangChain components
 */
export async function initializeLangChain(): Promise<{
  success: boolean;
  errors: string[];
  components: {
    config: boolean;
    rag: boolean;
    agent: boolean;
    integration: boolean;
  };
}> {
  const errors: string[] = [];
  const components = {
    config: false,
    rag: false,
    agent: false,
    integration: false,
  };

  try {
    // Validate configuration
    const validation = langChainConfig.validateConfig();
    if (validation.isValid) {
      components.config = true;
    } else {
      errors.push(`Configuration errors: ${validation.errors.join(', ')}`);
    }

    // Initialize RAG system
    try {
      await ragSystem.initialize();
      components.rag = true;
    } catch (error) {
      errors.push(`RAG initialization failed: ${error}`);
    }

    // Initialize AI agent
    try {
      await aiWorkboardAgent.initialize();
      components.agent = true;
    } catch (error) {
      errors.push(`Agent initialization failed: ${error}`);
    }

    // Initialize integration service
    try {
      await langChainIntegration.initialize();
      components.integration = true;
    } catch (error) {
      errors.push(`Integration service initialization failed: ${error}`);
    }

    const success = Object.values(components).every(Boolean);
    
    return {
      success,
      errors,
      components,
    };
  } catch (error) {
    errors.push(`System initialization failed: ${error}`);
    return {
      success: false,
      errors,
      components,
    };
  }
}

/**
 * Get LangChain System Status
 * Returns the current status of all LangChain components
 */
export async function getLangChainStatus(): Promise<{
  overall: 'healthy' | 'warning' | 'critical';
  components: {
    config: 'healthy' | 'error';
    rag: 'healthy' | 'error';
    agent: 'healthy' | 'error';
    integration: 'healthy' | 'error';
    monitoring: 'healthy' | 'error';
  };
  metrics: {
    operationsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
    systemHealth: 'healthy' | 'warning' | 'critical';
  };
}> {
  const components = {
    config: 'error' as const,
    rag: 'error' as const,
    agent: 'error' as const,
    integration: 'error' as const,
    monitoring: 'error' as const,
  };

  // Check configuration
  try {
    const validation = langChainConfig.validateConfig();
    components.config = validation.isValid ? 'healthy' : 'error';
  } catch (error) {
    console.error('Config status check failed:', error);
  }

  // Check RAG system
  try {
    const ragStore = ragSystem.getRetriever();
    components.rag = ragStore ? 'healthy' : 'error';
  } catch (error) {
    console.error('RAG status check failed:', error);
  }

  // Check agent (simplified check)
  try {
    components.agent = 'healthy'; // Assume healthy if no errors
  } catch (error) {
    console.error('Agent status check failed:', error);
  }

  // Check integration service (simplified check)
  try {
    components.integration = 'healthy'; // Assume healthy if no errors
  } catch (error) {
    console.error('Integration status check failed:', error);
  }

  // Check monitoring
  try {
    const realTimeMetrics = await langChainMonitoring.getRealTimeMetrics();
    components.monitoring = 'healthy';
    
    // Determine overall health
    const healthyComponents = Object.values(components).filter(status => status === 'healthy').length;
    const totalComponents = Object.values(components).length;
    
    let overall: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (healthyComponents < totalComponents) {
      overall = healthyComponents < totalComponents / 2 ? 'critical' : 'warning';
    }

    return {
      overall,
      components,
      metrics: realTimeMetrics,
    };
  } catch (error) {
    console.error('Monitoring status check failed:', error);
    return {
      overall: 'critical',
      components,
      metrics: {
        operationsPerMinute: 0,
        averageResponseTime: 0,
        errorRate: 1,
        systemHealth: 'critical',
      },
    };
  }
}

/**
 * Quick LangChain Health Check
 * Simple health check for monitoring systems
 */
export async function healthCheck(): Promise<{
  status: 'ok' | 'error';
  timestamp: string;
  version: string;
}> {
  try {
    const status = await getLangChainStatus();
    return {
      status: status.overall === 'critical' ? 'error' : 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };
  } catch (error) {
    return {
      status: 'error',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };
  }
}

/**
 * LangChain Utilities
 */
export const LangChainUtils = {
  /**
   * Process a simple AI request
   */
  async processMessage(
    message: string, 
    userId?: string, 
    options: any = {}
  ): Promise<string> {
    try {
      const response = await langChainIntegration.processRequest({
        message,
        userId,
        options,
      });
      return response.response;
    } catch (error) {
      throw new Error(`Failed to process message: ${error}`);
    }
  },

  /**
   * Add document to knowledge base
   */
  async addDocument(content: string, metadata: any = {}): Promise<void> {
    try {
      const processedDoc = await documentProcessor.processDocument(content, {
        customMetadata: metadata,
      });
      await ragSystem.addDocuments(processedDoc.chunks);
    } catch (error) {
      throw new Error(`Failed to add document: ${error}`);
    }
  },

  /**
   * Search knowledge base
   */
  async searchKnowledge(query: string, maxResults: number = 5): Promise<any[]> {
    try {
      return await ragSystem.searchKnowledgeBase(query, maxResults);
    } catch (error) {
      throw new Error(`Failed to search knowledge base: ${error}`);
    }
  },

  /**
   * Get conversation summary
   */
  async getConversationSummary(userId: string, sessionId: string): Promise<string> {
    try {
      return await memoryManager.getConversationSummary({ userId, sessionId });
    } catch (error) {
      throw new Error(`Failed to get conversation summary: ${error}`);
    }
  },
};
