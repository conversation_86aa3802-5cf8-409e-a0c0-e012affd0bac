import { supabase } from '@/integrations/supabase/client';
import { ExcelGenerator, ExcelGenerationConfig } from './excelGenerator';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

export interface AIDocumentRequest {
  generation_id: string;
  config: DocumentGenerationConfig;
  user_id: string;
}

export interface DocumentGenerationConfig {
  template_id: string;
  document_name: string;
  ai_enhancement: boolean;
  data_sources: string[];
  variables: Record<string, any>;
  chart_types: string[];
  include_analysis: boolean;
  output_format: string;
}

export interface AIEnhancementResult {
  insights: string[];
  recommendations: string[];
  optimizations: string[];
  generated_content: Record<string, any>;
}

export class AIDocumentService {
  private static instance: AIDocumentService;

  static getInstance(): AIDocumentService {
    if (!AIDocumentService.instance) {
      AIDocumentService.instance = new AIDocumentService();
    }
    return AIDocumentService.instance;
  }

  async generateDocument(request: AIDocumentRequest): Promise<string> {
    try {
      // Update generation status
      await this.updateGenerationStatus(request.generation_id, 'processing');

      // Get template configuration
      const template = await this.getTemplate(request.config.template_id);
      if (!template) {
        throw new Error('Template not found');
      }

      // Fetch data from configured sources
      const data = await this.fetchDataSources(template.data_sources, request.config.variables);

      // Apply AI enhancements if enabled
      let aiEnhancements: AIEnhancementResult | null = null;
      if (request.config.ai_enhancement) {
        aiEnhancements = await this.applyAIEnhancements(data, template, request.config);
      }

      // Generate document based on format
      let filePath: string;
      let fileSize: number;

      switch (request.config.output_format) {
        case 'excel':
          const result = await this.generateExcelDocument(template, data, aiEnhancements, request.config);
          filePath = result.filePath;
          fileSize = result.fileSize;
          break;
        case 'pdf':
          // PDF generation would be implemented here
          throw new Error('PDF generation not yet implemented');
        case 'dashboard':
          // Dashboard generation would be implemented here
          throw new Error('Dashboard generation not yet implemented');
        default:
          throw new Error(`Unsupported output format: ${request.config.output_format}`);
      }

      // Update generation record with success
      await this.updateGenerationStatus(request.generation_id, 'completed', {
        file_path: filePath,
        file_size: fileSize,
        ai_enhancements: aiEnhancements,
        processing_time: Date.now() - new Date().getTime()
      });

      // Log AI enhancement usage
      if (aiEnhancements) {
        await this.logAIEnhancement(request.generation_id, aiEnhancements);
      }

      return filePath;
    } catch (error: any) {
      // Update generation record with error
      await this.updateGenerationStatus(request.generation_id, 'failed', {
        error_details: error.message
      });
      throw error;
    }
  }

  private async getTemplate(templateId: string): Promise<any> {
    const { data, error } = await supabase
      .from('document_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (error) throw error;
    return data;
  }

  private async fetchDataSources(dataSources: any[], variables: Record<string, any>): Promise<any[]> {
    const results = [];

    for (const source of dataSources) {
      try {
        let data: any[] = [];

        switch (source.type) {
          case 'database_table':
            data = await this.fetchDatabaseData(source, variables);
            break;
          case 'api_endpoint':
            data = await this.fetchAPIData(source, variables);
            break;
          case 'real_time_query':
            data = await this.fetchRealTimeData(source, variables);
            break;
          default:
            console.warn(`Unsupported data source type: ${source.type}`);
        }

        results.push({
          source_name: source.name,
          data: data
        });
      } catch (error) {
        console.error(`Error fetching data from ${source.name}:`, error);
        results.push({
          source_name: source.name,
          data: [],
          error: error.message
        });
      }
    }

    return results;
  }

  private async fetchDatabaseData(source: any, variables: Record<string, any>): Promise<any[]> {
    // Replace variables in query
    let query = source.query;
    Object.entries(variables).forEach(([key, value]) => {
      query = query.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });

    // Execute query based on source configuration
    if (source.table_name) {
      const { data, error } = await supabase
        .from(source.table_name)
        .select(source.columns || '*');

      if (error) throw error;
      return data || [];
    }

    // For custom queries, we'd need to use RPC functions
    return [];
  }

  private async fetchAPIData(source: any, variables: Record<string, any>): Promise<any[]> {
    // Replace variables in URL
    let url = source.url;
    Object.entries(variables).forEach(([key, value]) => {
      url = url.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });

    const response = await fetch(url, {
      method: source.method || 'GET',
      headers: source.headers || {},
      body: source.body ? JSON.stringify(source.body) : undefined
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    const data = await response.json();
    return Array.isArray(data) ? data : [data];
  }

  private async fetchRealTimeData(source: any, variables: Record<string, any>): Promise<any[]> {
    // Implement real-time data fetching based on source configuration
    // This could involve WebSocket connections, real-time database queries, etc.
    return [];
  }

  private async applyAIEnhancements(
    data: any[], 
    template: any, 
    config: DocumentGenerationConfig
  ): Promise<AIEnhancementResult> {
    try {
      // Call AI service for enhancements
      const { data: aiResult, error } = await supabase.functions.invoke('enhance-document-ai', {
        body: {
          data: data,
          template: template,
          config: config,
          enhancement_types: [
            'data_analysis',
            'insight_generation',
            'chart_recommendations',
            'content_optimization'
          ]
        }
      });

      if (error) throw error;

      return {
        insights: aiResult.insights || [],
        recommendations: aiResult.recommendations || [],
        optimizations: aiResult.optimizations || [],
        generated_content: aiResult.generated_content || {}
      };
    } catch (error) {
      console.error('AI enhancement failed:', error);
      return {
        insights: [],
        recommendations: [],
        optimizations: [],
        generated_content: {}
      };
    }
  }

  private async generateExcelDocument(
    template: any,
    data: any[],
    aiEnhancements: AIEnhancementResult | null,
    config: DocumentGenerationConfig
  ): Promise<{ filePath: string; fileSize: number }> {
    // Create Excel configuration based on template and data
    const excelConfig: ExcelGenerationConfig = {
      documentName: config.document_name,
      sheets: this.createSheetsFromTemplate(template, data, aiEnhancements),
      charts: this.createChartsFromTemplate(template, data),
      styling: template.styling_config || {
        theme: 'modern',
        primaryColor: '#2563eb',
        secondaryColor: '#64748b',
        fontFamily: 'Arial',
        fontSize: 11
      },
      aiEnhancements: config.ai_enhancement ? {
        generateInsights: true,
        suggestCharts: true,
        optimizeLayout: true,
        addCommentary: true,
        dataAnalysis: true
      } : undefined
    };

    // Generate Excel file
    const generator = new ExcelGenerator(excelConfig);
    const blob = await generator.generateExcel();

    // Upload to Supabase Storage
    const fileName = `${config.document_name}_${Date.now()}.xlsx`;
    const filePath = `generated-documents/${fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('documents')
      .upload(filePath, blob, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) throw uploadError;

    return {
      filePath: filePath,
      fileSize: blob.size
    };
  }

  private createSheetsFromTemplate(template: any, data: any[], aiEnhancements: AIEnhancementResult | null): any[] {
    const sheets = [];

    // Create main data sheet
    if (data.length > 0) {
      const mainData = data[0]?.data || [];
      if (mainData.length > 0) {
        const headers = Object.keys(mainData[0]);
        sheets.push({
          name: 'Data',
          headers: headers,
          data: mainData,
          formatting: {
            font: { name: 'Arial', size: 11 },
            fill: { type: 'solid', color: '#f8fafc' }
          }
        });
      }
    }

    // Create summary sheet if AI insights are available
    if (aiEnhancements && aiEnhancements.insights.length > 0) {
      sheets.push({
        name: 'AI Insights',
        headers: ['Insight', 'Type', 'Confidence'],
        data: aiEnhancements.insights.map((insight, index) => [
          insight,
          'Generated',
          'High'
        ]),
        formatting: {
          font: { name: 'Arial', size: 11, bold: true },
          fill: { type: 'solid', color: '#eff6ff' }
        }
      });
    }

    return sheets;
  }

  private createChartsFromTemplate(template: any, data: any[]): any[] {
    const charts = [];

    // Create charts based on template configuration
    if (template.chart_configs && template.chart_configs.length > 0) {
      template.chart_configs.forEach((chartConfig: any, index: number) => {
        charts.push({
          type: chartConfig.type || 'column',
          title: chartConfig.title || `Chart ${index + 1}`,
          sheetName: 'Data',
          dataRange: 'A1:D10', // This would be calculated based on actual data
          position: { row: 15 + (index * 20), col: 1 },
          size: { width: 600, height: 400 },
          styling: {
            colors: chartConfig.colors || ['#2563eb', '#dc2626', '#16a34a'],
            theme: 'modern',
            showLegend: true,
            showDataLabels: true,
            gridLines: true
          }
        });
      });
    }

    return charts;
  }

  private async updateGenerationStatus(
    generationId: string, 
    status: string, 
    updates: any = {}
  ): Promise<void> {
    const { error } = await supabase
      .from('document_generations')
      .update({
        status: status,
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', generationId);

    if (error) {
      console.error('Error updating generation status:', error);
    }
  }

  private async logAIEnhancement(
    generationId: string,
    enhancements: AIEnhancementResult
  ): Promise<void> {
    try {
      await supabase
        .from('ai_enhancement_logs')
        .insert({
          document_generation_id: generationId,
          enhancement_type: 'comprehensive',
          ai_provider: 'openai',
          model_used: 'gpt-4',
          ai_response: enhancements,
          success: true,
          processing_time: 1000, // This would be measured
          tokens_used: 500, // This would be tracked
          cost_estimate: 0.01 // This would be calculated
        });
    } catch (error) {
      console.error('Error logging AI enhancement:', error);
    }
  }

  // Utility method for direct Excel generation (for testing/demo)
  async generateSampleExcel(type: 'financial' | 'project' = 'financial'): Promise<void> {
    try {
      let config: ExcelGenerationConfig;

      if (type === 'financial') {
        // Generate sample financial data
        const sampleData = this.generateSampleFinancialData();
        config = await ExcelGenerator.createFinancialReport(sampleData);
      } else {
        // Generate sample project data
        const sampleData = this.generateSampleProjectData();
        config = await ExcelGenerator.createProjectReport(sampleData);
      }

      const generator = new ExcelGenerator(config);
      const blob = await generator.generateExcel();

      // Download the file
      saveAs(blob, `${config.documentName}_${Date.now()}.xlsx`);
    } catch (error) {
      console.error('Error generating sample Excel:', error);
      throw error;
    }
  }

  private generateSampleFinancialData(): any[] {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => ({
      Month: month,
      Revenue: Math.floor(Math.random() * 100000) + 50000,
      Expenses: Math.floor(Math.random() * 60000) + 30000,
      Profit: Math.floor(Math.random() * 40000) + 20000
    }));
  }

  private generateSampleProjectData(): any[] {
    const projects = ['Website Redesign', 'Mobile App', 'Database Migration', 'Security Audit'];
    return projects.map(project => ({
      Project: project,
      Status: ['Active', 'Completed', 'On Hold'][Math.floor(Math.random() * 3)],
      'Progress %': Math.floor(Math.random() * 100),
      Budget: Math.floor(Math.random() * 50000) + 10000,
      Spent: Math.floor(Math.random() * 30000) + 5000,
      Remaining: Math.floor(Math.random() * 20000) + 5000
    }));
  }
}
