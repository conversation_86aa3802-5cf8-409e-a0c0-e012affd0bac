import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { AdminTabContent } from "./AdminTabContent";
import { AdminStats } from "./AdminStats";
import { AdminPerformanceChart } from "./AdminPerformanceChart";
import { CompactTimeCard } from "@/components/time-tracking/CompactTimeCard";
import {
  Clock,
  Brain,
  BookOpen,
  Radio,
  FolderOpen,
  Calendar,
  Users,
  Building,
  Battery,
  Settings,
  MessageSquare
} from "lucide-react";

export const AdminDashboard = () => {
  return (
    <div className="space-y-6">
      {/* Admin Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <div className="md:col-span-1">
          <CompactTimeCard userRole="admin" showControls={true} />
        </div>
        <div className="md:col-span-4 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <AdminStats />
        </div>
      </div>

      {/* Performance Chart */}
      <AdminPerformanceChart />

      {/* Admin Management Tabs */}
      <Tabs defaultValue="time" className="space-y-6">
        <div className="overflow-x-auto">
          <TabsList className="grid w-full grid-cols-5 lg:grid-cols-10 gap-1 min-w-max">
            <TabsTrigger value="time" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Time</span>
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <Brain className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">AI</span>
            </TabsTrigger>
            <TabsTrigger value="knowledge" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <BookOpen className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Knowledge</span>
            </TabsTrigger>
            <TabsTrigger value="telecom" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <Radio className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Telecom</span>
            </TabsTrigger>
            <TabsTrigger value="projects" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <FolderOpen className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Projects</span>
            </TabsTrigger>
            <TabsTrigger value="leave" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Leave</span>
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <Users className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Users</span>
            </TabsTrigger>
            <TabsTrigger value="departments" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <Building className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Depts</span>
            </TabsTrigger>
            <TabsTrigger value="battery" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <Battery className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Battery</span>
            </TabsTrigger>

            <TabsTrigger value="communication" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <MessageSquare className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Communication</span>
            </TabsTrigger>

            <TabsTrigger value="settings" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3">
              <Settings className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline text-xs sm:text-sm">Settings</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <AdminTabContent />
      </Tabs>
    </div>
  );
};
