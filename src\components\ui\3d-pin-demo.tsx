"use client";
import React from "react";
import { PinContainer } from "@/components/ui/3d-pin";

export default function AnimatedPinDemo() {
  return (
    <div className="h-[40rem] w-full flex items-center justify-center">
      <PinContainer
        title="CTNL AI Location"
        href="#"
      >
        <div className="flex basis-full flex-col p-4 tracking-tight text-slate-100/50 sm:basis-1/2 w-[20rem] h-[20rem]">
          <h3 className="max-w-xs !pb-2 !m-0 font-bold text-base text-slate-100 dark:text-slate-900">
            Current Location
          </h3>
          <div className="text-base !m-0 !p-0 font-normal">
            <span className="text-slate-500 dark:text-slate-600">
              CT Communication Towers - AI Workforce Management Hub
            </span>
          </div>
          <div className="flex flex-1 w-full rounded-lg mt-4 bg-gradient-to-br from-red-500 via-red-600 to-red-700 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-red-400/20 to-transparent"></div>
            <div className="absolute bottom-4 left-4 text-white">
              <div className="text-sm font-semibold">CTNL AI</div>
              <div className="text-xs opacity-80">Work-Board Location</div>
            </div>
          </div>
        </div>
      </PinContainer>
    </div>
  );
}
