/**
 * Lang<PERSON>hain Document Processing Service
 * Handles document loading, splitting, and embedding with LangChain
 */

import { Document } from '@langchain/core/documents';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { OpenAIEmbeddings } from '@langchain/openai';
import { Chroma } from '@langchain/community/vectorstores/chroma';
import { HNSWLib } from '@langchain/community/vectorstores/hnswlib';
import { FaissStore } from '@langchain/community/vectorstores/faiss';
import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf';
import { DocxLoader } from '@langchain/community/document_loaders/fs/docx';
import { TextLoader } from '@langchain/community/document_loaders/fs/text';
import { CSVLoader } from '@langchain/community/document_loaders/fs/csv';
import { JSONLoader } from '@langchain/community/document_loaders/fs/json';
import type { VectorStore } from '@langchain/core/vectorstores';
import { langChainConfig } from './config';

export interface ProcessedDocument {
  id: string;
  filename: string;
  content: string;
  metadata: Record<string, any>;
  chunks: Document[];
  embeddings?: number[][];
}

export interface DocumentProcessingOptions {
  chunkSize?: number;
  chunkOverlap?: number;
  preserveMetadata?: boolean;
  customMetadata?: Record<string, any>;
}

export class LangChainDocumentProcessor {
  private textSplitter: RecursiveCharacterTextSplitter;
  private embeddings: OpenAIEmbeddings;
  private vectorStore: VectorStore | null = null;

  constructor() {
    const config = langChainConfig.getConfig();
    
    this.textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: config.rag.chunkSize,
      chunkOverlap: config.rag.chunkOverlap,
      separators: ['\n\n', '\n', ' ', ''],
    });

    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: config.openai.apiKey,
      modelName: 'text-embedding-ada-002',
    });
  }

  /**
   * Load document from file path
   */
  public async loadDocument(filePath: string): Promise<Document[]> {
    const extension = filePath.split('.').pop()?.toLowerCase();
    
    let loader;
    switch (extension) {
      case 'pdf':
        loader = new PDFLoader(filePath);
        break;
      case 'docx':
        loader = new DocxLoader(filePath);
        break;
      case 'txt':
      case 'md':
        loader = new TextLoader(filePath);
        break;
      case 'csv':
        loader = new CSVLoader(filePath);
        break;
      case 'json':
        loader = new JSONLoader(filePath);
        break;
      default:
        throw new Error(`Unsupported file type: ${extension}`);
    }

    return await loader.load();
  }

  /**
   * Load document from text content
   */
  public async loadFromText(
    content: string, 
    metadata: Record<string, any> = {}
  ): Promise<Document[]> {
    return [new Document({ pageContent: content, metadata })];
  }

  /**
   * Split documents into chunks
   */
  public async splitDocuments(
    documents: Document[], 
    options: DocumentProcessingOptions = {}
  ): Promise<Document[]> {
    const splitter = options.chunkSize || options.chunkOverlap 
      ? new RecursiveCharacterTextSplitter({
          chunkSize: options.chunkSize || langChainConfig.getConfig().rag.chunkSize,
          chunkOverlap: options.chunkOverlap || langChainConfig.getConfig().rag.chunkOverlap,
        })
      : this.textSplitter;

    const chunks = await splitter.splitDocuments(documents);
    
    // Add custom metadata if provided
    if (options.customMetadata) {
      chunks.forEach(chunk => {
        chunk.metadata = { ...chunk.metadata, ...options.customMetadata };
      });
    }

    return chunks;
  }

  /**
   * Process complete document workflow
   */
  public async processDocument(
    filePath: string, 
    options: DocumentProcessingOptions = {}
  ): Promise<ProcessedDocument> {
    try {
      // Load document
      const documents = await this.loadDocument(filePath);
      
      // Split into chunks
      const chunks = await this.splitDocuments(documents, options);
      
      // Extract content and metadata
      const content = documents.map(doc => doc.pageContent).join('\n');
      const metadata = documents[0]?.metadata || {};
      
      // Generate embeddings if needed
      let embeddings: number[][] | undefined;
      if (options.preserveMetadata) {
        const texts = chunks.map(chunk => chunk.pageContent);
        embeddings = await this.embeddings.embedDocuments(texts);
      }

      return {
        id: this.generateDocumentId(filePath),
        filename: filePath.split('/').pop() || filePath,
        content,
        metadata: { ...metadata, ...options.customMetadata },
        chunks,
        embeddings,
      };
    } catch (error) {
      throw new Error(`Failed to process document ${filePath}: ${error}`);
    }
  }

  /**
   * Initialize vector store
   */
  public async initializeVectorStore(): Promise<VectorStore> {
    const config = langChainConfig.getConfig().vectorStore;
    
    switch (config.provider) {
      case 'chroma':
        this.vectorStore = new Chroma(this.embeddings, {
          collectionName: config.collectionName,
          url: process.env.CHROMA_URL || 'http://localhost:8000',
        });
        break;
        
      case 'hnswlib':
        this.vectorStore = new HNSWLib(this.embeddings, {
          space: 'cosine',
          numDimensions: config.dimensions,
        });
        break;
        
      case 'faiss':
        this.vectorStore = new FaissStore(this.embeddings, {});
        break;
        
      default:
        throw new Error(`Unsupported vector store provider: ${config.provider}`);
    }
    
    return this.vectorStore;
  }

  /**
   * Add documents to vector store
   */
  public async addDocumentsToVectorStore(documents: Document[]): Promise<void> {
    if (!this.vectorStore) {
      await this.initializeVectorStore();
    }
    
    if (!this.vectorStore) {
      throw new Error('Failed to initialize vector store');
    }
    
    await this.vectorStore.addDocuments(documents);
  }

  /**
   * Search similar documents
   */
  public async searchSimilarDocuments(
    query: string, 
    k: number = 5
  ): Promise<Document[]> {
    if (!this.vectorStore) {
      await this.initializeVectorStore();
    }
    
    if (!this.vectorStore) {
      throw new Error('Vector store not initialized');
    }
    
    return await this.vectorStore.similaritySearch(query, k);
  }

  /**
   * Search with score threshold
   */
  public async searchWithScore(
    query: string, 
    k: number = 5, 
    scoreThreshold: number = 0.7
  ): Promise<[Document, number][]> {
    if (!this.vectorStore) {
      await this.initializeVectorStore();
    }
    
    if (!this.vectorStore) {
      throw new Error('Vector store not initialized');
    }
    
    const results = await this.vectorStore.similaritySearchWithScore(query, k);
    return results.filter(([_, score]) => score >= scoreThreshold);
  }

  /**
   * Get vector store instance
   */
  public getVectorStore(): VectorStore | null {
    return this.vectorStore;
  }

  /**
   * Generate unique document ID
   */
  private generateDocumentId(filePath: string): string {
    const timestamp = Date.now();
    const filename = filePath.split('/').pop() || filePath;
    return `doc_${filename}_${timestamp}`;
  }

  /**
   * Clear vector store
   */
  public async clearVectorStore(): Promise<void> {
    if (this.vectorStore && 'delete' in this.vectorStore) {
      // Implementation depends on vector store type
      console.log('Clearing vector store...');
    }
  }
}

// Export singleton instance
export const documentProcessor = new LangChainDocumentProcessor();
