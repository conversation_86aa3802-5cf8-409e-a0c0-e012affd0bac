import { EnhancedAIChat } from '@/components/ai/EnhancedAIChat';
import { AIAssistant } from '@/components/ai/AIAssistant';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { <PERSON><PERSON>, <PERSON><PERSON>les, Zap, Brain, MessageSquare, Palette } from 'lucide-react';

export const AIShowcase = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="h-12 w-12 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center shadow-lg">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent">
              CTNL AI Chat Enhancement
            </h1>
          </div>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Experience the enhanced AI chat module with beautiful design, CTNL AI colors, and advanced features
          </p>
          <div className="flex justify-center gap-2 mt-4">
            <Badge variant="secondary" className="bg-red-500/20 text-red-300 border-red-500/30">
              <Sparkles className="h-3 w-3 mr-1" />
              Enhanced Design
            </Badge>
            <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/30">
              <Zap className="h-3 w-3 mr-1" />
              CTNL AI Colors
            </Badge>
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
              <MessageSquare className="h-3 w-3 mr-1" />
              Interactive Features
            </Badge>
          </div>
        </div>

        {/* Comparison Tabs */}
        <Tabs defaultValue="enhanced" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8 bg-gray-800/50 border border-gray-700">
            <TabsTrigger 
              value="enhanced" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-500 data-[state=active]:to-red-600 data-[state=active]:text-white"
            >
              <Palette className="h-4 w-4 mr-2" />
              Enhanced Design
            </TabsTrigger>
            <TabsTrigger 
              value="original"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-gray-600 data-[state=active]:to-gray-700 data-[state=active]:text-white"
            >
              <Bot className="h-4 w-4 mr-2" />
              Original Design
            </TabsTrigger>
          </TabsList>

          <TabsContent value="enhanced" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Enhanced Chat Demo */}
              <Card className="glass-card border-red-500/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <div className="h-6 w-6 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center">
                      <Sparkles className="h-3 w-3 text-white" />
                    </div>
                    Enhanced AI Chat
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <EnhancedAIChat />
                </CardContent>
              </Card>

              {/* Features List */}
              <Card className="glass-card border-green-500/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Zap className="h-5 w-5 text-green-400" />
                    Enhanced Features
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="h-2 w-2 rounded-full bg-red-500 mt-2"></div>
                      <div>
                        <h4 className="text-white font-medium">CTNL AI Color Scheme</h4>
                        <p className="text-gray-400 text-sm">Beautiful red and green gradients matching your brand</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <div className="h-2 w-2 rounded-full bg-green-500 mt-2"></div>
                      <div>
                        <h4 className="text-white font-medium">Glassmorphism Design</h4>
                        <p className="text-gray-400 text-sm">Modern glass effect with backdrop blur and gradients</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <div className="h-2 w-2 rounded-full bg-blue-500 mt-2"></div>
                      <div>
                        <h4 className="text-white font-medium">Interactive Elements</h4>
                        <p className="text-gray-400 text-sm">Hover effects, animations, and smooth transitions</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <div className="h-2 w-2 rounded-full bg-purple-500 mt-2"></div>
                      <div>
                        <h4 className="text-white font-medium">Quick Action Tags</h4>
                        <p className="text-gray-400 text-sm">Pre-defined queries for common tasks</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <div className="h-2 w-2 rounded-full bg-yellow-500 mt-2"></div>
                      <div>
                        <h4 className="text-white font-medium">Enhanced Status Indicators</h4>
                        <p className="text-gray-400 text-sm">Online status, typing indicators, and AI branding</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <div className="h-2 w-2 rounded-full bg-pink-500 mt-2"></div>
                      <div>
                        <h4 className="text-white font-medium">Mobile Responsive</h4>
                        <p className="text-gray-400 text-sm">Optimized for all screen sizes and touch devices</p>
                      </div>
                    </div>
                  </div>

                  {/* CSS Features */}
                  <div className="mt-6 p-4 bg-gray-800/50 rounded-lg border border-gray-700">
                    <h4 className="text-white font-medium mb-2 flex items-center gap-2">
                      <Palette className="h-4 w-4 text-red-400" />
                      CSS Enhancements
                    </h4>
                    <ul className="text-sm text-gray-400 space-y-1">
                      <li>• Gradient backgrounds with CTNL AI colors</li>
                      <li>• Custom scrollbar with red theme</li>
                      <li>• Smooth animations and transitions</li>
                      <li>• Glassmorphism effects with backdrop blur</li>
                      <li>• Hover states with transform effects</li>
                      <li>• Responsive design for mobile devices</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="original" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Original Chat Demo */}
              <Card className="glass-card border-gray-500/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Bot className="h-5 w-5 text-gray-400" />
                    Original AI Chat
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <AIAssistant />
                </CardContent>
              </Card>

              {/* Comparison */}
              <Card className="glass-card border-gray-500/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <MessageSquare className="h-5 w-5 text-gray-400" />
                    Before vs After
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="p-4 bg-gray-800/30 rounded-lg border border-gray-700">
                      <h4 className="text-white font-medium mb-2">Before Enhancement</h4>
                      <ul className="text-sm text-gray-400 space-y-1">
                        <li>• Basic card design</li>
                        <li>• Standard UI components</li>
                        <li>• Limited visual appeal</li>
                        <li>• Basic functionality</li>
                      </ul>
                    </div>
                    
                    <div className="p-4 bg-gradient-to-r from-red-500/10 to-green-500/10 rounded-lg border border-red-500/20">
                      <h4 className="text-white font-medium mb-2">After Enhancement</h4>
                      <ul className="text-sm text-gray-300 space-y-1">
                        <li>• Beautiful glassmorphism design</li>
                        <li>• CTNL AI brand colors</li>
                        <li>• Interactive animations</li>
                        <li>• Enhanced user experience</li>
                        <li>• Quick action tags</li>
                        <li>• Mobile responsive</li>
                      </ul>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
                    <h4 className="text-white font-medium mb-2 flex items-center gap-2">
                      <Sparkles className="h-4 w-4 text-green-400" />
                      Key Improvements
                    </h4>
                    <ul className="text-sm text-gray-300 space-y-1">
                      <li>✅ Modern glassmorphism design</li>
                      <li>✅ CTNL AI red/green color scheme</li>
                      <li>✅ Smooth animations and transitions</li>
                      <li>✅ Enhanced status indicators</li>
                      <li>✅ Quick action functionality</li>
                      <li>✅ Mobile-first responsive design</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Implementation Note */}
        <Card className="glass-card border-blue-500/20 mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-white">
              <Brain className="h-5 w-5 text-blue-400" />
              Implementation Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-white font-medium mb-2">Files Modified</h4>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>• <code className="text-green-400">src/components/ai/AIAssistant.tsx</code> - Enhanced with new design</li>
                  <li>• <code className="text-green-400">src/components/ai/EnhancedAIChat.tsx</code> - New component</li>
                  <li>• <code className="text-green-400">src/index.css</code> - Added enhanced styles</li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">CSS Features Added</h4>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>• Container chat bot styles</li>
                  <li>• Gradient backgrounds</li>
                  <li>• Custom scrollbars</li>
                  <li>• Animation keyframes</li>
                  <li>• Mobile responsive breakpoints</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
