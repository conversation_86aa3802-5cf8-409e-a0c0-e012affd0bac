/**
 * Simple LangChain Integration for CTNL AI Workboard
 * Direct integration without complex setup
 */

import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { LL<PERSON>hain } from 'langchain/chains';
import { BufferMemory } from 'langchain/memory';
import { supabase } from '@/integrations/supabase/client';
import { ErrorHandler } from '@/utils/error-handler';

// Configuration - Browser-safe environment variable access
const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;
const USE_LANGCHAIN = import.meta.env.VITE_USE_LANGCHAIN === 'true';

export interface SimpleLangChainRequest {
  message: string;
  userId?: string;
  sessionId?: string;
  context?: {
    interface?: 'standard' | 'hacker_terminal' | 'futuristic' | 'enhanced';
    role?: string;
    department?: string;
    previousMessages?: any[];
  };
  options?: {
    temperature?: number;
    maxTokens?: number;
    useMemory?: boolean;
  };
}

export interface SimpleLangChainResponse {
  response: string;
  enhanced: boolean;
  metadata?: {
    processingTime: number;
    model: string;
    tokensUsed?: number;
  };
}

/**
 * Simple LangChain Service
 */
export class SimpleLangChainService {
  private model: ChatOpenAI | null = null;
  private memory: Map<string, BufferMemory> = new Map();

  constructor() {
    if (USE_LANGCHAIN && OPENAI_API_KEY && OPENAI_API_KEY !== 'your_openai_api_key_here') {
      try {
        this.model = new ChatOpenAI({
          openAIApiKey: OPENAI_API_KEY,
          modelName: 'gpt-4-turbo-preview',
          temperature: 0.7,
          maxTokens: 4096,
        });
        console.log('LangChain initialized successfully');
      } catch (error) {
        console.warn('Failed to initialize LangChain:', error);
        this.model = null;
      }
    }
  }

  /**
   * Check if LangChain is available
   */
  public isAvailable(): boolean {
    return this.model !== null;
  }

  /**
   * Process a message with LangChain
   */
  public async processMessage(request: SimpleLangChainRequest): Promise<SimpleLangChainResponse> {
    const startTime = Date.now();

    if (!this.isAvailable()) {
      throw new Error('LangChain is not available');
    }

    try {
      // Create prompt based on interface type
      const prompt = this.createPrompt(request.context?.interface || 'standard');
      
      // Create chain
      const chain = new LLMChain({ 
        llm: this.model!, 
        prompt,
        memory: request.options?.useMemory ? this.getMemory(request.userId, request.sessionId) : undefined
      });

      // Build context
      const context = this.buildContext(request);

      // Execute chain
      const result = await chain.call({
        context,
        input: request.message,
        userRole: request.context?.role || 'user'
      });

      const processingTime = Date.now() - startTime;

      // Save conversation if userId provided
      if (request.userId && request.sessionId) {
        await this.saveConversation(
          request.userId,
          request.sessionId,
          request.message,
          result.text,
          processingTime
        );
      }

      return {
        response: result.text,
        enhanced: true,
        metadata: {
          processingTime,
          model: 'gpt-4-turbo-preview',
        },
      };
    } catch (error) {
      const errorInfo = ErrorHandler.handleSupabaseError(error, 'LangChain');
      const cleanMessage = ErrorHandler.cleanErrorMessage(error);

      console.error('LangChain processing error:', cleanMessage);
      throw new Error(cleanMessage);
    }
  }

  /**
   * Create prompt template based on interface
   */
  private createPrompt(interfaceType: string): PromptTemplate {
    let systemPrompt = '';

    switch (interfaceType) {
      case 'hacker_terminal':
        systemPrompt = `You are a sophisticated AI system operating in a hacker-style terminal interface for the CTNL AI Workboard.

Respond in a technical, precise manner with:
- Terminal-style formatting when appropriate
- Technical insights and system information
- Command-like responses for system queries
- Cybersecurity and system administration context

Context: {context}
User Role: {userRole}
User Input: {input}

Response:`;
        break;

      case 'futuristic':
        systemPrompt = `You are an advanced AI assistant with futuristic capabilities for the CTNL AI Workboard system.

You can:
- Execute complex multi-step workflows
- Analyze data and generate insights
- Perform system actions and integrations
- Provide predictive analytics
- Handle advanced automation tasks

Context: {context}
User Role: {userRole}
User Request: {input}

AI Response:`;
        break;

      case 'enhanced':
        systemPrompt = `You are an enhanced AI assistant for the CTNL AI Workboard with advanced conversational abilities.

Provide:
- Detailed, helpful responses
- Context-aware suggestions
- Proactive assistance
- Rich formatting when appropriate
- Follow-up questions when needed

Context: {context}
User Role: {userRole}
User Message: {input}

Assistant Response:`;
        break;

      default:
        systemPrompt = `You are a helpful AI assistant for the CTNL AI Workboard system.

Provide accurate, helpful responses about:
- Work management and productivity
- System features and capabilities
- Data analysis and reporting
- Task management and collaboration
- General assistance with the platform

Context: {context}
User Role: {userRole}
User Question: {input}

Answer:`;
    }

    return PromptTemplate.fromTemplate(systemPrompt);
  }

  /**
   * Build context string
   */
  private buildContext(request: SimpleLangChainRequest): string {
    const parts: string[] = [];

    if (request.userId) parts.push(`User ID: ${request.userId}`);
    if (request.context?.role) parts.push(`User Role: ${request.context.role}`);
    if (request.context?.department) parts.push(`Department: ${request.context.department}`);
    if (request.context?.interface) parts.push(`Interface: ${request.context.interface}`);
    
    if (request.context?.previousMessages?.length) {
      const recentMessages = request.context.previousMessages.slice(-3);
      parts.push(`Recent Messages: ${JSON.stringify(recentMessages)}`);
    }

    return parts.join('\n');
  }

  /**
   * Get or create memory for user session
   */
  private getMemory(userId?: string, sessionId?: string): BufferMemory | undefined {
    if (!userId || !sessionId) return undefined;

    const memoryKey = `${userId}_${sessionId}`;
    
    if (!this.memory.has(memoryKey)) {
      this.memory.set(memoryKey, new BufferMemory({
        memoryKey: 'chat_history',
        returnMessages: true,
      }));
    }

    return this.memory.get(memoryKey);
  }

  /**
   * Save conversation to database (respects RLS policies)
   */
  private async saveConversation(
    userId: string,
    sessionId: string,
    message: string,
    response: string,
    processingTime: number
  ): Promise<void> {
    try {
      // Save user message (RLS will ensure user can only insert their own data)
      const { error: userMessageError } = await supabase.from('conversation_history').insert({
        user_id: userId,
        session_id: sessionId,
        type: 'human',
        content: message,
        metadata: {
          interface: 'langchain',
          timestamp: new Date().toISOString()
        }
      });

      if (userMessageError) {
        console.warn('Failed to save user message:', userMessageError);
      }

      // Save AI response (RLS will ensure user can only insert their own data)
      const { error: aiMessageError } = await supabase.from('conversation_history').insert({
        user_id: userId,
        session_id: sessionId,
        type: 'ai',
        content: response,
        metadata: {
          interface: 'langchain',
          processingTime,
          model: 'gpt-4-turbo-preview',
          enhanced: true,
          timestamp: new Date().toISOString()
        }
      });

      if (aiMessageError) {
        console.warn('Failed to save AI response:', aiMessageError);
      }

      // Save analytics (requires staff-admin+ role or service role)
      const { error: analyticsError } = await supabase.from('conversation_analytics').insert({
        user_id: userId,
        session_id: sessionId,
        human_message_length: message.length,
        ai_message_length: response.length,
        processing_time: processingTime,
        model_used: 'gpt-4-turbo-preview',
        interface_type: 'langchain',
        metadata: {
          enhanced: true,
          timestamp: new Date().toISOString()
        }
      });

      if (analyticsError) {
        console.warn('Failed to save analytics (may require higher privileges):', analyticsError);
      }

      // Log operation (requires staff-admin+ role or service role)
      const { error: operationError } = await supabase.from('langchain_operations').insert({
        operation_type: 'chat',
        duration: processingTime,
        success: true,
        user_id: userId,
        session_id: sessionId,
        model: 'gpt-4-turbo-preview',
        input_length: message.length,
        output_length: response.length,
        interface_type: 'langchain',
        metadata: {
          enhanced: true,
          timestamp: new Date().toISOString()
        }
      });

      if (operationError) {
        console.warn('Failed to log operation (may require higher privileges):', operationError);
      }

    } catch (error) {
      console.warn('Failed to save conversation data:', error);
    }
  }

  /**
   * Clear memory for user session
   */
  public clearMemory(userId: string, sessionId: string): void {
    const memoryKey = `${userId}_${sessionId}`;
    this.memory.delete(memoryKey);
  }

  /**
   * Get conversation history (respects RLS policies)
   */
  public async getConversationHistory(userId: string, sessionId: string): Promise<any[]> {
    try {
      // RLS policies will automatically filter based on user role
      // Users can only see their own conversations unless they're manager+
      const { data, error } = await supabase
        .from('conversation_history')
        .select('*')
        .eq('user_id', userId)
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true })
        .limit(50); // Limit to last 50 messages for performance

      if (error) {
        console.warn('Failed to get conversation history (may be due to RLS):', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get conversation history:', error);
      return [];
    }
  }

  /**
   * Get conversation analytics (respects RLS policies)
   */
  public async getConversationAnalytics(userId?: string): Promise<any[]> {
    try {
      let query = supabase
        .from('conversation_analytics')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100);

      // If userId provided, filter by user (RLS will still apply)
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;

      if (error) {
        console.warn('Failed to get analytics (may be due to RLS permissions):', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get conversation analytics:', error);
      return [];
    }
  }

  /**
   * Get LangChain operations (respects RLS policies)
   */
  public async getLangChainOperations(userId?: string): Promise<any[]> {
    try {
      let query = supabase
        .from('langchain_operations')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100);

      // If userId provided, filter by user (RLS will still apply)
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;

      if (error) {
        console.warn('Failed to get operations (may be due to RLS permissions):', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get LangChain operations:', error);
      return [];
    }
  }
}

// Export singleton instance
export const simpleLangChain = new SimpleLangChainService();

/**
 * Simple function to process AI messages
 */
export async function processAIMessage(
  message: string,
  userId?: string,
  context: any = {}
): Promise<SimpleLangChainResponse> {
  if (simpleLangChain.isAvailable()) {
    try {
      return await simpleLangChain.processMessage({
        message,
        userId,
        sessionId: `session_${Date.now()}`,
        context,
        options: { useMemory: true }
      });
    } catch (error) {
      console.warn('LangChain processing failed, falling back to basic response');
    }
  }

  // Fallback to basic response
  return {
    response: generateBasicResponse(message, context),
    enhanced: false,
    metadata: {
      processingTime: 100,
      model: 'basic'
    }
  };
}

/**
 * Basic response generator (fallback)
 */
function generateBasicResponse(message: string, context: any = {}): string {
  let response = "I'm here to help you with the CTNL AI Work-Board system. ";

  if (context.role) {
    response += `As a ${context.role}, `;
  }

  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('time') || lowerMessage.includes('log')) {
    response += "I can help you track your time and manage your work logs.";
  } else if (lowerMessage.includes('project')) {
    response += "I can assist you with project management tasks.";
  } else if (lowerMessage.includes('report')) {
    response += "I can help you generate various reports.";
  } else if (lowerMessage.includes('help')) {
    response += "I can assist you with various tasks in the system.";
  } else {
    response += "I understand your request and I'm here to help.";
  }

  return response;
}
