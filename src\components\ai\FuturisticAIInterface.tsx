import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Bot, 
  Send, 
  User, 
  Loader, 
  FileText, 
  Users, 
  ClipboardList,
  Database,
  Upload,
  Zap,
  Brain,
  Cpu,
  Activity,
  CheckCircle,
  AlertTriangle,
  Mic,
  <PERSON>c<PERSON>ff,
  Key,
  Settings
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { NeuralNetwork } from "@/components/NeuralNetwork";
import { api } from "@/lib/api";
import { APIKeyManager } from "./APIKeyManager";
import { useSimpleLangChain } from "@/hooks/useSimpleLangChain";

interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'system' | 'analysis' | 'action' | 'error';
  actions?: Array<{
    type: string;
    status: 'completed' | 'failed';
    result?: any;
  }>;
}

interface AIAction {
  type: 'create_project' | 'assign_task' | 'query_database' | 'analyze_file' | 'generate_report';
  parameters: Record<string, any>;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

export const FuturisticAIInterface = () => {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [currentStep, setCurrentStep] = useState('');
  const [progress, setProgress] = useState(0);
  const [hasApiKeyError, setHasApiKeyError] = useState(false);
  const [showApiKeyManager, setShowApiKeyManager] = useState(false);
  const [isApiKeyConfigured, setIsApiKeyConfigured] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const { sendFuturisticMessage } = useSimpleLangChain();

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const speechRecognition = new SpeechRecognition();
      speechRecognition.continuous = false;
      speechRecognition.interimResults = false;
      speechRecognition.lang = 'en-US';

      speechRecognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInput(transcript);
        setIsListening(false);
      };

      speechRecognition.onend = () => {
        setIsListening(false);
      };

      setRecognition(speechRecognition);
    }
  }, []);

  // Load conversation history
  useEffect(() => {
    loadConversationHistory();
  }, [userProfile]);

  const loadConversationHistory = async () => {
    try {
      if (!userProfile?.id) return;

      const { data, error } = await supabase
        .from('ai_interactions')
        .select('*')
        .eq('user_id', userProfile.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;

      if (data && data.length > 0) {
        const historyMessages: AIMessage[] = data.reverse().flatMap(interaction => {
          const messages: AIMessage[] = [];

          // Add user message if query exists
          if (interaction.query) {
            messages.push({
              id: `${interaction.id}-user`,
              role: 'user' as const,
              content: interaction.query,
              timestamp: new Date(interaction.created_at),
            });
          }

          // Add assistant message if response exists
          if (interaction.response) {
            messages.push({
              id: `${interaction.id}-assistant`,
              role: 'assistant' as const,
              content: interaction.response,
              timestamp: new Date(interaction.created_at),
              type: 'analysis' as const,
              actions: interaction.actions || []
            });
          } else if (interaction.role === 'assistant') {
            // Fallback for direct assistant messages
            messages.push({
              id: `${interaction.id}-assistant`,
              role: 'assistant' as const,
              content: interaction.message,
              timestamp: new Date(interaction.created_at),
              type: interaction.type as any || 'analysis',
              actions: interaction.actions || []
            });
          }

          return messages;
        });
        setMessages(historyMessages);
      }
    } catch (error) {
      console.error('Error loading conversation history:', error);
    }
  };

  const handleAIError = (error: any): { content: string; showSettings: boolean } => {
    const errorMessage = error?.message || error?.error || error || 'Unknown error';
    const errorStr = typeof errorMessage === 'string' ? errorMessage.toLowerCase() : '';

    // API Key errors
    if (errorStr.includes('openai api key not configured') || 
        errorStr.includes('api key not configured') ||
        errorStr.includes('authentication failed') ||
        errorStr.includes('invalid api key')) {
      setHasApiKeyError(true);
      setShowApiKeyManager(true);

      toast({
        title: "⚠️ API Key Required",
        description: "OpenAI API key is not configured. Please configure your API key to enable AI features.",
        variant: "destructive",
        duration: 10000,
        action: {
          label: "Configure API Key",
          onClick: () => setShowApiKeyManager(true)
        },
      });

      return {
        content: `🔑 **API Configuration Required**

The OpenAI API key is not configured in the system. AI functionality requires a valid API key to operate.

**What this means:**
- AI chat, analysis, and automation features are unavailable
- File processing and document analysis won't work
- Intelligent task creation and project management are disabled

**Solution:**
${userProfile?.role === 'admin' 
  ? '• Go to **Admin Settings > API Keys** to configure the OpenAI API key\n• Ensure the API key has sufficient credits and permissions' 
  : '• Contact your system administrator to configure the API key\n• Ask them to set up the OpenAI integration in the admin panel'
}

**Need help?** Contact your administrator or check the system documentation.`,
        showSettings: userProfile?.role === 'admin'
      };
    }

    // Quota/Rate limit errors
    if (errorStr.includes('quota') || errorStr.includes('rate limit') || errorStr.includes('insufficient_quota')) {
      toast({
        title: "⚠️ API Quota Exceeded",
        description: "AI service quota reached. Please try again later or contact administrator.",
        variant: "destructive",
        duration: 8000,
      });

      return {
        content: `⚠️ **API Quota Exceeded**

The AI service has reached its usage quota or rate limit.

**What this means:**
- Too many requests have been made to the AI service
- The API plan may need to be upgraded
- Service will resume when the quota resets

**Solutions:**
• Wait and try again later (quotas typically reset daily/monthly)
• Contact your administrator to upgrade the API plan
• Use AI features more sparingly to conserve quota

**Estimated reset time:** Check with your administrator for quota details.`,
        showSettings: false
      };
    }

    // Network errors
    if (errorStr.includes('network') || errorStr.includes('connection') || errorStr.includes('timeout')) {
      toast({
        title: "🌐 Connection Error",
        description: "Network connection issue. Please check your connection and try again.",
        variant: "destructive",
        duration: 5000,
      });

      return {
        content: `🌐 **Connection Error**

There was a problem connecting to the AI service.

**Possible causes:**
• Internet connection issues
• Service temporarily unavailable
• Firewall or network restrictions

**Solutions:**
• Check your internet connection
• Try again in a few moments
• Contact support if the issue persists`,
        showSettings: false
      };
    }

    // Generic error
    toast({
      title: "❌ AI Processing Failed",
      description: "An unexpected error occurred. Please try again.",
      variant: "destructive",
      duration: 5000,
    });

    return {
      content: `❌ **Processing Error**

An unexpected error occurred while processing your request.

**Error details:** ${errorMessage}

**What you can do:**
• Try rephrasing your request
• Check if all required information is provided
• Contact support if the issue continues

I'm ready to try again when you are!`,
      showSettings: false
    };
  };

  const detectActions = (userInput: string): AIAction[] => {
    const actions: AIAction[] = [];
    const lowercaseInput = userInput.toLowerCase();

    if (lowercaseInput.includes('create project') || lowercaseInput.includes('new project')) {
      actions.push({
        type: 'create_project',
        parameters: { name: extractProjectName(userInput) },
        status: 'pending'
      });
    }

    if (lowercaseInput.includes('assign task') || lowercaseInput.includes('create task')) {
      actions.push({
        type: 'assign_task',
        parameters: { description: userInput },
        status: 'pending'
      });
    }

    if (lowercaseInput.includes('search') || lowercaseInput.includes('query') || lowercaseInput.includes('find')) {
      actions.push({
        type: 'query_database',
        parameters: { query: userInput },
        status: 'pending'
      });
    }

    return actions;
  };

  const extractProjectName = (input: string): string => {
    const match = input.match(/create project[:\s]+([^,.\n]+)/i);
    return match ? match[1].trim() : 'New AI Generated Project';
  };

  const executeActions = async (actions: AIAction[]): Promise<AIAction[]> => {
    const executedActions = [];

    for (const action of actions) {
      try {
        setCurrentStep(`Executing: ${action.type.replace('_', ' ')}`);
        await new Promise(resolve => setTimeout(resolve, 1000));

        switch (action.type) {
          case 'create_project':
            // Call your project creation API here
            action.status = 'completed';
            action.parameters.result = `Project "${action.parameters.name}" created successfully`;
            break;
          
          case 'assign_task':
            // Call your task assignment API here
            action.status = 'completed';
            action.parameters.result = 'Task assigned successfully';
            break;
          
          case 'query_database':
            // Call your database query API here
            action.status = 'completed';
            action.parameters.result = 'Database query executed successfully';
            break;
          
          default:
            action.status = 'completed';
        }

        executedActions.push(action);
      } catch (error) {
        action.status = 'failed';
        executedActions.push(action);
      }
    }

    return executedActions;
  };

  const sendMessage = async () => {
    if (!input.trim()) return;

    const userMessage: AIMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsProcessing(true);
    setProgress(0);
    setHasApiKeyError(false);

    try {
      // Detect and execute actions
      const actions = detectActions(input);
      const executedActions = await executeActions(actions);

      setCurrentStep('Generating AI response...');
      setProgress(50);

      let aiResponse = '';
      let enhanced = false;

      try {
        // Try LangChain first
        const langChainResponse = await sendFuturisticMessage(input);
        if (langChainResponse) {
          aiResponse = langChainResponse.content;
          enhanced = langChainResponse.enhanced || false;
        }
      } catch (error) {
        console.warn('LangChain failed, falling back to basic AI:', error);
      }

      // Fallback to basic AI assistant if LangChain failed
      if (!aiResponse) {
        const { data, error } = await supabase.functions.invoke('ai-assistant', {
          body: {
            message: input,
            conversation_history: messages.slice(-10),
            actions: executedActions
          }
        });

        if (error) throw error;
        aiResponse = data.response || 'I understand your request and have processed it.';
      }

      setProgress(100);

      const aiMessage: AIMessage = {
        id: `ai-${Date.now()}`,
        role: 'assistant',
        content: enhanced ? `[AI-ENHANCED] ${aiResponse}` : aiResponse,
        timestamp: new Date(),
        type: 'analysis',
        actions: executedActions.map(action => ({
          type: action.type,
          status: action.status,
          result: action.parameters.result
        }))
      };

      setMessages(prev => [...prev, aiMessage]);

      // Save interaction to database
      await supabase.from('ai_interactions').insert({
        user_id: userProfile?.id,
        role: 'user',
        message: input,
        query: input,
        type: 'user_input'
      });

      await supabase.from('ai_interactions').insert({
        user_id: userProfile?.id,
        role: 'assistant',
        message: aiMessage.content,
        response: aiMessage.content,
        type: 'analysis',
        actions: aiMessage.actions || [],
        metadata: { model_used: 'ai-assistant', actions_executed: executedActions.length }
      });

    } catch (error) {
      console.error('Error processing message:', error);
      
      const { content, showSettings } = handleAIError(error);
      
      const errorMessage: AIMessage = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content,
        timestamp: new Date(),
        type: 'error'
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
      setCurrentStep('');
      setProgress(0);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isProcessing) {
      sendMessage();
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsProcessing(true);
    setCurrentStep('Analyzing uploaded files...');

    try {
      for (const file of Array.from(files)) {
        const formData = new FormData();
        formData.append('file', file);

        const { data, error } = await supabase.functions.invoke('ai-file-analyzer', {
          body: formData
        });

        if (error) {
          const { content } = handleAIError(error);
          
          const errorMessage: AIMessage = {
            id: `file-error-${Date.now()}`,
            role: 'assistant',
            content: `📄 **File Analysis Failed: ${file.name}**\n\n${content}`,
            timestamp: new Date(),
            type: 'error'
          };

          setMessages(prev => [...prev, errorMessage]);
          continue;
        }

        const analysisMessage: AIMessage = {
          id: `analysis-${Date.now()}`,
          role: 'assistant',
          content: `📄 **File Analysis: ${file.name}**\n\n${data.analysis || 'File processed successfully'}`,
          timestamp: new Date(),
          type: 'analysis'
        };

        setMessages(prev => [...prev, analysisMessage]);
      }

      if (!hasApiKeyError) {
        toast({
          title: "✅ Files Analyzed",
          description: `Successfully analyzed ${files.length} file(s)`,
        });
      }
    } catch (error) {
      console.error('Error analyzing files:', error);
      handleAIError(error);
    } finally {
      setIsProcessing(false);
      setCurrentStep('');
    }
  };

  const startListening = () => {
    if (recognition) {
      setIsListening(true);
      recognition.start();
    }
  };

  const stopListening = () => {
    if (recognition) {
      setIsListening(false);
      recognition.stop();
    }
  };

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'create_project': return <ClipboardList className="h-3 w-3" />;
      case 'assign_task': return <Users className="h-3 w-3" />;
      case 'query_database': return <Database className="h-3 w-3" />;
      case 'analyze_file': return <FileText className="h-3 w-3" />;
      default: return <Zap className="h-3 w-3" />;
    }
  };

  const getMessageTypeStyle = (type?: string) => {
    switch (type) {
      case 'system': return 'bg-muted border-border';
      case 'analysis': return 'bg-muted border-border';
      case 'error': return 'bg-destructive/10 border-destructive/30';
      default: return 'bg-muted border-border';
    }
  };

  const getMessageIcon = (type?: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-4 w-4 text-destructive" />;
      case 'system': return <Settings className="h-4 w-4 text-muted-foreground" />;
      default: return null;
    }
  };

  return (
    <div className="relative h-[900px] overflow-hidden rounded-[30px] border border-border bg-card backdrop-blur-xl" data-aos="fade-up">
      {/* Neural Network Background */}
      <div className="absolute inset-0 opacity-20">
        <NeuralNetwork />
      </div>

      {/* Grid Overlay */}
      <div className="absolute inset-0 opacity-10" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23374151' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      <div className="relative z-10 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border bg-muted/50 backdrop-blur-sm" data-aos="slide-down">
          <div className="flex items-center gap-4" data-aos="fade-right">
            <div className="relative">
              <div className={`h-12 w-12 rounded-full flex items-center justify-center ${hasApiKeyError ? 'bg-gradient-to-r from-destructive to-yellow-500' : 'bg-gradient-to-r from-primary to-secondary'}`}>
                {hasApiKeyError ? (
                  <Key className={`h-6 w-6 text-primary-foreground ${isProcessing ? 'animate-pulse' : ''}`} />
                ) : (
                  <Brain className={`h-6 w-6 text-primary-foreground ${isProcessing ? 'animate-pulse' : ''}`} />
                )}
              </div>
              <div className={`absolute -inset-2 rounded-full border animate-pulse ${hasApiKeyError ? 'border-destructive/50' : 'border-primary/50'}`}></div>
            </div>
            <div>
              <h2 className={`text-2xl font-bold bg-gradient-to-r bg-clip-text text-transparent ${hasApiKeyError ? 'from-destructive to-yellow-500' : 'from-primary to-secondary'}`}>
                AI Assistant
              </h2>
              <p className={`text-sm ${hasApiKeyError ? 'text-destructive' : 'text-muted-foreground'}`}>
                {hasApiKeyError ? 'Configuration Required' : 'Advanced Intelligence System'}
              </p>
            </div>
          </div>
          <div className="flex gap-3" data-aos="fade-left">
            {hasApiKeyError && userProfile?.role === 'admin' && (
              <Button
                variant="outline"
                onClick={() => window.open('/dashboard/admin/api-keys', '_blank')}
                className="bg-destructive/10 border-destructive/50 text-destructive hover:bg-destructive/20 transition-all duration-300"
              >
                <Key className="h-4 w-4 mr-2" />
                Configure API Key
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isProcessing || hasApiKeyError}
              className="bg-primary/10 border-primary/50 text-primary hover:bg-primary/20 transition-all duration-300"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Files
            </Button>
            <Button
              variant="outline"
              onClick={isListening ? stopListening : startListening}
              disabled={isProcessing || hasApiKeyError}
              className={`${isListening ? 'bg-destructive/10 border-destructive/50 text-destructive' : 'bg-secondary/10 border-secondary/50 text-secondary'} hover:bg-opacity-20 transition-all duration-300`}
            >
              {isListening ? <MicOff className="h-4 w-4 mr-2" /> : <Mic className="h-4 w-4 mr-2" />}
              {isListening ? 'Stop' : 'Voice'}
            </Button>
            {isProcessing && (
              <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/50 animate-pulse">
                <Activity className="h-3 w-3 mr-1 animate-spin" />
                Processing...
              </Badge>
            )}
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          multiple
          accept=".pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx"
          onChange={handleFileUpload}
        />

        {/* Processing Progress */}
        {isProcessing && (
          <div className="px-6 py-4 border-b border-border bg-muted/50" data-aos="slide-down">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Loader className="h-4 w-4 animate-spin text-primary" />
                  <span className="text-sm text-foreground">{currentStep}</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
              <span className="text-xs text-muted-foreground">{progress}%</span>
            </div>
          </div>
        )}

        {/* Messages Area */}
        <ScrollArea className="flex-1 p-6">
          {messages.length === 0 ? (
            <div className="text-center text-muted-foreground py-16" data-aos="fade-up">
              <div className="relative mb-8">
                {hasApiKeyError ? (
                  <Key className="h-24 w-24 mx-auto mb-6 text-destructive/50" />
                ) : (
                  <Bot className="h-24 w-24 mx-auto mb-6 text-primary/50" />
                )}
                <div className={`absolute -inset-4 border-2 rounded-full ${hasApiKeyError ? 'border-destructive/30' : 'border-primary/30'}`}></div>
              </div>
              <h3 className={`text-2xl font-bold mb-4 bg-gradient-to-r bg-clip-text text-transparent ${hasApiKeyError ? 'from-destructive to-yellow-500' : 'from-primary to-secondary'}`}>
                {hasApiKeyError ? 'API Configuration Required' : 'AI Assistant Ready'}
              </h3>
              <p className="mb-6">
                {hasApiKeyError 
                  ? 'AI functionality requires OpenAI API key configuration'
                  : 'Advanced AI assistant ready for your commands'
                }
              </p>
              {!hasApiKeyError && (
                <div className="grid grid-cols-2 gap-4 max-w-md mx-auto text-sm">
                  <div className="flex items-center gap-2 p-3 rounded-lg bg-primary/10 border border-primary/20" data-aos="zoom-in" data-aos-delay="100">
                    <ClipboardList className="h-4 w-4 text-primary" />
                    Create Projects
                  </div>
                  <div className="flex items-center gap-2 p-3 rounded-lg bg-secondary/10 border border-secondary/20" data-aos="zoom-in" data-aos-delay="200">
                    <Users className="h-4 w-4 text-secondary" />
                    Manage Tasks
                  </div>
                  <div className="flex items-center gap-2 p-3 rounded-lg bg-accent/10 border border-accent/20" data-aos="zoom-in" data-aos-delay="300">
                    <FileText className="h-4 w-4 text-accent-foreground" />
                    Analyze Files
                  </div>
                  <div className="flex items-center gap-2 p-3 rounded-lg bg-muted border border-border" data-aos="zoom-in" data-aos-delay="400">
                    <Database className="h-4 w-4 text-muted-foreground" />
                    Query Data
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              {messages.map((message, index) => (
                <div
                  key={message.id}
                  className={`flex gap-4 ${
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  }`}
                  data-aos="fade-up"
                  data-aos-delay={index * 50}
                >
                  {message.role === 'assistant' && (
                    <div className="relative">
                      <div className={`h-10 w-10 rounded-full flex items-center justify-center ${message.type === 'error' ? 'bg-gradient-to-r from-destructive to-yellow-500' : 'bg-gradient-to-r from-primary to-secondary'}`}>
                        {message.type === 'error' ? (
                          <AlertTriangle className="h-5 w-5 text-primary-foreground" />
                        ) : (
                          <Bot className="h-5 w-5 text-primary-foreground" />
                        )}
                      </div>
                      <div className={`absolute -inset-1 rounded-full border animate-pulse ${message.type === 'error' ? 'border-destructive/30' : 'border-primary/30'}`}></div>
                    </div>
                  )}
                  
                  <div
                    className={`max-w-[80%] p-4 rounded-2xl border ${
                      message.role === 'user'
                        ? 'bg-primary/10 border-primary/30 text-primary-foreground ml-auto'
                        : getMessageTypeStyle(message.type)
                    }`}
                  >
                    {message.role === 'assistant' && message.type && (
                      <div className="flex items-center gap-2 mb-2">
                        {getMessageIcon(message.type)}
                        <span className="text-xs font-medium uppercase tracking-wide">
                          {message.type === 'error' ? 'System Error' : message.type}
                        </span>
                      </div>
                    )}
                    
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {message.content}
                    </div>
                    
                    {message.actions && message.actions.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-border/50">
                        <div className="text-xs font-medium mb-2">Actions Performed:</div>
                        <div className="space-y-1">
                          {message.actions.map((action, idx) => (
                            <div key={idx} className="flex items-center gap-2 text-xs">
                              {getActionIcon(action.type)}
                              <span className="capitalize">{action.type.replace('_', ' ')}</span>
                              {action.status === 'completed' ? (
                                <CheckCircle className="h-3 w-3 text-green-500" />
                              ) : (
                                <AlertTriangle className="h-3 w-3 text-yellow-500" />
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div className="text-xs text-muted-foreground mt-2">
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                  
                  {message.role === 'user' && (
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                      <User className="h-5 w-5 text-white" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {/* Input Area */}
        <div className="p-6 border-t border-border bg-muted/50" data-aos="slide-up">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={hasApiKeyError ? "AI functionality requires API key configuration..." : "Ask me anything about your work..."}
                disabled={isProcessing || hasApiKeyError}
                className="pr-12 h-12 text-base rounded-full border-2 border-primary/20 focus:border-primary/50 bg-background/50 backdrop-blur-sm"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex gap-1">
                {isListening && (
                  <div className="h-6 w-6 rounded-full bg-destructive/20 flex items-center justify-center">
                    <div className="h-2 w-2 rounded-full bg-destructive animate-pulse"></div>
                  </div>
                )}
              </div>
            </div>
            <Button
              onClick={sendMessage}
              disabled={isProcessing || !input.trim() || hasApiKeyError}
              size="lg"
              className="h-12 px-6 rounded-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 transition-all duration-300"
            >
              {isProcessing ? (
                <Loader className="h-5 w-5 animate-spin" />
              ) : (
                <Send className="h-5 w-5" />
              )}
            </Button>
          </div>
          
          {(hasApiKeyError || showApiKeyManager) && (
            <div className="mt-4">
              {showApiKeyManager ? (
                <div className="space-y-4">
                  <APIKeyManager
                    onApiKeyConfigured={(configured) => {
                      setIsApiKeyConfigured(configured);
                      setHasApiKeyError(!configured);
                      setShowApiKeyManager(!configured);
                    }}
                  />
                  {isApiKeyConfigured && (
                    <Button
                      onClick={() => setShowApiKeyManager(false)}
                      className="w-full"
                    >
                      Continue with AI Chat
                    </Button>
                  )}
                </div>
              ) : (
                <div className="p-3 bg-destructive/10 border border-destructive/30 rounded-lg text-center">
                  <p className="text-sm text-destructive mb-2">
                    🔑 AI functionality is currently unavailable - API key required
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowApiKeyManager(true)}
                    className="text-destructive border-destructive/50 hover:bg-destructive/10"
                  >
                    <Key className="h-4 w-4 mr-2" />
                    Configure API Key
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
