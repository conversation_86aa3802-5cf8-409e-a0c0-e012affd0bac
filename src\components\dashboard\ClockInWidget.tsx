
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Clock, MapPin, Loader2 } from 'lucide-react';
import { useClockIn } from '@/hooks/useClockIn';
import { Badge } from '@/components/ui/badge';

export const ClockInWidget = () => {
  const { loading, currentSession, clockIn, clockOut, getCurrentSession } = useClockIn();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [sessionDuration, setSessionDuration] = useState('');

  useEffect(() => {
    getCurrentSession();
    
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      
      if (currentSession?.clock_in) {
        const start = new Date(currentSession.clock_in);
        const now = new Date();
        const diff = now.getTime() - start.getTime();
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        setSessionDuration(`${hours}h ${minutes}m`);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [currentSession?.clock_in]);

  const handleClockAction = async () => {
    try {
      if (currentSession) {
        await clockOut(currentSession.id);
      } else {
        await clockIn();
      }
    } catch (error) {
      console.error('Clock action failed:', error);
    }
  };

  return (
    <Card className="glass-card floating-animation" data-aos="fade-up">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg modern-heading">
          <div className="p-2 rounded-full bg-primary/10">
            <Clock className="h-5 w-5 text-primary" />
          </div>
          Time Tracking
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center">
          <div className="text-3xl font-mono font-bold gradient-text pulse-glow">
            {currentTime.toLocaleTimeString()}
          </div>
          <div className="text-sm text-muted-foreground font-medium">
            {currentTime.toLocaleDateString()}
          </div>
        </div>

        {currentSession && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Status:</span>
              <Badge variant="default" className="bg-green-500">
                Clocked In
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Duration:</span>
              <span className="font-mono text-sm font-medium">
                {sessionDuration}
              </span>
            </div>
            {currentSession.location_address && (
              <div className="flex items-start gap-2">
                <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {currentSession.location_address}
                </span>
              </div>
            )}
          </div>
        )}

        <Button
          onClick={handleClockAction}
          disabled={loading}
          className={`w-full rounded-[30px] font-semibold shadow-xl transition-all duration-300 transform hover:scale-105 ${
            currentSession
              ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-red-500/30 hover:shadow-red-500/50'
              : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 shadow-green-500/30 hover:shadow-green-500/50'
          }`}
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : currentSession ? (
            'Clock Out'
          ) : (
            'Clock In'
          )}
        </Button>
      </CardContent>
    </Card>
  );
};
