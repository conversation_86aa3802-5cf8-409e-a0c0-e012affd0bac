import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/components/auth/AuthProvider";
import { format } from "date-fns";
import { FileText, Plus, Eye, Clock, CheckCircle, XCircle, AlertCircle, TrendingUp, Send } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { MemoAPI, MemoFormData } from "@/lib/memo-api";
import { SuccessModal } from "@/components/ui/success-modal";

interface Memo {
  id: string;
  title: string;
  subject: string;
  content: string;
  created_at: string;
  created_by: string;
  status: string;
  to_recipient: string;
  total_amount: number;
}

export function MemoWidget() {
  const navigate = useNavigate();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [formData, setFormData] = useState<MemoFormData>({
    title: "",
    subject: "",
    content: "",
    to_recipient: "",
    purpose: "",
    account_details: "",
    total_amount: 0,
    memo_date: new Date().toISOString().split('T')[0],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { userProfile } = useAuth();
  const { toast } = useToast();

  // Fetch staff memos using the new API
  const { data: memos, isLoading, refetch } = useQuery({
    queryKey: ['staff-memos-widget', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];
      const { data, error } = await MemoAPI.getUserMemos(userProfile.id, 5);
      if (error) throw error;
      return data as Memo[];
    },
    enabled: !!userProfile?.id,
  });

  // Fetch profiles for recipient selection
  const { data: profiles } = useQuery({
    queryKey: ['profiles-for-memo'],
    queryFn: async () => {
      const { data, error } = await MemoAPI.getProfiles();
      if (error) throw error;
      return data;
    },
  });

  // Calculate memo statistics
  const memoStats = {
    total: memos?.length || 0,
    pending: memos?.filter(m => m.status === 'pending' || !m.status).length || 0,
    approved: memos?.filter(m => m.status === 'approved').length || 0,
    rejected: memos?.filter(m => m.status === 'rejected').length || 0,
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'bg-green-500/20 text-green-500';
      case 'rejected':
        return 'bg-red-500/20 text-red-500';
      default:
        return 'bg-yellow-500/20 text-yellow-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return <CheckCircle className="h-3 w-3" />;
      case 'rejected':
        return <XCircle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  const handleFormChange = (field: keyof MemoFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      title: "",
      subject: "",
      content: "",
      to_recipient: "",
      purpose: "",
      account_details: "",
      total_amount: 0,
      memo_date: new Date().toISOString().split('T')[0],
    });
  };

  const handleSubmitMemo = async () => {
    if (!userProfile?.id) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive",
      });
      return;
    }

    if (!formData.title.trim() || !formData.content.trim() || !formData.to_recipient.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields (Title, Content, and Recipient)",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const { data, error } = await MemoAPI.createMemo(formData, userProfile.id);

      if (error) throw error;

      resetForm();
      setIsCreateDialogOpen(false);
      setShowSuccessModal(true);
      refetch();
    } catch (error: any) {
      console.error("Error creating memo:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to create memo. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="glassmorphism border border-primary/20 h-fit">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-[#0FA0CE]" />
            <span>My Memos</span>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="h-8 w-8 p-0">
                <Plus className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-lg glassmorphism">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Send className="h-5 w-5 text-primary" />
                  Create New Memo
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Title *</Label>
                    <Input
                      id="title"
                      placeholder="Memo title"
                      value={formData.title}
                      onChange={(e) => handleFormChange('title', e.target.value)}
                      disabled={isSubmitting}
                    />
                  </div>
                  <div>
                    <Label htmlFor="subject">Subject</Label>
                    <Input
                      id="subject"
                      placeholder="Subject (optional)"
                      value={formData.subject}
                      onChange={(e) => handleFormChange('subject', e.target.value)}
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="to_recipient">To (Recipient) *</Label>
                  <Select
                    value={formData.to_recipient}
                    onValueChange={(value) => handleFormChange('to_recipient', value)}
                    disabled={isSubmitting}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select recipient..." />
                    </SelectTrigger>
                    <SelectContent>
                      {profiles?.filter(profile => profile.full_name && profile.full_name.trim() !== '').map((profile) => (
                        <SelectItem key={profile.id} value={profile.full_name}>
                          {profile.full_name} ({profile.role})
                        </SelectItem>
                      ))}
                      {profiles?.filter(profile => !profile.full_name || profile.full_name.trim() === '').map((profile) => (
                        <SelectItem key={profile.id} value={profile.email || profile.id}>
                          {profile.email} ({profile.role})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="content">Content *</Label>
                  <Textarea
                    id="content"
                    placeholder="Memo content and details"
                    value={formData.content}
                    onChange={(e) => handleFormChange('content', e.target.value)}
                    className="min-h-[120px]"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="total_amount">Amount (₦)</Label>
                    <Input
                      id="total_amount"
                      type="number"
                      placeholder="0.00"
                      value={formData.total_amount}
                      onChange={(e) => handleFormChange('total_amount', parseFloat(e.target.value) || 0)}
                      disabled={isSubmitting}
                    />
                  </div>
                  <div>
                    <Label htmlFor="memo_date">Date</Label>
                    <Input
                      id="memo_date"
                      type="date"
                      value={formData.memo_date}
                      onChange={(e) => handleFormChange('memo_date', e.target.value)}
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="account_details">Account Details (Optional)</Label>
                  <Textarea
                    id="account_details"
                    placeholder="Bank account details, payment instructions, etc."
                    value={formData.account_details}
                    onChange={(e) => handleFormChange('account_details', e.target.value)}
                    className="min-h-[80px]"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={handleSubmitMemo}
                    disabled={isSubmitting || !formData.title.trim() || !formData.content.trim() || !formData.to_recipient.trim()}
                    className="flex-1"
                  >
                    {isSubmitting ? "Creating..." : "Create Memo"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsCreateDialogOpen(false);
                      resetForm();
                    }}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Statistics */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 rounded-lg p-3">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">Total</span>
            </div>
            <p className="text-2xl font-bold text-blue-600">{memoStats.total}</p>
          </div>
          <div className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 rounded-lg p-3">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium">Pending</span>
            </div>
            <p className="text-2xl font-bold text-yellow-600">{memoStats.pending}</p>
          </div>
        </div>

        {/* Recent Memos */}
        <div>
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Recent Memos
          </h4>
          {isLoading ? (
            <div className="space-y-2">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-12 bg-muted/50 rounded animate-pulse" />
              ))}
            </div>
          ) : memos && memos.length > 0 ? (
            <div className="space-y-2">
              {memos.slice(0, 3).map((memo) => (
                <div key={memo.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm truncate">{memo.subject || memo.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {format(new Date(memo.created_at), 'MMM dd, yyyy')}
                    </p>
                  </div>
                  <Badge className={`${getStatusColor(memo.status)} text-xs flex items-center gap-1`}>
                    {getStatusIcon(memo.status)}
                    {memo.status || 'pending'}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No memos found</p>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={() => navigate('/dashboard/staff?tab=memos')}
          >
            <Eye className="h-4 w-4 mr-2" />
            View All
          </Button>
          <Button 
            size="sm" 
            className="flex-1"
            onClick={() => setIsCreateDialogOpen(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Memo
          </Button>
        </div>
      </CardContent>

      {/* Success Modal */}
      <SuccessModal
        open={showSuccessModal}
        onOpenChange={setShowSuccessModal}
        title="Memo Created Successfully!"
        description="Your memo has been submitted and is pending approval. You will be notified once it's reviewed."
        variant="success"
        showConfetti={true}
      />
    </Card>
  );
}