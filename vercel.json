{"name": "ctnl-ai-work-board", "version": 2, "rewrites": [{"source": "/((?!assets|api|_next|favicon.ico|robots.txt|sitemap.xml|user-manual.html).*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)\\.css", "headers": [{"key": "Content-Type", "value": "text/css"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)\\.js", "headers": [{"key": "Content-Type", "value": "application/javascript"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}