
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import type {
    EnhancedProfile,
    UserRole
} from "@/types/auth";
import {
    DEFAULT_ROLE,
    DEFAULT_STATUS,
    isValidRole,
    isValidStatus
} from "@/types/auth";
import { Session, User } from "@supabase/supabase-js";
import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";

export const useSupabaseAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<EnhancedProfile | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const isMountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Helper function to validate and convert profile data
  const validateProfile = useCallback((profileData: any): EnhancedProfile | null => {
    if (!profileData) return null;

    // Validate role
    const role = profileData.role && isValidRole(profileData.role)
      ? profileData.role as UserRole
      : DEFAULT_ROLE;

    // Validate status
    const status = profileData.status && isValidStatus(profileData.status)
      ? profileData.status
      : DEFAULT_STATUS;

    // Return validated profile
    return {
      ...profileData,
      role,
      status,
      account_type: profileData.account_type || role
    } as EnhancedProfile;
  }, []);

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          // Fetch user profile with role information
          setTimeout(async () => {
            try {
              // First try to get existing profile by user_id
              const { data: profile, error } = await supabase
                .from('profiles')
                .select('*')
                .eq('user_id', session.user.id)
                .single();

              if (error && error.code === 'PGRST116') {
                // Profile doesn't exist, create one
                console.log('Profile not found, creating new profile for user:', session.user.email);
                const newProfile = {
                  user_id: session.user.id,
                  email: session.user.email || '',
                  full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
                  role: 'staff',
                  account_type: 'staff',
                  status: 'active'
                };

                const { data: createdProfile, error: createError } = await supabase
                  .from('profiles')
                  .insert([newProfile])
                  .select()
                  .single();

                if (createError) {
                  console.error('Error creating profile:', createError);
                } else {
                  const validatedProfile = validateProfile(createdProfile);
                  setUserProfile(validatedProfile);
                  if (validatedProfile?.role) {
                    localStorage.setItem('userRole', validatedProfile.role);
                    localStorage.setItem('accountType', validatedProfile.account_type || validatedProfile.role);
                  }
                }
              } else if (error) {
                console.error('Error fetching profile:', error);
              } else {
                const validatedProfile = validateProfile(profile);
                setUserProfile(validatedProfile);
                // Store role information for easy access
                if (validatedProfile?.role) {
                  localStorage.setItem('userRole', validatedProfile.role);
                  localStorage.setItem('accountType', validatedProfile.account_type || validatedProfile.role);
                }
              }
            } catch (err) {
              console.error('Profile fetch error:', err);
            }
          }, 0);
        } else {
          setUserProfile(null);
          localStorage.removeItem('userRole');
          localStorage.removeItem('accountType');
        }
        setLoading(false);
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        // Check if it's an invalid credentials error (user doesn't exist)
        if (error.message.includes('Invalid login credentials') ||
            error.message.includes('Email not confirmed') ||
            error.message.includes('User not found')) {
          toast({
            title: "Account Not Found",
            description: "No account found with this email. Please sign up first or check your email address.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Authentication Error",
            description: error.message,
            variant: "destructive",
          });
        }
        throw error;
      }

      toast({
        title: "Success",
        description: "Successfully signed in!",
      });

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      setLoading(true);
      const redirectUrl = `${window.location.origin}/`;
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: metadata
        }
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Please check your email to verify your account!",
      });

      return { data, error: null };
    } catch (error: any) {
      toast({
        title: "Sign Up Error",
        description: error.message,
        variant: "destructive",
      });
      return { data: null, error };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      localStorage.removeItem('userRole');
      localStorage.removeItem('accountType');
      
      navigate('/login');
      toast({
        title: "Signed Out",
        description: "You have been successfully signed out.",
      });
    } catch (error: any) {
      toast({
        title: "Sign Out Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const updateProfile = async (updates: any) => {
    try {
      if (!user) throw new Error('No user logged in');
      
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);
      
      if (error) throw error;
      
      // Refresh profile data
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      const validatedProfile = validateProfile(profile);
      setUserProfile(validatedProfile);
      
      toast({
        title: "Success",
        description: "Profile updated successfully!",
      });
      
      return { error: null };
    } catch (error: any) {
      toast({
        title: "Update Error",
        description: error.message,
        variant: "destructive",
      });
      return { error };
    }
  };

  return {
    user,
    session,
    userProfile,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    isAuthenticated: !!user,
    isAdmin: userProfile?.role === 'admin',
    isManager: userProfile?.role === 'manager',
    isStaff: userProfile?.role === 'staff',
    isAccountant: userProfile?.role === 'accountant',
    isHR: userProfile?.role === 'hr',
    isStaffAdmin: userProfile?.role === 'staff-admin',
    // Helper functions for role checking
    hasRole: (role: UserRole) => userProfile?.role === role,
    hasAnyRole: (roles: UserRole[]) => roles.includes(userProfile?.role as UserRole),
    canManage: userProfile?.role === 'admin' || userProfile?.role === 'manager',
  };
};
