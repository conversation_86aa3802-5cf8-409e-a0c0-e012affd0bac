import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { RefreshCw, Trash2 } from "lucide-react";
import { clearAllCacheAndReload } from "@/utils/cacheManager";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface QuickCacheClearButtonProps {
  variant?: "default" | "outline" | "ghost" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  showText?: boolean;
}

export const QuickCacheClearButton = ({ 
  variant = "ghost", 
  size = "sm",
  showText = false 
}: QuickCacheClearButtonProps) => {
  const { toast } = useToast();
  const [isClearing, setIsClearing] = useState(false);

  const handleQuickClear = async () => {
    setIsClearing(true);
    try {
      const success = await clearAllCacheAndReload();
      if (success) {
        toast({
          title: "Cache Cleared",
          description: "System cache cleared. Redirecting to prevent reload issues...",
          variant: "default",
        });
        
        // Redirect to auth page to prevent infinite reload
        setTimeout(() => {
          window.location.href = '/auth';
        }, 1500);
      } else {
        throw new Error('Cache clearing failed');
      }
    } catch (error) {
      console.error('Quick cache clear error:', error);
      toast({
        title: "Cache Clear Failed",
        description: "Please try refreshing the page manually (Ctrl+F5)",
        variant: "destructive",
      });
    } finally {
      setIsClearing(false);
    }
  };

  const ButtonContent = () => (
    <>
      {isClearing ? (
        <RefreshCw className="h-4 w-4 animate-spin" />
      ) : (
        <Trash2 className="h-4 w-4" />
      )}
      {showText && (
        <span className="ml-2">
          {isClearing ? "Clearing..." : "Clear Cache"}
        </span>
      )}
    </>
  );

  if (showText) {
    return (
      <Button
        variant={variant}
        size={size}
        onClick={handleQuickClear}
        disabled={isClearing}
        className="flex items-center gap-2"
      >
        <ButtonContent />
      </Button>
    );
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={variant}
          size={size}
          onClick={handleQuickClear}
          disabled={isClearing}
          className="flex items-center"
        >
          <ButtonContent />
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{isClearing ? "Clearing cache..." : "Clear system cache"}</p>
      </TooltipContent>
    </Tooltip>
  );
};
