
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download, Plus } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useFinancialData } from "@/hooks/useFinancialData";
import { FinancialOverviewCards } from "./FinancialOverviewCards";
import { FinancialRevenueTab } from "./FinancialRevenueTab";
import { FinancialExpensesTab } from "./FinancialExpensesTab";
import { AddTransactionDialog } from "./AddTransactionDialog";
import { TransactionList } from "./TransactionList";

export const FinancialManagement = () => {
  const [dateRange, setDateRange] = useState("30");
  const [isTransactionDialogO<PERSON>, setIsTransactionDialogOpen] = useState(false);

  const {
    invoices,
    expenses,
    accountsInvoices,
    financialSummary,
    isLoading
  } = useFinancialData(dateRange);

  const handleExportReport = () => {
    const reportData = {
      period: `Last ${dateRange} days`,
      generatedAt: new Date().toISOString(),
      summary: financialSummary,
      invoices: invoices.slice(0, 10),
      expenses: expenses.slice(0, 10)
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `financial-report-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Financial Management</h2>
          <p className="text-muted-foreground">Track revenue, expenses, and financial performance</p>
        </div>
        <div className="flex gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleExportReport} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button onClick={() => setIsTransactionDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Transaction
          </Button>
        </div>
      </div>

      {/* Financial Overview Cards */}
      <FinancialOverviewCards summary={financialSummary} isLoading={isLoading} />

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="expenses">Expenses</TabsTrigger>
          <TabsTrigger value="accounts">Accounts</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-6 lg:grid-cols-2">
            <TransactionList limit={5} title="Recent Transactions" />

            <Card>
              <CardHeader>
                <CardTitle>Financial Health</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Total Revenue</span>
                    <span className="text-green-600 font-bold">₦{(financialSummary?.total_revenue || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Total Expenses</span>
                    <span className="text-red-600 font-bold">₦{(financialSummary?.total_expenses || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Net Profit</span>
                    <span className={`font-bold ${(financialSummary?.net_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      ₦{(financialSummary?.net_profit || 0).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Profit Margin</span>
                    <span className={`font-bold ${(financialSummary?.net_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {financialSummary?.total_revenue && financialSummary.total_revenue > 0 
                        ? Math.round(((financialSummary.net_profit || 0) / financialSummary.total_revenue) * 100) 
                        : 0}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="invoices">
          <FinancialRevenueTab invoices={invoices} isLoading={isLoading} />
        </TabsContent>

        <TabsContent value="expenses">
          <FinancialExpensesTab expenses={expenses} isLoading={isLoading} />
        </TabsContent>

        <TabsContent value="accounts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Accounts Payable</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {accountsInvoices?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No accounts payable found
                  </div>
                ) : (
                  accountsInvoices?.map((invoice) => (
                    <Card key={invoice.id} className="p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-semibold">{invoice.vendor_name}</h3>
                          <p className="text-sm text-muted-foreground">
                            Invoice: {invoice.invoice_number}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold">₦{invoice.amount.toLocaleString()}</p>
                          <p className="text-sm text-muted-foreground">{invoice.payment_status}</p>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Profit & Loss Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Revenue</span>
                    <span className="font-bold text-green-600">₦{(financialSummary?.total_revenue || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Expenses</span>
                    <span className="font-bold text-red-600">₦{(financialSummary?.total_expenses || 0).toLocaleString()}</span>
                  </div>
                  <hr />
                  <div className="flex justify-between">
                    <span>Net Profit</span>
                    <span className={`font-bold text-lg ${(financialSummary?.net_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      ₦{(financialSummary?.net_profit || 0).toLocaleString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Key Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Active Invoices</span>
                    <span className="font-bold">{invoices.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Pending Payments</span>
                    <span className="font-bold">{financialSummary?.pending_invoices_count || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Approved Expenses</span>
                    <span className="font-bold">{financialSummary?.approved_expenses_count || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Add Transaction Dialog */}
      <AddTransactionDialog
        isOpen={isTransactionDialogOpen}
        onOpenChange={setIsTransactionDialogOpen}
        onTransactionAdded={() => {
          // Refresh financial data when a transaction is added
          window.location.reload();
        }}
      />
    </div>
  );
};
