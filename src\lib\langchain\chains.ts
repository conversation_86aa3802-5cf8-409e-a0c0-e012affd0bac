/**
 * LangChain Chains and Workflows
 * Specialized chains for different use cases in the AI Workboard system
 */

import { BaseChain } from 'langchain/chains';
import { LLMChain } from 'langchain/chains';
import { SequentialChain } from 'langchain/chains';
import { PromptTemplate } from '@langchain/core/prompts';
import { Document } from '@langchain/core/documents';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { langChainConfig } from './config';
import { ragSystem } from './rag-system';
import { supabase } from '../supabase';

export interface ChainInput {
  [key: string]: any;
}

export interface ChainOutput {
  [key: string]: any;
}

/**
 * Document Summarization Chain
 */
export class DocumentSummarizationChain extends BaseChain {
  private llmChain: LLMChain;

  constructor(model?: BaseChatModel) {
    super();
    const llm = model || langChainConfig.getDefaultModel();
    
    const prompt = PromptTemplate.fromTemplate(`
Please provide a comprehensive summary of the following document:

Document Title: {title}
Document Content: {content}

Summary Requirements:
- Provide a concise overview of the main points
- Highlight key findings or conclusions
- Include important details and data
- Structure the summary with clear sections
- Keep the summary between 200-500 words

Summary:`);

    this.llmChain = new LLMChain({ llm, prompt });
  }

  get inputKeys(): string[] {
    return ['title', 'content'];
  }

  get outputKeys(): string[] {
    return ['summary'];
  }

  async _call(values: ChainInput): Promise<ChainOutput> {
    const result = await this.llmChain.call(values);
    return { summary: result.text };
  }

  _chainType(): string {
    return 'document_summarization_chain';
  }
}

/**
 * Report Analysis Chain
 */
export class ReportAnalysisChain extends BaseChain {
  private analysisChain: LLMChain;
  private insightsChain: LLMChain;
  private sequentialChain: SequentialChain;

  constructor(model?: BaseChatModel) {
    super();
    const llm = model || langChainConfig.getDefaultModel();

    // Analysis step
    const analysisPrompt = PromptTemplate.fromTemplate(`
Analyze the following report data:

Report Type: {reportType}
Data: {data}
Time Period: {timePeriod}

Provide a detailed analysis including:
- Data trends and patterns
- Key metrics and KPIs
- Notable changes or anomalies
- Statistical insights

Analysis:`);

    this.analysisChain = new LLMChain({
      llm,
      prompt: analysisPrompt,
      outputKey: 'analysis',
    });

    // Insights step
    const insightsPrompt = PromptTemplate.fromTemplate(`
Based on the following analysis, provide actionable insights and recommendations:

Analysis: {analysis}

Provide:
- Key insights and takeaways
- Actionable recommendations
- Potential risks or opportunities
- Next steps for improvement

Insights:`);

    this.insightsChain = new LLMChain({
      llm,
      prompt: insightsPrompt,
      outputKey: 'insights',
    });

    // Sequential chain
    this.sequentialChain = new SequentialChain({
      chains: [this.analysisChain, this.insightsChain],
      inputVariables: ['reportType', 'data', 'timePeriod'],
      outputVariables: ['analysis', 'insights'],
    });
  }

  get inputKeys(): string[] {
    return ['reportType', 'data', 'timePeriod'];
  }

  get outputKeys(): string[] {
    return ['analysis', 'insights'];
  }

  async _call(values: ChainInput): Promise<ChainOutput> {
    return await this.sequentialChain.call(values);
  }

  _chainType(): string {
    return 'report_analysis_chain';
  }
}

/**
 * Q&A Chain with Context
 */
export class ContextualQAChain extends BaseChain {
  private llmChain: LLMChain;

  constructor(model?: BaseChatModel) {
    super();
    const llm = model || langChainConfig.getDefaultModel();

    const prompt = PromptTemplate.fromTemplate(`
Answer the following question using the provided context and your knowledge of the AI Workboard system.

Context: {context}
Question: {question}
User Role: {userRole}

Instructions:
- Provide accurate, helpful answers based on the context
- Consider the user's role when tailoring the response
- If the context doesn't contain enough information, use your general knowledge
- Be specific and actionable when possible
- Include relevant examples or next steps

Answer:`);

    this.llmChain = new LLMChain({ llm, prompt });
  }

  get inputKeys(): string[] {
    return ['context', 'question', 'userRole'];
  }

  get outputKeys(): string[] {
    return ['answer'];
  }

  async _call(values: ChainInput): Promise<ChainOutput> {
    // Enhance context with RAG if needed
    if (!values.context || values.context.length < 100) {
      try {
        const ragResponse = await ragSystem.query({
          question: values.question,
          maxResults: 3,
        });
        values.context = ragResponse.sources.map(doc => doc.pageContent).join('\n\n');
      } catch (error) {
        console.warn('Failed to enhance context with RAG:', error);
      }
    }

    const result = await this.llmChain.call(values);
    return { answer: result.text };
  }

  _chainType(): string {
    return 'contextual_qa_chain';
  }
}

/**
 * Multi-step Workflow Chain
 */
export class WorkflowChain extends BaseChain {
  private steps: LLMChain[];
  private sequentialChain: SequentialChain;

  constructor(steps: { name: string; prompt: string; outputKey: string }[], model?: BaseChatModel) {
    super();
    const llm = model || langChainConfig.getDefaultModel();

    this.steps = steps.map(step => new LLMChain({
      llm,
      prompt: PromptTemplate.fromTemplate(step.prompt),
      outputKey: step.outputKey,
    }));

    this.sequentialChain = new SequentialChain({
      chains: this.steps,
      inputVariables: ['input'],
      outputVariables: steps.map(step => step.outputKey),
    });
  }

  get inputKeys(): string[] {
    return ['input'];
  }

  get outputKeys(): string[] {
    return this.steps.map(step => step.outputKey);
  }

  async _call(values: ChainInput): Promise<ChainOutput> {
    return await this.sequentialChain.call(values);
  }

  _chainType(): string {
    return 'workflow_chain';
  }
}

/**
 * Chain Factory
 */
export class ChainFactory {
  private static model: BaseChatModel;

  public static setModel(model: BaseChatModel): void {
    ChainFactory.model = model;
  }

  public static createSummarizationChain(): DocumentSummarizationChain {
    return new DocumentSummarizationChain(ChainFactory.model);
  }

  public static createReportAnalysisChain(): ReportAnalysisChain {
    return new ReportAnalysisChain(ChainFactory.model);
  }

  public static createQAChain(): ContextualQAChain {
    return new ContextualQAChain(ChainFactory.model);
  }

  public static createCustomWorkflow(
    steps: { name: string; prompt: string; outputKey: string }[]
  ): WorkflowChain {
    return new WorkflowChain(steps, ChainFactory.model);
  }

  public static createDocumentProcessingWorkflow(): WorkflowChain {
    const steps = [
      {
        name: 'extract',
        prompt: 'Extract key information from this document: {input}',
        outputKey: 'extracted_info',
      },
      {
        name: 'categorize',
        prompt: 'Categorize this information: {extracted_info}',
        outputKey: 'category',
      },
      {
        name: 'summarize',
        prompt: 'Create a summary based on: {extracted_info} Category: {category}',
        outputKey: 'summary',
      },
    ];

    return new WorkflowChain(steps, ChainFactory.model);
  }

  public static createReportGenerationWorkflow(): WorkflowChain {
    const steps = [
      {
        name: 'gather_data',
        prompt: 'Identify what data is needed for this report: {input}',
        outputKey: 'data_requirements',
      },
      {
        name: 'analyze_data',
        prompt: 'Analyze the data requirements: {data_requirements}',
        outputKey: 'analysis',
      },
      {
        name: 'generate_report',
        prompt: 'Generate a comprehensive report based on: {analysis}',
        outputKey: 'report',
      },
    ];

    return new WorkflowChain(steps, ChainFactory.model);
  }
}

// Initialize with default model
ChainFactory.setModel(langChainConfig.getDefaultModel());

// Export commonly used chains
export const summarizationChain = ChainFactory.createSummarizationChain();
export const reportAnalysisChain = ChainFactory.createReportAnalysisChain();
export const qaChain = ChainFactory.createQAChain();
