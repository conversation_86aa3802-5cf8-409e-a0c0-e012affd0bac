import { SearchInput } from "@/components/ui/search";
import { ThemeSwitcher } from "@/components/ThemeSwitcher";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";
import { Bell, Search, User, BookOpen, ExternalLink } from "lucide-react";
import { useAuth } from "@/components/auth/AuthProvider";
import { useNotifications } from "@/hooks/useNotifications";
import { EnhancedNotificationCenter } from "@/components/notifications/EnhancedNotificationCenter";

export const MainNavBar = () => {
  const { userProfile } = useAuth();
  const { unreadCount } = useNotifications();
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    console.log("Searching for:", query);
  };

  const handleUserManual = () => {
    // Open user manual in new tab
    window.open('/user-manual.html', '_blank');
  };

  return (
    <header className="glassmorphism bg-background/80 backdrop-blur-md border-b border-border/50 px-3 sm:px-6 py-3 sm:py-4 sticky top-0 z-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 sm:space-x-4" data-aos="fade-right">
          {/* Mobile Sidebar Trigger */}
          <div className="md:hidden">
            <SidebarTrigger className="glassmorphism hover:bg-accent/20 transition-all duration-300" />
          </div>

          <div className="flex items-center space-x-2 sm:space-x-3">
            <img
              src="/lovable-uploads/491c7e61-a4fb-46a3-a002-904b84354e48.png"
              alt="CT Communication Towers Logo"
              className="w-6 h-6 sm:w-8 sm:h-8 object-contain"
            />
            <div className="hidden sm:block">
              <h1 className="text-lg sm:text-xl font-bold" style={{ fontFamily: 'Rubik Doodle Shadow, cursive' }}>
                CTNL AI WORK-BOARD
              </h1>
              <p className="text-xs text-muted-foreground -mt-1">
                AI-Powered Workforce Management
              </p>
            </div>
            <div className="block sm:hidden">
              <h1 className="text-sm font-bold" style={{ fontFamily: 'Rubik Doodle Shadow, cursive' }}>
                CTNL AI
              </h1>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-1 sm:gap-3 ml-auto" data-aos="fade-left">
          {/* Real-time Notifications */}
          <div className="relative">
            <EnhancedNotificationCenter />
          </div>

          {/* User Manual Button */}
          <Button
            onClick={handleUserManual}
            variant="ghost"
            size="sm"
            className="hidden md:flex glassmorphism hover:bg-accent/20 transition-all duration-300 group"
            title="Open User Manual"
          >
            <BookOpen className="h-4 w-4 mr-2 group-hover:text-primary transition-colors" />
            <span className="hidden lg:inline">Manual</span>
            <ExternalLink className="h-3 w-3 ml-1 opacity-60" />
          </Button>

          {/* Mobile User Manual Button */}
          <Button
            onClick={handleUserManual}
            variant="ghost"
            size="sm"
            className="md:hidden glassmorphism hover:bg-accent/20 transition-all duration-300"
            title="Open User Manual"
          >
            <BookOpen className="h-4 w-4" />
          </Button>

          {/* User Profile Badge */}
          {userProfile && (
            <div className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1 sm:py-2 rounded-lg bg-accent/20 glassmorphism">
              <div className="w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center">
                <User className="h-2 w-2 sm:h-3 sm:w-3 text-white" />
              </div>
              <span className="text-xs sm:text-sm font-medium hidden sm:block">
                {userProfile.full_name?.split(' ')[0] || 'User'}
              </span>
              <Badge variant="outline" className="text-xs hidden sm:inline-flex">
                {userProfile.role}
              </Badge>
              <Badge variant="outline" className="text-xs sm:hidden">
                {userProfile.role?.charAt(0).toUpperCase()}
              </Badge>
            </div>
          )}

          <ThemeSwitcher />
        </div>
      </div>
    </header>
  );
};
