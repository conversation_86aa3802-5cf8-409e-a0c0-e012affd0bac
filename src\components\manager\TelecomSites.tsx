
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MapPin, BarChart3, FileText, Calendar } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { useNavigate } from "react-router-dom";

interface TelecomSite {
  id: string;
  name: string;
  location: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

interface ProjectReport {
  id: string;
  report_title: string;
  site_id: string;
  created_at: string;
  created_by: string;
}

export const TelecomSites = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { userProfile } = useAuth();
  const [sites, setSites] = useState<TelecomSite[]>([]);
  const [reports, setReports] = useState<ProjectReport[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchTelecomSites = async () => {
    try {
      const { data, error } = await supabase
        .from('telecom_sites')
        .select(`
          id,
          name,
          location,
          status,
          created_at,
          updated_at
        `)
        .order('name');

      if (error) throw error;
      setSites(data || []);
    } catch (error) {
      console.error('Error fetching telecom sites:', error);
      toast({
        title: "Error",
        description: "Failed to fetch telecom sites",
        variant: "destructive",
      });
    }
  };

  const fetchReports = async () => {
    try {
      const { data, error } = await supabase
        .from('construction_reports')
        .select(`
          id,
          report_title,
          site_id,
          created_at,
          created_by
        `)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      
      const transformedReports = (data || []).map(report => ({
        id: report.id,
        report_title: report.report_title || 'Construction Report',
        site_id: report.site_id,
        created_at: report.created_at,
        created_by: report.created_by
      }));
      
      setReports(transformedReports);
    } catch (error) {
      console.error('Error fetching reports:', error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      await Promise.all([fetchTelecomSites(), fetchReports()]);
      setLoading(false);
    };
    loadData();
  }, []);

  const handleAction = async (action: string, siteName: string, siteId: string) => {
    if (action === "Submit Report") {
      try {
        const { error } = await supabase
          .from('construction_reports')
          .insert({
            report_title: `${siteName} - Telecom Site Report`,
            site_id: siteId,
            report_type: 'site_inspection',
            work_performed: `Automated telecom site report for ${siteName}`,
            created_by: userProfile?.id
          });

        if (error) throw error;

        toast({
          title: "Report Submitted",
          description: `Telecom site report for ${siteName} has been submitted successfully`,
        });
        
        fetchReports();
      } catch (error) {
        console.error('Error submitting report:', error);
        toast({
          title: "Error",
          description: "Failed to submit report",
          variant: "destructive",
        });
      }
    } else if (action === "View Analytics") {
      toast({
        title: "Analytics",
        description: `Loading telecom site analytics for ${siteName}`,
      });
      // Navigate to analytics page
      navigate(`/dashboard/manager?tab=analytics&site=${siteId}`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-500/20 text-green-500';
      case 'maintenance':
      case 'planning':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'inactive':
      case 'cancelled':
        return 'bg-red-500/20 text-red-500';
      case 'completed':
        return 'bg-blue-500/20 text-blue-500';
      default:
        return 'bg-gray-500/20 text-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Telecom Sites</h2>
          <p className="text-muted-foreground">Loading telecom site data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Telecom Sites Management</h2>
        <p className="text-muted-foreground">Monitor telecom site performance and generate reports</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <MapPin className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Sites</p>
              <p className="text-2xl font-bold">{sites.length}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <BarChart3 className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Active Sites</p>
              <p className="text-2xl font-bold">
                {sites.filter(site => site.status === 'active').length}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Calendar className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">In Progress</p>
              <p className="text-2xl font-bold">
                {sites.filter(site => site.status === 'planning').length}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <FileText className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Recent Reports</p>
              <p className="text-2xl font-bold">{reports.length}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sites Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {sites.map((site) => (
          <Card key={site.id} className="bg-black/10 border-none shadow-lg rounded-2xl hover:bg-black/20 transition-all">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-medium">
                <MapPin className="h-5 w-5" />
                {site.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground">{site.location}</p>

                  <div className="mt-4 flex items-center justify-between">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${getStatusColor(site.status)}`}
                    >
                      {site.status}
                    </span>
                    
                    {site.created_at && (
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>Created: {new Date(site.created_at).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-between gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 rounded-xl"
                    onClick={() => handleAction("View Analytics", site.name, site.id)}
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Analytics
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 rounded-xl"
                    onClick={() => handleAction("Submit Report", site.name, site.id)}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Report
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {sites.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Telecom Sites Found</h3>
            <p className="text-muted-foreground">Contact your administrator to add telecom sites.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
