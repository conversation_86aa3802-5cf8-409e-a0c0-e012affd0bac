import { defineTool } from '../../defineTool';
import { lazy } from 'react';

// Text Case Converter
const textCaseConverter = defineTool('string', {
  name: 'Text Case Converter',
  path: 'case-converter',
  icon: 'material-symbols:text-format',
  description: 'Convert text between different cases like uppercase, lowercase, title case, camel case, and more.',
  shortDescription: 'Convert text between different cases.',
  keywords: ['case', 'uppercase', 'lowercase', 'camel', 'pascal', 'snake'],
  component: lazy(() => import('./TextCaseConverter'))
});

// Base64 Encoder/Decoder
const base64Tool = defineTool('string', {
  name: 'Base64 Encoder/Decoder',
  path: 'base64',
  icon: 'tabler:number-64-small',
  description: 'Encode or decode data using Base64, commonly used in web applications for data transmission.',
  shortDescription: 'Encode or decode data using Base64.',
  keywords: ['base64', 'encode', 'decode', 'encoding'],
  component: lazy(() => import('./Base64Tool'))
});

// Text Replacer
const textReplacer = defineTool('string', {
  name: 'Text Replacer',
  path: 'text-replacer',
  icon: 'material-symbols:find-replace',
  description: 'Find and replace text with support for regular expressions and case sensitivity options.',
  shortDescription: 'Find and replace text with regex support.',
  keywords: ['replace', 'find', 'regex', 'substitute'],
  component: lazy(() => import('./TextReplacer'))
});

// Text Splitter
const textSplitter = defineTool('string', {
  name: 'Text Splitter',
  path: 'text-splitter',
  icon: 'material-symbols:content-cut',
  description: 'Split text into lines or segments using custom delimiters and separators.',
  shortDescription: 'Split text using custom delimiters.',
  keywords: ['split', 'delimiter', 'separator', 'lines'],
  component: lazy(() => import('./TextSplitter'))
});

// Text Joiner
const textJoiner = defineTool('string', {
  name: 'Text Joiner',
  path: 'text-joiner',
  icon: 'material-symbols:join',
  description: 'Join multiple lines of text with custom separators and formatting options.',
  shortDescription: 'Join text lines with custom separators.',
  keywords: ['join', 'merge', 'combine', 'separator'],
  component: lazy(() => import('./TextJoiner'))
});

// Text Reverser
const textReverser = defineTool('string', {
  name: 'Text Reverser',
  path: 'text-reverser',
  icon: 'material-symbols:swap-horiz',
  description: 'Reverse text character by character or word by word with various options.',
  shortDescription: 'Reverse text characters or words.',
  keywords: ['reverse', 'backwards', 'flip'],
  component: lazy(() => import('./TextReverser'))
});

// Text Statistics
const textStatistics = defineTool('string', {
  name: 'Text Statistics',
  path: 'text-statistics',
  icon: 'material-symbols:analytics',
  description: 'Analyze text to get detailed statistics including character count, word count, and readability metrics.',
  shortDescription: 'Get detailed text statistics and analysis.',
  keywords: ['statistics', 'count', 'analysis', 'metrics'],
  component: lazy(() => import('./TextStatistics'))
});

// URL Encoder/Decoder
const urlEncoder = defineTool('string', {
  name: 'URL Encoder/Decoder',
  path: 'url-encoder',
  icon: 'material-symbols:link',
  description: 'Encode or decode URLs and URI components for web development and data transmission.',
  shortDescription: 'Encode or decode URLs and URI components.',
  keywords: ['url', 'uri', 'encode', 'decode', 'percent'],
  component: lazy(() => import('./UrlEncoder'))
});

// HTML Encoder/Decoder
const htmlEncoder = defineTool('string', {
  name: 'HTML Encoder/Decoder',
  path: 'html-encoder',
  icon: 'material-symbols:code',
  description: 'Encode or decode HTML entities and special characters for web development.',
  shortDescription: 'Encode or decode HTML entities.',
  keywords: ['html', 'entities', 'encode', 'decode', 'escape'],
  component: lazy(() => import('./HtmlEncoder'))
});

// Text Repeater
const textRepeater = defineTool('string', {
  name: 'Text Repeater',
  path: 'text-repeater',
  icon: 'material-symbols:repeat',
  description: 'Repeat text a specified number of times with custom separators and formatting.',
  shortDescription: 'Repeat text multiple times.',
  keywords: ['repeat', 'duplicate', 'multiply'],
  component: lazy(() => import('./TextRepeater'))
});

// Remove Duplicate Lines
const removeDuplicateLines = defineTool('string', {
  name: 'Remove Duplicate Lines',
  path: 'remove-duplicates',
  icon: 'material-symbols:filter-list',
  description: 'Remove duplicate lines from text while preserving order or sorting options.',
  shortDescription: 'Remove duplicate lines from text.',
  keywords: ['duplicate', 'unique', 'filter', 'clean'],
  component: lazy(() => import('./RemoveDuplicateLines'))
});

// Morse Code Converter
const morseCodeConverter = defineTool('string', {
  name: 'Morse Code Converter',
  path: 'morse-code',
  icon: 'material-symbols:radio',
  description: 'Convert text to Morse code and vice versa with audio playback support.',
  shortDescription: 'Convert text to/from Morse code.',
  keywords: ['morse', 'code', 'telegraph', 'dots', 'dashes'],
  component: lazy(() => import('./MorseCodeConverter'))
});

export const stringTools = [
  textCaseConverter,
  base64Tool,
  textReplacer,
  textSplitter,
  textJoiner,
  textReverser,
  textStatistics,
  urlEncoder,
  htmlEncoder,
  textRepeater,
  removeDuplicateLines,
  morseCodeConverter
];
