
import { ReactNode, useEffect } from "react";
import { SidebarProvider, SidebarTrigger, SidebarInset } from "@/components/ui/sidebar";
import { EnhancedAppSidebar } from "@/components/navigation/EnhancedAppSidebar";
import { MainNavBar } from "@/components/navigation/MainNavBar";
import { VoiceNavigationButton } from "@/components/voice/VoiceNavigationButton";
import { TokenExpiryWarning } from "@/components/auth/TokenExpiryWarning";
import { useAuth } from "@/components/auth/AuthProvider";
import AOS from "aos";

interface EnhancedAppLayoutProps {
  children: ReactNode;
  title?: string;
}

export function EnhancedAppLayout({ children, title }: EnhancedAppLayoutProps) {
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    AOS.refresh();
  }, []);

  return (
    <SidebarProvider>
      <div className="dashboard-container min-h-screen flex w-full">
        <EnhancedAppSidebar />
        <SidebarInset className="flex-1">
          <MainNavBar />
          <main className="content-wrapper flex-1 p-3 sm:p-4 md:p-6 pt-16 sm:pt-20 md:pt-24" data-aos="fade-up" data-aos-duration="600">
            {/* Token Expiry Warning */}
            {isAuthenticated && (
              <div className="mb-4" data-aos="fade-down" data-aos-delay="100">
                <TokenExpiryWarning />
              </div>
            )}

            {title && (
              <div className="glassmorphism rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8" data-aos="fade-down" data-aos-delay="200">
                <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold modern-heading">
                  {title}
                </h1>
              </div>
            )}
            <div data-aos="fade-in" data-aos-delay="400">
              {children}
            </div>
          </main>
        </SidebarInset>

        {/* Voice Navigation Button - Available on all dashboard pages */}
        <VoiceNavigationButton
          position="bottom-right"
          size="md"
          showTooltip={true}
        />
      </div>
    </SidebarProvider>
  );
}
