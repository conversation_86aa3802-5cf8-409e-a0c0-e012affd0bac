
import { useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Bot, 
  Send, 
  User, 
  Loader, 
  FileText, 
  Users, 
  ClipboardList,
  Database,
  Upload,
  Download,
  BarChart3,
  Zap
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  actions?: AIAction[];
  attachments?: FileAttachment[];
}

interface AIAction {
  type: 'create_project' | 'assign_task' | 'generate_document' | 'analyze_file' | 'query_database';
  status: 'pending' | 'completed' | 'error';
  data?: any;
  result?: any;
}

interface FileAttachment {
  name: string;
  type: string;
  size: number;
  content?: string;
  analysis?: any;
}

export const AIAgent = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState('');
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { userProfile } = useAuth();
  const { toast } = useToast();

  const sendMessage = async () => {
    if (!input.trim() || isProcessing) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsProcessing(true);
    setProgress(0);

    try {
      // Step 1: Analyze user intent
      setProcessingStep('Analyzing your request...');
      setProgress(20);

      const { data: intentData, error: intentError } = await supabase.functions.invoke('ai-agent-intent', {
        body: {
          message: input.trim(),
          userId: userProfile?.id,
          userRole: userProfile?.role,
          context: {
            previousMessages: messages.slice(-5)
          }
        }
      });

      if (intentError) throw intentError;

      // Step 2: Execute AI actions based on intent
      setProcessingStep('Processing your request...');
      setProgress(40);

      const actions: AIAction[] = [];
      let responseContent = '';

      for (const action of intentData.actions || []) {
        const actionResult = await executeAIAction(action);
        actions.push(actionResult);
      }

      // Step 3: Generate response
      setProcessingStep('Generating response...');
      setProgress(80);

      const { data: responseData, error: responseError } = await supabase.functions.invoke('ai-agent-response', {
        body: {
          originalMessage: input.trim(),
          actions: actions,
          userRole: userProfile?.role,
          context: intentData.context
        }
      });

      if (responseError) throw responseError;

      setProgress(100);

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: responseData.response,
        timestamp: new Date(),
        actions: actions
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Show success notification
      if (actions.some(a => a.status === 'completed')) {
        toast({
          title: "Actions Completed",
          description: `Successfully executed ${actions.filter(a => a.status === 'completed').length} action(s)`,
        });
      }

    } catch (error: any) {
      console.error('AI Agent Error:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `I encountered an error: ${error.message}. Please try rephrasing your request or check if you have the necessary permissions.`,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: "AI Agent Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
      setProgress(0);
    }
  };

  const executeAIAction = async (action: any): Promise<AIAction> => {
    try {
      const { data, error } = await supabase.functions.invoke('ai-agent-executor', {
        body: {
          intent: action.type || action.action || action, // Use 'intent' instead of 'action'
          query: action.description || `Execute ${action.type || action.action || action}`,
          userId: userProfile?.id,
          userRole: userProfile?.role,
          context: action.context || {}
        }
      });

      if (error) throw error;

      return {
        type: action.type,
        status: 'completed',
        data: action.data,
        result: data
      };
    } catch (error) {
      return {
        type: action.type,
        status: 'error',
        data: action.data,
        result: { error: error.message }
      };
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsProcessing(true);
    setProcessingStep('Analyzing uploaded files...');

    try {
      const attachments: FileAttachment[] = [];

      for (const file of files) {
        const content = await readFileContent(file);
        
        const { data: analysisData, error } = await supabase.functions.invoke('ai-file-analyzer', {
          body: {
            fileName: file.name,
            fileType: file.type,
            content: content,
            userId: userProfile?.id
          }
        });

        if (error) throw error;

        attachments.push({
          name: file.name,
          type: file.type,
          size: file.size,
          content: content,
          analysis: analysisData
        });
      }

      const fileMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content: `Uploaded ${files.length} file(s) for analysis`,
        timestamp: new Date(),
        attachments: attachments
      };

      setMessages(prev => [...prev, fileMessage]);

      // Generate AI response about the files
      const analysisMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `I've analyzed your uploaded files. Here's what I found:\n\n${attachments.map(att => 
          `**${att.name}**: ${att.analysis?.summary || 'Analysis completed'}`
        ).join('\n\n')}`,
        timestamp: new Date(),
        attachments: attachments
      };

      setMessages(prev => [...prev, analysisMessage]);

    } catch (error: any) {
      toast({
        title: "File Analysis Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
    }
  };

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = reject;
      reader.readAsText(file);
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'create_project': return <ClipboardList className="h-4 w-4" />;
      case 'assign_task': return <Users className="h-4 w-4" />;
      case 'generate_document': return <FileText className="h-4 w-4" />;
      case 'analyze_file': return <BarChart3 className="h-4 w-4" />;
      case 'query_database': return <Database className="h-4 w-4" />;
      default: return <Zap className="h-4 w-4" />;
    }
  };

  return (
    <div className="h-[800px] flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-2xl font-bold">AI Agent</h2>
          <p className="text-muted-foreground">Natural language interface for all platform operations</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isProcessing}
          >
            <Upload className="h-4 w-4 mr-2" />
            Upload Files
          </Button>
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        multiple
        accept=".pdf,.doc,.docx,.txt,.csv,.xlsx,.xls"
        onChange={handleFileUpload}
      />

      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI Assistant
            {isProcessing && (
              <Badge variant="secondary" className="ml-auto">
                <Loader className="h-3 w-3 mr-1 animate-spin" />
                Processing
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col p-0">
          <ScrollArea className="flex-1 p-4">
            {messages.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Bot className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <h3 className="font-semibold mb-2">Welcome to AI Agent</h3>
                <p className="mb-4">I can help you with:</p>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center gap-2">
                    <ClipboardList className="h-4 w-4" />
                    Create projects
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Assign tasks
                  </div>
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Generate documents
                  </div>
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Query database
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      message.role === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    {message.role === 'assistant' && (
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <Bot className="h-4 w-4" />
                      </div>
                    )}
                    
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.role === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      }`}
                    >
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      
                      {message.actions && message.actions.length > 0 && (
                        <div className="mt-3 space-y-2">
                          <p className="text-xs font-semibold">Actions Performed:</p>
                          {message.actions.map((action, index) => (
                            <div key={index} className="flex items-center gap-2 text-xs">
                              {getActionIcon(action.type)}
                              <span>{action.type.replace('_', ' ').toUpperCase()}</span>
                              <Badge 
                                variant={action.status === 'completed' ? 'default' : 'destructive'}
                                className="text-xs"
                              >
                                {action.status}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      )}

                      {message.attachments && message.attachments.length > 0 && (
                        <div className="mt-3 space-y-2">
                          <p className="text-xs font-semibold">Files:</p>
                          {message.attachments.map((attachment, index) => (
                            <div key={index} className="flex items-center gap-2 text-xs">
                              <FileText className="h-3 w-3" />
                              <span>{attachment.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {(attachment.size / 1024).toFixed(1)} KB
                              </Badge>
                            </div>
                          ))}
                        </div>
                      )}
                      
                      <p className="text-xs opacity-70 mt-2">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                    
                    {message.role === 'user' && (
                      <div className="h-8 w-8 rounded-full bg-secondary/10 flex items-center justify-center">
                        <User className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                ))}
                
                {isProcessing && (
                  <div className="flex gap-3 justify-start">
                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Bot className="h-4 w-4" />
                    </div>
                    <div className="bg-muted p-3 rounded-lg max-w-[80%]">
                      <div className="flex items-center gap-2 mb-2">
                        <Loader className="h-4 w-4 animate-spin" />
                        <span className="text-sm">{processingStep}</span>
                      </div>
                      <Progress value={progress} className="w-full" />
                    </div>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>
          
          <div className="p-4 border-t">
            <div className="flex gap-2">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me to create projects, assign tasks, generate reports, analyze files..."
                disabled={isProcessing}
                className="flex-1"
              />
              <Button 
                onClick={sendMessage} 
                disabled={!input.trim() || isProcessing}
                size="sm"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Try: "Create a new telecom project", "Generate expense report for last month", "Analyze the uploaded spreadsheet"
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
