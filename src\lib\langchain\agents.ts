/**
 * LangChain Agents and Tools
 * Custom agents and tools for the AI Workboard system
 */

import { Tool } from '@langchain/core/tools';
import { AgentExecutor, createOpenAIFunctionsAgent } from 'langchain/agents';
import { ChatPromptTemplate, MessagesPlaceholder } from '@langchain/core/prompts';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { langChainConfig } from './config';
import { ragSystem } from './rag-system';
import { supabase } from '../supabase';

/**
 * Database Query Tool
 */
export class DatabaseQueryTool extends Tool {
  name = 'database_query';
  description = 'Query the Supabase database for information about users, reports, memos, and system data';

  async _call(query: string): Promise<string> {
    try {
      // Parse the query to determine table and operation
      const queryLower = query.toLowerCase();
      
      if (queryLower.includes('user') || queryLower.includes('profile')) {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .limit(10);
        
        if (error) throw error;
        return JSON.stringify(data, null, 2);
      }
      
      if (queryLower.includes('report')) {
        const { data, error } = await supabase
          .from('reports')
          .select('*')
          .limit(10);
        
        if (error) throw error;
        return JSON.stringify(data, null, 2);
      }
      
      if (queryLower.includes('memo')) {
        const { data, error } = await supabase
          .from('memos')
          .select('*')
          .limit(10);
        
        if (error) throw error;
        return JSON.stringify(data, null, 2);
      }
      
      return 'Please specify which table you want to query (users, reports, memos)';
    } catch (error) {
      return `Database query error: ${error}`;
    }
  }
}

/**
 * Document Analysis Tool
 */
export class DocumentAnalysisTool extends Tool {
  name = 'document_analysis';
  description = 'Analyze documents using RAG system to extract insights and answer questions';

  async _call(input: string): Promise<string> {
    try {
      const response = await ragSystem.query({
        question: input,
        maxResults: 5,
      });
      
      return `Analysis: ${response.answer}\n\nSources: ${response.sources.length} documents\nConfidence: ${(response.confidence * 100).toFixed(1)}%`;
    } catch (error) {
      return `Document analysis error: ${error}`;
    }
  }
}

/**
 * Report Generation Tool
 */
export class ReportGenerationTool extends Tool {
  name = 'report_generation';
  description = 'Generate reports based on system data and user requirements';

  async _call(input: string): Promise<string> {
    try {
      // Parse input to determine report type
      const inputLower = input.toLowerCase();
      
      if (inputLower.includes('user') || inputLower.includes('staff')) {
        return await this.generateUserReport();
      }
      
      if (inputLower.includes('activity') || inputLower.includes('usage')) {
        return await this.generateActivityReport();
      }
      
      if (inputLower.includes('performance') || inputLower.includes('metrics')) {
        return await this.generatePerformanceReport();
      }
      
      return 'Please specify report type: user, activity, or performance';
    } catch (error) {
      return `Report generation error: ${error}`;
    }
  }

  private async generateUserReport(): Promise<string> {
    const { data: users, error } = await supabase
      .from('profiles')
      .select('role, created_at')
      .order('created_at', { ascending: false });

    if (error) throw error;

    const roleStats = users?.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return `User Report:
Total Users: ${users?.length || 0}
Role Distribution: ${JSON.stringify(roleStats, null, 2)}
Latest Registration: ${users?.[0]?.created_at || 'N/A'}`;
  }

  private async generateActivityReport(): Promise<string> {
    // This would typically query activity logs
    return 'Activity Report: System activity analysis would be implemented here';
  }

  private async generatePerformanceReport(): Promise<string> {
    // This would typically analyze performance metrics
    return 'Performance Report: System performance metrics would be analyzed here';
  }
}

/**
 * System Management Tool
 */
export class SystemManagementTool extends Tool {
  name = 'system_management';
  description = 'Manage system settings, configurations, and administrative tasks';

  async _call(input: string): Promise<string> {
    try {
      const inputLower = input.toLowerCase();
      
      if (inputLower.includes('status') || inputLower.includes('health')) {
        return await this.getSystemStatus();
      }
      
      if (inputLower.includes('config') || inputLower.includes('setting')) {
        return await this.getSystemConfig();
      }
      
      return 'Available commands: status, health, config, settings';
    } catch (error) {
      return `System management error: ${error}`;
    }
  }

  private async getSystemStatus(): Promise<string> {
    const config = langChainConfig.getConfig();
    const validation = langChainConfig.validateConfig();
    
    return `System Status:
LangChain Config Valid: ${validation.isValid}
Vector Store: ${config.vectorStore.provider}
Default Model: ${config.openai.apiKey ? 'OpenAI' : config.anthropic.apiKey ? 'Anthropic' : 'None'}
Memory Type: ${config.memory.type}
${validation.errors.length > 0 ? `Errors: ${validation.errors.join(', ')}` : ''}`;
  }

  private async getSystemConfig(): Promise<string> {
    const config = langChainConfig.getConfig();
    
    return `System Configuration:
RAG Chunk Size: ${config.rag.chunkSize}
RAG Top K: ${config.rag.topK}
Vector Store: ${config.vectorStore.provider}
Memory Max Tokens: ${config.memory.maxTokens}
Agent Max Iterations: ${config.agent.maxIterations}`;
  }
}

/**
 * AI Workboard Agent
 */
export class AIWorkboardAgent {
  private agent: AgentExecutor | null = null;
  private model: BaseChatModel;
  private tools: Tool[];

  constructor(model?: BaseChatModel) {
    this.model = model || langChainConfig.getDefaultModel();
    this.tools = [
      new DatabaseQueryTool(),
      new DocumentAnalysisTool(),
      new ReportGenerationTool(),
      new SystemManagementTool(),
    ];
  }

  /**
   * Initialize the agent
   */
  public async initialize(): Promise<void> {
    try {
      const prompt = ChatPromptTemplate.fromMessages([
        ['system', this.getSystemPrompt()],
        ['human', '{input}'],
        new MessagesPlaceholder('agent_scratchpad'),
      ]);

      const agent = await createOpenAIFunctionsAgent({
        llm: this.model,
        tools: this.tools,
        prompt,
      });

      this.agent = new AgentExecutor({
        agent,
        tools: this.tools,
        maxIterations: langChainConfig.getConfig().agent.maxIterations,
        verbose: langChainConfig.getConfig().agent.verbose,
      });

      console.log('AI Workboard Agent initialized successfully');
    } catch (error) {
      throw new Error(`Failed to initialize agent: ${error}`);
    }
  }

  /**
   * Execute agent task
   */
  public async execute(input: string): Promise<string> {
    try {
      if (!this.agent) {
        await this.initialize();
      }

      if (!this.agent) {
        throw new Error('Agent not initialized');
      }

      const result = await this.agent.invoke({ input });
      return result.output;
    } catch (error) {
      throw new Error(`Agent execution failed: ${error}`);
    }
  }

  /**
   * Stream agent response
   */
  public async *streamExecute(input: string): AsyncGenerator<string, void, unknown> {
    try {
      if (!this.agent) {
        await this.initialize();
      }

      if (!this.agent) {
        throw new Error('Agent not initialized');
      }

      const stream = await this.agent.streamLog({ input });
      
      for await (const chunk of stream) {
        if (chunk.ops?.length > 0) {
          for (const op of chunk.ops) {
            if (op.op === 'add' && op.path?.includes('messages')) {
              const message = op.value;
              if (message instanceof AIMessage) {
                yield message.content as string;
              }
            }
          }
        }
      }
    } catch (error) {
      throw new Error(`Agent streaming failed: ${error}`);
    }
  }

  /**
   * Add custom tool
   */
  public addTool(tool: Tool): void {
    this.tools.push(tool);
    this.agent = null; // Force re-initialization
  }

  /**
   * Get system prompt
   */
  private getSystemPrompt(): string {
    return `You are an AI assistant for the CTNL AI Workboard system. You have access to various tools to help users with:

1. Database queries - Access user profiles, reports, memos, and system data
2. Document analysis - Analyze documents using RAG system
3. Report generation - Create reports based on system data
4. System management - Check status and manage configurations

Guidelines:
- Be helpful, accurate, and professional
- Use the appropriate tools for each task
- Provide clear explanations of your actions
- If you need more information, ask clarifying questions
- Always prioritize user privacy and data security
- Format responses clearly and include relevant details

You are designed to assist with administrative tasks, data analysis, and system management for the AI Workboard platform.`;
  }
}

// Export singleton instance
export const aiWorkboardAgent = new AIWorkboardAgent();
