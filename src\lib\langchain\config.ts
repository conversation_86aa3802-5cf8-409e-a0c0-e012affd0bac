/**
 * LangChain Configuration Service
 * Centralized configuration for all LangChain operations
 */

import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

export interface LangChainConfig {
  // Model configurations
  openai: {
    apiKey: string;
    model: string;
    temperature: number;
    maxTokens: number;
    streaming: boolean;
  };
  anthropic: {
    apiKey: string;
    model: string;
    temperature: number;
    maxTokens: number;
    streaming: boolean;
  };
  // Vector store configurations
  vectorStore: {
    provider: 'chroma' | 'hnswlib' | 'faiss';
    dimensions: number;
    collectionName: string;
    persistDirectory?: string;
  };
  // Memory configurations
  memory: {
    type: 'buffer' | 'summary' | 'entity' | 'knowledge_graph';
    maxTokens: number;
    returnMessages: boolean;
  };
  // RAG configurations
  rag: {
    chunkSize: number;
    chunkOverlap: number;
    topK: number;
    scoreThreshold: number;
  };
  // Agent configurations
  agent: {
    maxIterations: number;
    maxExecutionTime: number;
    verbose: boolean;
  };
}

export class LangChainConfigService {
  private static instance: LangChainConfigService;
  private config: LangChainConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  public static getInstance(): LangChainConfigService {
    if (!LangChainConfigService.instance) {
      LangChainConfigService.instance = new LangChainConfigService();
    }
    return LangChainConfigService.instance;
  }

  private loadConfig(): LangChainConfig {
    return {
      openai: {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: process.env.OPENAI_MODEL || 'gpt-4-turbo-preview',
        temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
        maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '4096'),
        streaming: process.env.OPENAI_STREAMING === 'true',
      },
      anthropic: {
        apiKey: process.env.ANTHROPIC_API_KEY || '',
        model: process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229',
        temperature: parseFloat(process.env.ANTHROPIC_TEMPERATURE || '0.7'),
        maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS || '4096'),
        streaming: process.env.ANTHROPIC_STREAMING === 'true',
      },
      vectorStore: {
        provider: (process.env.VECTOR_STORE_PROVIDER as 'chroma' | 'hnswlib' | 'faiss') || 'chroma',
        dimensions: parseInt(process.env.VECTOR_STORE_DIMENSIONS || '1536'),
        collectionName: process.env.VECTOR_STORE_COLLECTION || 'aiworkboard_documents',
        persistDirectory: process.env.VECTOR_STORE_PERSIST_DIR || './data/vectorstore',
      },
      memory: {
        type: (process.env.MEMORY_TYPE as 'buffer' | 'summary' | 'entity' | 'knowledge_graph') || 'buffer',
        maxTokens: parseInt(process.env.MEMORY_MAX_TOKENS || '2000'),
        returnMessages: process.env.MEMORY_RETURN_MESSAGES === 'true',
      },
      rag: {
        chunkSize: parseInt(process.env.RAG_CHUNK_SIZE || '1000'),
        chunkOverlap: parseInt(process.env.RAG_CHUNK_OVERLAP || '200'),
        topK: parseInt(process.env.RAG_TOP_K || '5'),
        scoreThreshold: parseFloat(process.env.RAG_SCORE_THRESHOLD || '0.7'),
      },
      agent: {
        maxIterations: parseInt(process.env.AGENT_MAX_ITERATIONS || '10'),
        maxExecutionTime: parseInt(process.env.AGENT_MAX_EXECUTION_TIME || '30000'),
        verbose: process.env.AGENT_VERBOSE === 'true',
      },
    };
  }

  public getConfig(): LangChainConfig {
    return this.config;
  }

  public updateConfig(updates: Partial<LangChainConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  public getOpenAIModel(): ChatOpenAI {
    const config = this.config.openai;
    return new ChatOpenAI({
      openAIApiKey: config.apiKey,
      modelName: config.model,
      temperature: config.temperature,
      maxTokens: config.maxTokens,
      streaming: config.streaming,
    });
  }

  public getAnthropicModel(): ChatAnthropic {
    const config = this.config.anthropic;
    return new ChatAnthropic({
      anthropicApiKey: config.apiKey,
      modelName: config.model,
      temperature: config.temperature,
      maxTokens: config.maxTokens,
      streaming: config.streaming,
    });
  }

  public getDefaultModel(): BaseChatModel {
    // Prefer OpenAI if available, fallback to Anthropic
    if (this.config.openai.apiKey) {
      return this.getOpenAIModel();
    } else if (this.config.anthropic.apiKey) {
      return this.getAnthropicModel();
    } else {
      throw new Error('No API keys configured for language models');
    }
  }

  public validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for at least one model API key
    if (!this.config.openai.apiKey && !this.config.anthropic.apiKey) {
      errors.push('At least one model API key (OpenAI or Anthropic) must be configured');
    }

    // Validate numeric values
    if (this.config.rag.chunkSize <= 0) {
      errors.push('RAG chunk size must be greater than 0');
    }

    if (this.config.rag.chunkOverlap < 0) {
      errors.push('RAG chunk overlap must be non-negative');
    }

    if (this.config.rag.topK <= 0) {
      errors.push('RAG top K must be greater than 0');
    }

    if (this.config.vectorStore.dimensions <= 0) {
      errors.push('Vector store dimensions must be greater than 0');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const langChainConfig = LangChainConfigService.getInstance();

// Export types
export type { BaseChatModel };
