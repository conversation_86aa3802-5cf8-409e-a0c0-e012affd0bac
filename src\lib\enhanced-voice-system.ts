/**
 * Enhanced Voice System
 * Advanced text-to-speech with human-like male voice and better speech synthesis
 */

export class EnhancedVoiceSystem {
  private synthesis: SpeechSynthesis | null = null;
  private voices: SpeechSynthesisVoice[] = [];
  private selectedVoice: SpeechSynthesisVoice | null = null;
  private isInitialized = false;

  constructor() {
    this.initializeVoiceSystem();
  }

  private async initializeVoiceSystem(): Promise<void> {
    if (!('speechSynthesis' in window)) {
      console.warn('Speech synthesis not supported');
      return;
    }

    this.synthesis = window.speechSynthesis;
    
    // Wait for voices to load
    await this.loadVoices();
    this.selectBestMaleVoice();
    this.isInitialized = true;
    
    console.log('🎤 Enhanced Voice System initialized with voice:', this.selectedVoice?.name);
  }

  private loadVoices(): Promise<void> {
    return new Promise((resolve) => {
      const loadVoicesHandler = () => {
        this.voices = this.synthesis?.getVoices() || [];
        if (this.voices.length > 0) {
          resolve();
        }
      };

      // Voices might already be loaded
      this.voices = this.synthesis?.getVoices() || [];
      if (this.voices.length > 0) {
        resolve();
        return;
      }

      // Wait for voices to load
      this.synthesis?.addEventListener('voiceschanged', loadVoicesHandler);
      
      // Fallback timeout
      setTimeout(() => {
        this.voices = this.synthesis?.getVoices() || [];
        resolve();
      }, 1000);
    });
  }

  private selectBestMaleVoice(): void {
    if (this.voices.length === 0) return;

    // Priority order for male voices (most human-like first)
    const maleVoicePreferences = [
      // Premium/Neural voices
      'Microsoft David - English (United States)',
      'Microsoft Mark - English (United States)', 
      'Google US English Male',
      'Alex', // macOS
      'Daniel (Enhanced)', // macOS
      'Microsoft David Desktop',
      'Microsoft Mark Desktop',
      
      // Standard male voices
      'David', 'Mark', 'Daniel', 'Alex', 'Tom', 'Michael', 'James', 'Robert',
      
      // Fallback patterns
      'male', 'man', 'masculine'
    ];

    // Find the best male voice
    for (const preference of maleVoicePreferences) {
      const voice = this.voices.find(v => 
        v.name.toLowerCase().includes(preference.toLowerCase()) &&
        v.lang.startsWith('en')
      );
      
      if (voice) {
        this.selectedVoice = voice;
        console.log('🎤 Selected male voice:', voice.name);
        return;
      }
    }

    // If no specific male voice found, look for any English voice that sounds male
    const englishVoices = this.voices.filter(v => v.lang.startsWith('en'));
    
    // Try to find voices with male-sounding names or characteristics
    const maleVoice = englishVoices.find(v => {
      const name = v.name.toLowerCase();
      return name.includes('male') || 
             name.includes('man') || 
             name.includes('david') || 
             name.includes('mark') || 
             name.includes('alex') || 
             name.includes('daniel') ||
             name.includes('tom') ||
             name.includes('michael') ||
             name.includes('james');
    });

    if (maleVoice) {
      this.selectedVoice = maleVoice;
    } else {
      // Fallback to first English voice
      this.selectedVoice = englishVoices[0] || this.voices[0];
    }

    console.log('🎤 Selected fallback voice:', this.selectedVoice?.name);
  }

  public speak(text: string, options: {
    rate?: number;
    pitch?: number;
    volume?: number;
    emphasis?: boolean;
  } = {}): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.synthesis || !this.isInitialized) {
        console.warn('Voice system not initialized');
        resolve();
        return;
      }

      // Cancel any ongoing speech
      this.synthesis.cancel();

      // Enhance text for more natural speech
      const enhancedText = this.enhanceTextForNaturalSpeech(text, options.emphasis);
      
      const utterance = new SpeechSynthesisUtterance(enhancedText);

      // Use selected voice
      if (this.selectedVoice) {
        utterance.voice = this.selectedVoice;
      }

      // Optimized parameters for human-like male voice
      utterance.rate = options.rate || 0.9; // Slightly slower for clarity
      utterance.pitch = options.pitch || 0.8; // Lower pitch for masculine sound
      utterance.volume = options.volume || 0.95; // Clear volume

      // Event handlers
      utterance.onend = () => resolve();
      utterance.onerror = (error) => {
        console.error('Speech synthesis error:', error);
        reject(error);
      };

      // Speak with retry mechanism
      try {
        this.synthesis.speak(utterance);
        
        // Fallback for some browsers that might not trigger onend
        setTimeout(() => {
          resolve();
        }, (enhancedText.length * 100) + 2000); // Estimate speech duration
      } catch (error) {
        console.error('Speech synthesis failed:', error);
        reject(error);
      }
    });
  }

  private enhanceTextForNaturalSpeech(text: string, emphasis: boolean = false): string {
    let enhanced = text;

    // Add natural pauses and breathing
    enhanced = enhanced
      // Add pauses after sentences
      .replace(/\./g, '. ')
      .replace(/\!/g, '! ')
      .replace(/\?/g, '? ')
      // Add pauses after commas
      .replace(/,/g, ', ')
      // Add emphasis to important words if requested
      .replace(/\b(welcome|hello|hi|thanks|please|sorry|important|urgent|critical)\b/gi, 
        emphasis ? '<emphasis level="strong">$1</emphasis>' : '$1')
      // Make greetings more natural
      .replace(/\bhello\b/gi, 'Hello there')
      .replace(/\bhi\b/gi, 'Hi there')
      // Make responses more conversational
      .replace(/\byes\b/gi, 'Yes, absolutely')
      .replace(/\bokay\b/gi, 'Okay, got it')
      .replace(/\bdone\b/gi, 'All done')
      // Add natural transitions
      .replace(/\blet me\b/gi, 'Let me just')
      .replace(/\bi will\b/gi, 'I\'ll go ahead and')
      .replace(/\bi am\b/gi, 'I\'m')
      // Make it sound more like Ifeanyi's assistant
      .replace(/\bmy maker ifeanyi\b/gi, 'my creator Ifeanyi')
      .replace(/\bifeanyi\b/gi, 'Ifeanyi, my developer');

    // Add natural speech patterns
    if (enhanced.length > 100) {
      // Add breathing pause for longer texts
      const midPoint = Math.floor(enhanced.length / 2);
      const nearestSpace = enhanced.indexOf(' ', midPoint);
      if (nearestSpace > 0) {
        enhanced = enhanced.slice(0, nearestSpace) + '. ' + enhanced.slice(nearestSpace + 1);
      }
    }

    return enhanced;
  }

  public async speakWithPersonality(text: string, personality: 'professional' | 'friendly' | 'enthusiastic' = 'professional'): Promise<void> {
    let personalizedText = text;
    let voiceOptions: any = {};

    switch (personality) {
      case 'professional':
        personalizedText = `${text}`;
        voiceOptions = { rate: 0.9, pitch: 0.8, volume: 0.95 };
        break;
      
      case 'friendly':
        personalizedText = `${text}`;
        voiceOptions = { rate: 0.95, pitch: 0.85, volume: 0.9, emphasis: true };
        break;
      
      case 'enthusiastic':
        personalizedText = `${text}`;
        voiceOptions = { rate: 1.0, pitch: 0.9, volume: 1.0, emphasis: true };
        break;
    }

    return this.speak(personalizedText, voiceOptions);
  }

  public getAvailableVoices(): SpeechSynthesisVoice[] {
    return this.voices;
  }

  public getCurrentVoice(): SpeechSynthesisVoice | null {
    return this.selectedVoice;
  }

  public setVoice(voiceName: string): boolean {
    const voice = this.voices.find(v => v.name === voiceName);
    if (voice) {
      this.selectedVoice = voice;
      console.log('🎤 Voice changed to:', voice.name);
      return true;
    }
    return false;
  }

  public stop(): void {
    if (this.synthesis) {
      this.synthesis.cancel();
    }
  }

  public isSupported(): boolean {
    return 'speechSynthesis' in window;
  }

  public isReady(): boolean {
    return this.isInitialized && this.selectedVoice !== null;
  }

  // Test the voice system
  public async testVoice(): Promise<void> {
    const testMessage = "Hello! I'm your CTNL AI Workboard assistant, developed by Ifeanyi. I'm ready to help you navigate the system with voice commands.";
    return this.speakWithPersonality(testMessage, 'friendly');
  }
}

// Create singleton instance
export const enhancedVoiceSystem = new EnhancedVoiceSystem();
