/**
 * Simple React Hook for LangChain Integration
 * Easy-to-use hook for LangChain functionality
 */

import { useState, useCallback } from 'react';
import { useAuth } from '@/components/auth/AuthProvider';
import { useToast } from '@/hooks/use-toast';
import { processAIMessage, type SimpleLangChainResponse } from '@/lib/langchain-simple';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  enhanced?: boolean;
  metadata?: any;
}

export interface LangChainOptions {
  interface?: 'standard' | 'hacker_terminal' | 'futuristic' | 'enhanced';
  useMemory?: boolean;
  temperature?: number;
}

export function useSimpleLangChain() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { userProfile } = useAuth();
  const { toast } = useToast();

  /**
   * Send a message to the AI
   */
  const sendMessage = useCallback(async (
    content: string,
    options: LangChainOptions = {}
  ): Promise<ChatMessage | null> => {
    if (!content.trim() || isLoading) return null;

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      const response: SimpleLangChainResponse = await processAIMessage(
        content.trim(),
        userProfile?.id,
        {
          interface: options.interface || 'standard',
          role: userProfile?.role,
          department: userProfile?.department,
          previousMessages: messages.slice(-5),
        }
      );

      const assistantMessage: ChatMessage = {
        id: `assistant_${Date.now()}`,
        role: 'assistant',
        content: response.response,
        timestamp: new Date(),
        enhanced: response.enhanced,
        metadata: response.metadata,
      };

      setMessages(prev => [...prev, assistantMessage]);
      return assistantMessage;

    } catch (error: any) {
      console.error('AI message error:', error);
      
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        role: 'assistant',
        content: 'I apologize, but I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
        metadata: { error: true },
      };

      setMessages(prev => [...prev, errorMessage]);
      
      toast({
        title: "AI Assistant Error",
        description: error.message || "Failed to get response from AI assistant",
        variant: "destructive",
      });

      return null;
    } finally {
      setIsLoading(false);
    }
  }, [messages, isLoading, userProfile, toast]);

  /**
   * Clear conversation
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  /**
   * Get conversation summary
   */
  const getConversationSummary = useCallback(async (): Promise<string> => {
    if (messages.length === 0) return 'No conversation to summarize.';

    try {
      const conversationText = messages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');

      const response = await processAIMessage(
        `Please provide a brief summary of our conversation: ${conversationText}`,
        userProfile?.id,
        { interface: 'standard' }
      );

      return response.response;
    } catch (error) {
      console.error('Summary generation error:', error);
      return 'Failed to generate conversation summary.';
    }
  }, [messages, userProfile]);

  /**
   * Send message with specific interface
   */
  const sendHackerMessage = useCallback((content: string) => {
    return sendMessage(content, { interface: 'hacker_terminal' });
  }, [sendMessage]);

  const sendFuturisticMessage = useCallback((content: string) => {
    return sendMessage(content, { interface: 'futuristic' });
  }, [sendMessage]);

  const sendEnhancedMessage = useCallback((content: string) => {
    return sendMessage(content, { interface: 'enhanced' });
  }, [sendMessage]);

  return {
    // State
    messages,
    isLoading,

    // Actions
    sendMessage,
    sendHackerMessage,
    sendFuturisticMessage,
    sendEnhancedMessage,
    clearMessages,
    getConversationSummary,

    // Utilities
    messageCount: messages.length,
    lastMessage: messages[messages.length - 1] || null,
    hasEnhancedMessages: messages.some(msg => msg.enhanced),
  };
}
