import { useEffect, useRef } from 'react';
import { cacheManager } from '@/utils/cacheManager';

export const useStartupCacheClear = () => {
  const hasRun = useRef(false);

  useEffect(() => {
    // Only run once per app session
    if (hasRun.current) return;
    hasRun.current = true;

    const handleStartupCacheClear = () => {
      try {
        // Check if we're in a reload loop
        const reloadCount = parseInt(sessionStorage.getItem('reloadCount') || '0');
        const lastReloadTime = parseInt(sessionStorage.getItem('lastReloadTime') || '0');
        const currentTime = Date.now();
        
        // If we've reloaded more than 3 times in the last 30 seconds, clear everything
        if (reloadCount > 3 && (currentTime - lastReloadTime) < 30000) {
          console.warn('🔄 Infinite reload detected, clearing all cache and storage');
          
          // Clear everything to break the loop
          localStorage.clear();
          sessionStorage.clear();
          
          // Clear React Query cache
          cacheManager.clearAll();
          
          // Clear cookies
          document.cookie.split(";").forEach(function(c) { 
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
          });
          
          // Redirect to auth page to break the loop
          window.location.href = '/auth';
          return;
        }
        
        // Track reload count
        sessionStorage.setItem('reloadCount', (reloadCount + 1).toString());
        sessionStorage.setItem('lastReloadTime', currentTime.toString());
        
        // Clear reload count after 30 seconds
        setTimeout(() => {
          sessionStorage.removeItem('reloadCount');
          sessionStorage.removeItem('lastReloadTime');
        }, 30000);
        
        // Check if cache should be cleared on startup
        const shouldClear = cacheManager.shouldClearCache(30); // 30 minutes threshold
        
        if (shouldClear) {
          console.log('🧹 Clearing stale cache on startup');
          cacheManager.clearAll();
        }
        
        // Handle server restart detection
        cacheManager.handleServerRestart();
        
      } catch (error) {
        console.error('Error in startup cache clear:', error);
      }
    };

    // Run immediately
    handleStartupCacheClear();

    // Also run when the page becomes visible (tab switching)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        handleStartupCacheClear();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);
};
