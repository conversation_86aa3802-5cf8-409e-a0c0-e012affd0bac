
import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/AuthProvider";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Clock, Calendar, BookOpen, ExternalLink } from "lucide-react";
import { ThemeSwitcher } from "@/components/ThemeSwitcher";
import { VoiceNavigationButton } from "@/components/voice/VoiceNavigationButton";
import { SmallPin3D } from "@/components/ui/SmallPin3D";
import AOS from "aos";

const ClockInPage = () => {
  const { userProfile, isAuthenticated, loading } = useAuth();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isAnimating, setIsAnimating] = useState(false);

  // Debug auth state
  useEffect(() => {
    console.log('🏠 ClockInPage: Auth state:', {
      isAuthenticated,
      userProfile: userProfile ? { id: userProfile.id, role: userProfile.role } : null,
      loading
    });
  }, [isAuthenticated, userProfile, loading]);

  useEffect(() => {
    AOS.init({
      duration: 1200,
      easing: 'ease-out-cubic',
      once: false,
      mirror: true,
    });

    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleClockIn = () => {
    console.log('🏠 ClockInPage: Clock-in button clicked!');
    setIsAnimating(true);
    setTimeout(() => {
      console.log('🏠 ClockInPage: Redirecting to auth page...');
      // Always redirect to auth page for login first
      navigate('/auth');
    }, 1500);
  };

  return (
    <div className="dashboard-container relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Orbs */}
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-[#ff1c04]/30 to-[#000000]/20 rounded-full blur-3xl animate-pulse floating-animation" data-aos="fade-in" data-aos-delay="500"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-[#000000]/30 to-[#ff1c04]/20 rounded-full blur-3xl animate-pulse floating-animation" data-aos="fade-in" data-aos-delay="700" style={{ animationDelay: '1s' }}></div>

        {/* Geometric Patterns */}
        <div className="absolute top-20 left-10 w-32 h-32 border border-[#ff1c04]/20 rounded-full animate-spin" style={{ animationDuration: '20s' }}></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 border border-[#000000]/20 rounded-full animate-spin" style={{ animationDuration: '15s', animationDirection: 'reverse' }}></div>
      </div>

      {/* User Manual Link - Top Left */}
      <div className="fixed top-6 left-6 z-50" data-aos="fade-right">
        <Button
          variant="ghost"
          size="sm"
          className="text-[#000000] hover:text-[#ff1c04] hover:bg-white/10 transition-all duration-300 backdrop-blur-sm border border-white/20"
          onClick={() => window.open('/comprehensive-manual.html', '_blank')}
        >
          <BookOpen className="h-4 w-4 mr-2" />
          Comprehensive Manual
          <ExternalLink className="h-3 w-3 ml-2" />
        </Button>
      </div>

      {/* Theme Switcher */}
      <div className="fixed top-6 right-6 z-50" data-aos="fade-left">
        <ThemeSwitcher />
      </div>

      {/* AI Voice Assistant */}
      <VoiceNavigationButton
        position="bottom-right"
        size="lg"
        showTooltip={true}
      />

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex flex-col items-center justify-center px-4">
        {/* Logo Section */}
        <div className="mb-12" data-aos="zoom-in" data-aos-duration="1000">
          <div className="relative">
            <img 
              src="/lovable-uploads/491c7e61-a4fb-46a3-a002-904b84354e48.png" 
              alt="CTNL Logo" 
              className="h-20 w-auto mx-auto drop-shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-[#ff1c04]/20 to-[#000000]/20 blur-xl -z-10 animate-pulse"></div>
          </div>
        </div>

        {/* Main Clock-In Interface */}
        <div className="w-full max-w-lg">
          <Card className="clockin-card glass-card border-0 shadow-2xl" data-aos="fade-up" data-aos-delay="200">
            <CardContent className="p-10 text-center">
              {/* 3D Pin Location Indicator */}
              <div className="absolute top-4 right-4 z-10" data-aos="zoom-in" data-aos-delay="300">
                <SmallPin3D
                  location="Current Location"
                  size="sm"
                  variant="success"
                />
              </div>

              {/* 3D Circle Animation with Layers */}
              <div className="relative mb-10 flex items-center justify-center">
                <div className={`relative w-56 h-56 ${isAnimating ? 'animate-spin' : ''}`}>
                  {/* Outer Ring */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#ff1c04]/30 via-[#000000]/20 to-[#ff1c04]/30 animate-pulse blur-sm"></div>

                  {/* Middle Ring */}
                  <div className="absolute inset-3 rounded-full bg-gradient-to-r from-[#000000]/40 via-[#ff1c04]/30 to-[#000000]/40 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                  
                  {/* Inner Circle - Main Button */}
                  <div 
                    className="absolute inset-8 rounded-full bg-gradient-to-br from-[#ff1c04] via-[#e01703] to-[#cc1502] shadow-2xl hover:shadow-3xl cursor-pointer flex items-center justify-center transform hover:scale-110 transition-all duration-500 pulse-glow"
                    onClick={handleClockIn}
                  >
                    <div className="text-center text-white relative z-10">
                      <Clock className="h-10 w-10 mx-auto mb-3 drop-shadow-lg" />
                      <span className="text-xl font-bold tracking-wide">CLOCK IN</span>
                    </div>
                    
                    {/* Inner Glow Effect */}
                    <div className="absolute inset-2 rounded-full bg-gradient-to-br from-white/20 to-transparent"></div>
                  </div>
                  
                  {/* Rotating Border */}
                  <div className="absolute inset-0 rounded-full border-2 border-dashed border-[#ff1c04]/60 animate-spin" style={{ animationDuration: '12s' }}></div>
                  <div className="absolute inset-4 rounded-full border border-dashed border-[#000000]/40 animate-spin" style={{ animationDuration: '8s', animationDirection: 'reverse' }}></div>
                </div>
              </div>

              {/* Enhanced Time Display */}
              <div className="mb-8 p-6 glassmorphism rounded-2xl" data-aos="fade-up" data-aos-delay="400">
                <div className="text-4xl font-bold modern-heading mb-2">
                  {currentTime.toLocaleTimeString()}
                </div>
                <div className="text-muted-foreground text-lg font-medium">
                  {currentTime.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </div>
              </div>

              {/* Enhanced Quick Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8" data-aos="fade-up" data-aos-delay="600">
                <div className="stats-card text-center">
                  <Calendar className="h-6 w-6 mx-auto mb-2 text-[#ff1c04]" />
                  <div className="text-sm font-semibold">Today</div>
                  <div className="text-xs text-muted-foreground">Ready to start</div>
                </div>
                <div className="stats-card text-center">
                  <div className="flex justify-center mb-2">
                    <SmallPin3D
                      location="HQ Office"
                      size="md"
                      variant="default"
                    />
                  </div>
                  <div className="text-sm font-semibold">Location</div>
                  <div className="text-xs text-muted-foreground">HQ Office</div>
                </div>
              </div>

              {/* Enhanced Action Button */}
              <Button 
                onClick={handleClockIn}
                className="modern-btn w-full text-lg py-4 rounded-2xl"
                data-aos="fade-up" 
                data-aos-delay="700"
              >
                Continue to Login
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Compact Footer */}
        <div className="mt-8 text-center" data-aos="fade-up" data-aos-delay="800">
          <div className="glassmorphism rounded-xl p-4 inline-block">
            <h3 className="text-lg font-bold modern-heading mb-1">CTNL Work-Board</h3>
            <p className="text-sm text-muted-foreground">Secure • Reliable • Efficient</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClockInPage;
