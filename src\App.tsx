import { Suspense, useEffect, lazy } from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/components/auth/AuthProvider";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { EnhancedErrorBoundary } from "@/components/EnhancedErrorBoundary";
import { Loader } from "@/components/ui/Loader";
import { UnifiedDashboardLayout } from "@/components/layout/UnifiedDashboardLayout";
import { VoiceAssistant } from "@/components/voice/VoiceAssistant";
import { useStartupCacheClear } from "@/hooks/useStartupCacheClear";
import { Analytics } from "@vercel/analytics/react";
import { setupMissingTables } from "@/scripts/setup-missing-tables";

// Import responsive design system
import "@/styles/responsive-breakpoints.css";

// Import light theme border enhancements
import "@/styles/light-theme-borders.css";

// Import cache buster for PWA cleanup
import { <PERSON>ache<PERSON>uster } from "@/utils/cache-buster";

// Import error handler for clean console
import "@/utils/error-handler";

// Public Pages
import { AuthPage } from "@/pages/AuthPage";
import ClockInPage from "@/pages/ClockInPage";
import { DebugPage } from "@/pages/DebugPage";
import { EmergencySignIn } from "@/components/debug/EmergencySignIn";

// Dashboard Root
import Dashboard from "@/pages/Dashboard";

// Admin Pages
import { UserManagement } from "@/components/admin/UserManagement";
import { DepartmentManagement } from "@/components/admin/DepartmentManagement";
import { ProjectManagement } from "@/components/admin/ProjectManagement";
import { AdminReportsManagement } from "@/components/admin/ReportsManagement";
import { ActivityManagement } from "@/components/admin/ActivityManagement";
import { CommunicationCenter } from "@/components/admin/CommunicationCenter";
import SystemDiagnosticsPage from "@/pages/admin/SystemDiagnosticsPage";
import APIKeysPage from "@/pages/admin/APIKeysPage";
import AdminSettings from "@/pages/admin/AdminSettings";
import CommunicationPage from "@/pages/admin/CommunicationPage";
import DatabasePopulatePage from "@/pages/admin/DatabasePopulatePage";

// Manager Pages
import { TeamOverview } from "@/components/manager/TeamOverview";
import { InvoiceManagement } from "@/components/manager/InvoiceManagement";
import { ReportManagement } from "@/components/manager/ReportManagement";
import ManagerSettings from "@/pages/manager/ManagerSettings";
import ManagerTeamPage from "@/pages/manager/ManagerTeamPage";
import ManagerTimeTrackingPage from "@/pages/manager/ManagerTimeTrackingPage";
import ManagerWorkBoardPage from "@/pages/manager/ManagerWorkBoardPage";
import ManagerLeavePage from "@/pages/manager/ManagerLeavePage";
import ManagerSitesPage from "@/pages/manager/ManagerSitesPage";
import ManagerMeetingsPage from "@/pages/manager/ManagerMeetingsPage";
import ManagerMemosPage from "@/pages/manager/ManagerMemosPage";

// Staff Pages
import { MyTasks } from "@/components/staff/MyTasks";
import Memos from "@/pages/staff/Memos";
import Reports from "@/pages/staff/Reports";
import Meetings from "@/pages/staff/Meetings";
import StaffCurrentTasksPage from "@/pages/staff/StaffCurrentTasksPage";
import StaffMyTasksPage from "@/pages/staff/StaffMyTasksPage";
import StaffTelecomReportsPage from "@/pages/staff/StaffTelecomReportsPage";
import StaffBatteryReportsPage from "@/pages/staff/StaffBatteryReportsPage";
import StaffProfilePage from "@/pages/staff/StaffProfilePage";

// Accountant Pages
import { InvoiceManagement as AccountantInvoiceManagement } from "@/components/accountant/InvoiceManagement";

// Staff Admin Pages
import ExpenseManagement from "@/components/staff-admin/modules/ExpenseManagement";
import FleetManagement from "@/components/staff-admin/modules/FleetManagement";
import AssetInventoryManagement from "@/components/staff-admin/modules/AssetInventoryManagement";

// Asset Pages
import AssetsPage from "@/pages/assets/AssetsPage";
import ConstructionPage from "@/pages/construction/ConstructionPage";
import DocumentsPage from "@/pages/documents/DocumentsPage";
import FleetPage from "@/pages/fleet/FleetPage";
import FinancialPage from "@/pages/financial/FinancialPage";
import BatteryPage from "@/pages/battery/BatteryPage";
import TasksPage from "@/pages/tasks/TasksPage";
import ReportsPage from "@/pages/reports/ReportsPage";
import SettingsPage from "@/pages/settings/SettingsPage";

// Account and Files Pages
import AccountPage from "@/pages/account/AccountPage";
import FilesPage from "@/pages/files/FilesPage";

// Lazy-loaded AI Page
const AIPage = lazy(() => import("@/pages/ai/AIPage"));
import IntegrationsPage from "@/pages/admin/IntegrationsPage";

// 404 Fallback
const NotFound = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">404</h1>
      <p className="text-gray-600 dark:text-gray-400 mt-2">Page not found</p>
      <button
        onClick={() => window.history.back()}
        className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
      >
        Go Back
      </button>
    </div>
  </div>
);

function App() {
  // Prevent stuck cache states
  useStartupCacheClear();

  useEffect(() => {
    // Only initialize cache buster in development or when manually triggered
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const manualCacheInit = localStorage.getItem('manual-cache-init') === 'true';

    if (isDevelopment || manualCacheInit) {
      console.log('🧹 Initializing cache buster...');
      CacheBuster.initialize().catch(error => {
        console.error('Cache buster initialization failed:', error);
      });

      // Remove manual flag after initialization
      if (manualCacheInit) {
        localStorage.removeItem('manual-cache-init');
      }
    }

    // Setup missing database tables
    setupMissingTables().catch(console.error);
  }, []);

  return (
    <EnhancedErrorBoundary>
      <ErrorBoundary>
        <TooltipProvider>
          <BrowserRouter>
            <AuthProvider>
              <div className="min-h-screen bg-background">
                <Suspense fallback={<Loader />}>
                  <Routes>
                  {/* Public Routes */}
                  <Route path="/" element={<ClockInPage />} />
                  <Route path="/clockin" element={<ClockInPage />} />
                  <Route path="/auth" element={<AuthPage />} />
                  <Route path="/login" element={<Navigate to="/auth" replace />} />
                  <Route path="/debug" element={<DebugPage />} />
                  <Route path="/emergency-signin" element={<EmergencySignIn />} />

                  {/* Unified Dashboard Routes */}
                  <Route path="/dashboard/*" element={<UnifiedDashboardLayout />} />

                  {/* Legacy Role Redirects */}
                  <Route path="/admin" element={<Navigate to="/dashboard/admin" replace />} />
                  <Route path="/manager" element={<Navigate to="/dashboard/manager" replace />} />
                  <Route path="/staff" element={<Navigate to="/dashboard/staff" replace />} />
                  <Route path="/accountant" element={<Navigate to="/dashboard/accountant" replace />} />
                  <Route path="/staff-admin" element={<Navigate to="/dashboard/staff-admin" replace />} />

                  {/* 404 Fallback */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Suspense>
            </div>
            <Toaster />
            <VoiceAssistant />
            <Analytics />
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </ErrorBoundary>
    </EnhancedErrorBoundary>
  );
}

export default App;
