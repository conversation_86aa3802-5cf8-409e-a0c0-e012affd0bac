// @ts-nocheck
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

// Profile type removed - using Supabase generated types instead

// Helper function to safely execute Supabase queries with proper error handling
async function safeSupabaseQuery(queryFn: () => any): Promise<{ data: any; error: any }> {
  try {
    const result = await queryFn();
    if (result && typeof result.then === 'function') {
      // If it's a promise, await it
      return await result;
    }
    // If it's already resolved, return it
    return result;
  } catch (error: any) {
    console.error('Supabase query error:', error);
    return { data: null, error: error.message || 'Database query failed' };
  }
}

// ============= TYPES =============
type APIResponse<T> = {
  data: T | null;
  error: Error | null;
  success: boolean;
  message?: string;
};

type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// ============= ENHANCED API UTILITIES =============
import { errorHandler, ERROR_CODES, type ErrorContext } from './error-handler';
import { logger } from './logger';

class APIService {
  // Add missing properties
  public endpoints: Record<string, Function> = {};
  public admin: any = {};

  private async executeWithNotification<T>(
    operation: () => Promise<any>,
    method: HTTPMethod,
    resource: string,
    showNotification = true
  ): Promise<APIResponse<T>> {
    const startTime = performance.now();
    const operationId = `${method}_${resource}_${Date.now()}`;

    try {
      logger.debug('api', `Starting API operation: ${method} ${resource}`, {
        operationId,
        method,
        resource,
      });

      const result = await operation();
      const duration = performance.now() - startTime;

      if (result.error) {
        throw new Error(result.error.message || 'Operation failed');
      }

      const success = true;
      const message = this.getSuccessMessage(method, resource);

      // Log successful API call
      logger.logAPICall(method, resource, 200, duration, {
        operationId,
        dataSize: result.data ? JSON.stringify(result.data).length : 0,
      });

      if (showNotification && method !== 'GET') {
        toast({
          title: "✅ Success",
          description: message,
          variant: "default",
        });
      }

      return {
        data: result.data,
        error: null,
        success,
        message
      };
    } catch (error: any) {
      const duration = performance.now() - startTime;
      const errorMessage = error.message || 'An unexpected error occurred';

      // Determine error category and code
      let errorCode: string = ERROR_CODES.API_INVALID_REQUEST;
      let errorCategory: 'api' | 'database' | 'network' = 'api';

      const isDatabaseError = errorMessage.includes('column') ||
                             errorMessage.includes('relation') ||
                             errorMessage.includes('does not exist') ||
                             errorMessage.includes('connection') ||
                             errorMessage.includes('database');

      const isNetworkError = errorMessage.includes('fetch') ||
                            errorMessage.includes('network') ||
                            errorMessage.includes('timeout');

      if (isDatabaseError) {
        errorCode = ERROR_CODES.DB_QUERY_FAILED;
        errorCategory = 'database';
      } else if (isNetworkError) {
        errorCode = ERROR_CODES.NETWORK_CONNECTION_LOST;
        errorCategory = 'network';
      }

      // Create error context
      const context: ErrorContext = {
        function: `API_${method}`,
        metadata: {
          operationId,
          method,
          resource,
          duration,
          url: typeof window !== 'undefined' ? window.location.href : undefined,
        },
      };

      // Handle error through centralized error handler
      const appError = errorHandler.createError(
        errorCode,
        errorMessage,
        errorCategory,
        context,
        error
      );

      // Log API error
      logger.logAPICall(method, resource, error.status || 500, duration, {
        operationId,
        error: errorMessage,
        errorCode: appError.code,
        errorId: appError.id,
      });

      // Don't show notifications for database errors on API key operations
      // as we expect to fall back to demo mode
      const isAPIKeyOperation = resource.toLowerCase().includes('api key');

      if (showNotification && !isDatabaseError && !isAPIKeyOperation) {
        errorHandler.handleError(appError, context, true);
      } else {
        // Log without showing toast
        errorHandler.handleError(appError, context, false);
      }

      return {
        data: null,
        error: new Error(errorMessage),
        success: false,
        message: errorMessage
      };
    }
  }

  private getSuccessMessage(method: HTTPMethod, resource: string): string {
    const messages = {
      POST: `${resource} created successfully`,
      PUT: `${resource} updated successfully`,
      PATCH: `${resource} updated successfully`,
      DELETE: `${resource} deleted successfully`,
      GET: `${resource} retrieved successfully`
    };
    return messages[method];
  }

  // Add missing matchRoute method
  private matchRoute(pattern: string, endpoint: string): boolean {
    // Simple pattern matching for parameterized routes
    const patternParts = pattern.split('/');
    const endpointParts = endpoint.split('/');

    if (patternParts.length !== endpointParts.length) {
      return false;
    }

    for (let i = 0; i < patternParts.length; i++) {
      const patternPart = patternParts[i];
      const endpointPart = endpointParts[i];

      // Skip parameter parts (starting with :)
      if (patternPart.startsWith(':')) {
        continue;
      }

      if (patternPart !== endpointPart) {
        return false;
      }
    }

    return true;
  }

  // ============= ENHANCED AUTH API =============
  auth = {
    getCurrentUser: async () => {
      return this.executeWithNotification(
        () => supabase.auth.getUser(),
        'GET',
        'User session',
        false
      );
    },

    getUserProfile: async (userId: string) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .select(`*, department:departments(id, name, description)`)
            .eq('id', userId)
            .single();
          return { data, error };
        },
        'GET',
        'User profile',
        false
      );
    },

    signOut: async () => {
      return this.executeWithNotification(
        () => supabase.auth.signOut(),
        'POST',
        'Sign out'
      );
    },

    updateProfile: async (userId: string, userData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .update(userData)
            .eq('id', userId)
            .select()
            .single();
          return { data, error };
        },
        'PATCH',
        'Profile'
      );
    }
  };

  // ============= ENHANCED USERS API =============
  users = {
    getAll: async () => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .select(`*, department:departments(id, name, description)`)
            .order('created_at', { ascending: false });
          return { data, error };
        },
        'GET',
        'Users',
        false
      );
    },

    getById: async (userId: string) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .select(`*, department:departments(id, name, description)`)
            .eq('id', userId)
            .single();
          return { data, error };
        },
        'GET',
        'User',
        false
      );
    },

    create: async (userData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .insert(userData)
            .select()
            .single();
          return { data, error };
        },
        'POST',
        'User'
      );
    },

    update: async (userId: string, userData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .update({
              ...userData,
              updated_at: new Date().toISOString()
            })
            .eq('id', userId)
            .select()
            .single();
          return { data, error };
        },
        'PUT',
        'User'
      );
    },

    patch: async (userId: string, patchData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .update({
              ...patchData,
              updated_at: new Date().toISOString()
            })
            .eq('id', userId)
            .select()
            .single();
          return { data, error };
        },
        'PATCH',
        'User'
      );
    },

    delete: async (userId: string) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .delete()
            .eq('id', userId);
          return { data, error };
        },
        'DELETE',
        'User'
      );
    },

    bulkUpdate: async (userIds: string[], userData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .update({
              ...userData,
              updated_at: new Date().toISOString()
            })
            .in('id', userIds)
            .select();
          return { data, error };
        },
        'PATCH',
        'Users (bulk operation)'
      );
    },

    bulkDelete: async (userIds: string[]) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .delete()
            .in('id', userIds);
          return { data, error };
        },
        'DELETE',
        'Users (bulk operation)'
      );
    }
  };

  // ============= ENHANCED PROJECTS API =============
  projects = {
    getAll: async (filters?: { status?: string; managerId?: string }) => {
      return this.executeWithNotification(
        async () => {
          let query = supabase
            .from('projects')
            .select(`*, manager:profiles!projects_manager_id_fkey(id, full_name, email)`);

          if (filters?.status) query = query.eq('status', filters.status);
          if (filters?.managerId) query = query.eq('manager_id', filters.managerId);

          const { data, error } = await query.order('created_at', { ascending: false });
          return { data, error };
        },
        'GET',
        'Projects',
        false
      );
    },

    getById: async (projectId: string) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('projects')
            .select(`*, manager:profiles!manager_id(id, full_name, email), tasks(*)`)
            .eq('id', projectId)
            .single();
          return { data, error };
        },
        'GET',
        'Project',
        false
      );
    },

    create: async (projectData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('projects')
            .insert(projectData)
            .select()
            .single();
          return { data, error };
        },
        'POST',
        'Project'
      );
    },

    update: async (projectId: string, projectData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('projects')
            .update({
              ...projectData,
              updated_at: new Date().toISOString()
            })
            .eq('id', projectId)
            .select()
            .single();
          return { data, error };
        },
        'PUT',
        'Project'
      );
    },

    patch: async (projectId: string, patchData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('projects')
            .update({
              ...patchData,
              updated_at: new Date().toISOString()
            })
            .eq('id', projectId)
            .select()
            .single();
          return { data, error };
        },
        'PATCH',
        'Project'
      );
    },

    updateStatus: async (projectId: string, status: string) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('projects')
            .update({
              status,
              updated_at: new Date().toISOString()
            })
            .eq('id', projectId)
            .select()
            .single();
          return { data, error };
        },
        'PATCH',
        'Project status'
      );
    },

    getProjectsWithAssignments: async () => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('projects')
            .select(`
              *,
              manager:profiles!manager_id(id, full_name, email),
              project_assignments(
                id,
                staff_id,
                profiles:profiles!staff_id(id, full_name, email, role)
              )
            `)
            .order('created_at', { ascending: false });
          return { data, error };
        },
        'GET',
        'Projects with assignments',
        false
      );
    },

    delete: async (projectId: string) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('projects')
            .delete()
            .eq('id', projectId);
          return { data, error };
        },
        'DELETE',
        'Project'
      );
    }
  };

  // ============= ENHANCED TASKS API =============
  tasks = {
    getAll: async (filters?: { assignedTo?: string; projectId?: string; status?: string }) => {
      return this.executeWithNotification(
        async () => {
          let query = supabase
            .from('tasks')
            .select(`
              *,
              assigned_to:profiles!assigned_to_id(id, full_name, email),
              created_by:profiles!created_by_id(id, full_name, email),
              project:projects(id, name)
            `);

          if (filters?.assignedTo) query = query.eq('assigned_to_id', filters.assignedTo);
          if (filters?.projectId) query = query.eq('project_id', filters.projectId);
          if (filters?.status) query = query.eq('status', filters.status);

          const { data, error } = await query.order('created_at', { ascending: false });
          return { data, error };
        },
        'GET',
        'Tasks',
        false
      );
    },

    create: async (taskData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('tasks')
            .insert(taskData)
            .select()
            .single();
          return { data, error };
        },
        'POST',
        'Task'
      );
    },

    update: async (taskId: string, taskData: any) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('tasks')
            .update(taskData)
            .eq('id', taskId)
            .select()
            .single();
          return { data, error };
        },
        'PUT',
        'Task'
      );
    },

    delete: async (taskId: string) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('tasks')
            .delete()
            .eq('id', taskId);
          return { data, error };
        },
        'DELETE',
        'Task'
      );
    },

    updateStatus: async (taskId: string, status: string) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('tasks')
            .update({ status, updated_at: new Date().toISOString() })
            .eq('id', taskId)
            .select()
            .single();
          return { data, error };
        },
        'PATCH',
        'Task status'
      );
    },

    bulkAssign: async (taskIds: string[], assigneeId: string) => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('tasks')
            .update({ assigned_to_id: assigneeId, updated_at: new Date().toISOString() })
            .in('id', taskIds)
            .select();
          return { data, error };
        },
        'PATCH',
        'Tasks (bulk assignment)'
      );
    }
  };

  // ============= ENHANCED REPORTS API =============
  reports = {
    battery: {
      getAll: async (filters?: { reporterId?: string; siteId?: string }) => {
        return this.executeWithNotification(
          async () => {
            let query = supabase
              .from('battery_reports')
              .select(`*, reporter:profiles!reporter_id(id, full_name, email)`);

            if (filters?.reporterId) query = query.eq('reporter_id', filters.reporterId);
            if (filters?.siteId) query = query.eq('site_id', filters.siteId);

            const { data, error } = await query.order('created_at', { ascending: false });
            return { data, error };
          },
          'GET',
          'Battery reports',
          false
        );
      },

      create: async (reportData: any) => {
        return this.executeWithNotification(
          async () => {
            const { data, error } = await supabase
              .from('battery_reports')
              .insert(reportData)
              .select()
              .single();
            return { data, error };
          },
          'POST',
          'Battery report'
        );
      },

      update: async (reportId: string, reportData: any) => {
        return this.executeWithNotification(
          async () => {
            const { data, error } = await supabase
              .from('battery_reports')
              .update(reportData)
              .eq('id', reportId)
              .select()
              .single();
            return { data, error };
          },
          'PUT',
          'Battery report'
        );
      },

      delete: async (reportId: string) => {
        return this.executeWithNotification(
          async () => {
            const { data, error } = await supabase
              .from('battery_reports')
              .delete()
              .eq('id', reportId);
            return { data, error };
          },
          'DELETE',
          'Battery report'
        );
      }
    },

    telecom: {
      getAll: async (filters?: { reporterId?: string; siteId?: string }) => {
        return this.executeWithNotification(
          async () => {
            let query = supabase
              .from('ct_power_reports')
              .select(`*, created_by:profiles!created_by(id, full_name, email)`);

            if (filters?.reporterId) query = query.eq('created_by', filters.reporterId);
            if (filters?.siteId) query = query.eq('site_id', filters.siteId);

            const { data, error } = await query.order('created_at', { ascending: false });
            return { data, error };
          },
          'GET',
          'Telecom reports',
          false
        );
      },

      create: async (reportData: any) => {
        return this.executeWithNotification(
          async () => {
            const { data, error } = await supabase
              .from('ct_power_reports')
              .insert(reportData)
              .select()
              .single();
            return { data, error };
          },
          'POST',
          'Telecom report'
        );
      },

      update: async (reportId: string, reportData: any) => {
        return this.executeWithNotification(
          async () => {
            const { data, error } = await supabase
              .from('ct_power_reports')
              .update(reportData)
              .eq('id', reportId)
              .select()
              .single();
            return { data, error };
          },
          'PUT',
          'Telecom report'
        );
      }
    },

    construction: {
      // Construction Sites Management
      sites: {
        getAll: async (filters?: { status?: string; siteType?: string; managerId?: string }) => {
          return this.executeWithNotification(
            () => {
              let query = supabase
                .from('construction_sites')
                .select(`
                  *,
                  project_manager:profiles!project_manager_id(id, full_name, email)
                `);

              if (filters?.status) query = query.eq('status', filters.status);
              if (filters?.siteType) query = query.eq('site_type', filters.siteType);
              if (filters?.managerId) query = query.eq('project_manager_id', filters.managerId);

              return query.order('created_at', { ascending: false });
            },
            'GET',
            'Construction sites',
            false
          );
        },

        getById: async (id: string) => {
          return this.executeWithNotification(
            () => supabase
              .from('construction_sites')
              .select(`
                *,
                project_manager:profiles!project_manager_id(id, full_name, email)
              `)
              .eq('id', id)
              .single(),
            'GET',
            'Construction site',
            false
          );
        },

        create: async (siteData: any) => {
          return this.executeWithNotification(
            () => supabase
              .from('construction_sites')
              .insert(siteData)
              .select(`
                *,
                project_manager:profiles!project_manager_id(id, full_name, email)
              `)
              .single(),
            'POST',
            'Construction site'
          );
        },

        update: async (id: string, siteData: any) => {
          return this.executeWithNotification(
            () => supabase
              .from('construction_sites')
              .update(siteData)
              .eq('id', id)
              .select(`
                *,
                project_manager:profiles!project_manager_id(id, full_name, email)
              `)
              .single(),
            'PATCH',
            'Construction site'
          );
        },

        delete: async (id: string) => {
          return this.executeWithNotification(
            () => supabase
              .from('construction_sites')
              .delete()
              .eq('id', id),
            'DELETE',
            'Construction site'
          );
        },

        getStatistics: async () => {
          return this.executeWithNotification(
            async () => {
              const { data: sites, error: sitesError } = await supabase
                .from('construction_sites')
                .select('id, status, budget_allocated, budget_spent');

              if (sitesError) throw sitesError;

              const { data: reports, error: reportsError } = await supabase
                .from('construction_reports')
                .select('id, safety_incidents');

              if (reportsError) throw reportsError;

              const stats = {
                totalSites: sites?.length || 0,
                activeSites: sites?.filter(s => s.status === 'active').length || 0,
                completedSites: sites?.filter(s => s.status === 'completed').length || 0,
                planningSites: sites?.filter(s => s.status === 'planning').length || 0,
                totalBudget: sites?.reduce((sum, s) => sum + (s.budget_allocated || 0), 0) || 0,
                totalSpent: sites?.reduce((sum, s) => sum + (s.budget_spent || 0), 0) || 0,
                safetyIncidents: reports?.filter(r => r.safety_incidents && r.safety_incidents.trim() !== '').length || 0
              };

              return { data: stats, error: null };
            },
            'GET',
            'Construction statistics',
            false
          );
        }
      },

      // Construction Reports Management
      reports: {
        getAll: async (filters?: { siteId?: string; reportType?: string; createdBy?: string; dateFrom?: string; dateTo?: string }) => {
          return this.executeWithNotification(
            () => {
              let query = supabase
                .from('construction_reports')
                .select(`
                  *,
                  site:construction_sites!site_id(id, site_name, location),
                  created_by:profiles!created_by(id, full_name, email)
                `);

              if (filters?.siteId) query = query.eq('site_id', filters.siteId);
              if (filters?.reportType) query = query.eq('report_type', filters.reportType);
              if (filters?.createdBy) query = query.eq('created_by', filters.createdBy);
              if (filters?.dateFrom) query = query.gte('report_date', filters.dateFrom);
              if (filters?.dateTo) query = query.lte('report_date', filters.dateTo);

              return query.order('created_at', { ascending: false });
            },
            'GET',
            'Construction reports',
            false
          );
        },

        getById: async (id: string) => {
          return this.executeWithNotification(
            () => supabase
              .from('construction_reports')
              .select(`
                *,
                site:construction_sites!site_id(id, site_name, location),
                created_by:profiles!created_by(id, full_name, email)
              `)
              .eq('id', id)
              .single(),
            'GET',
            'Construction report',
            false
          );
        },

        create: async (reportData: any) => {
          return this.executeWithNotification(
            () => supabase
              .from('construction_reports')
              .insert(reportData)
              .select(`
                *,
                site:construction_sites!site_id(id, site_name, location),
                created_by:profiles!created_by(id, full_name, email)
              `)
              .single(),
            'POST',
            'Construction report'
          );
        },

        update: async (id: string, reportData: any) => {
          return this.executeWithNotification(
            () => supabase
              .from('construction_reports')
              .update(reportData)
              .eq('id', id)
              .select(`
                *,
                site:construction_sites!site_id(id, site_name, location),
                created_by:profiles!created_by(id, full_name, email)
              `)
              .single(),
            'PATCH',
            'Construction report'
          );
        },

        delete: async (id: string) => {
          return this.executeWithNotification(
            () => supabase
              .from('construction_reports')
              .delete()
              .eq('id', id),
            'DELETE',
            'Construction report'
          );
        },

        getBySite: async (siteId: string) => {
          return this.executeWithNotification(
            () => supabase
              .from('construction_reports')
              .select(`
                *,
                created_by:profiles!created_by(id, full_name, email)
              `)
              .eq('site_id', siteId)
              .order('created_at', { ascending: false }),
            'GET',
            'Site construction reports',
            false
          );
        }
      }
    }
  };

  // ============= ENHANCED FINANCIAL API =============
  financial = {
    invoices: {
      getAll: async (filters?: { status?: string; clientId?: string }) => {
        return this.executeWithNotification(
          () => {
            let query = supabase
              .from('accounts_invoices')
              .select(`*, created_by:profiles!created_by(id, full_name, email)`);

            if (filters?.status) query = query.eq('payment_status', filters.status);
            if (filters?.clientId) query = query.eq('client_id', filters.clientId);

            return query.order('created_at', { ascending: false });
          },
          'GET',
          'Invoices',
          false
        );
      },

      create: async (invoiceData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('accounts_invoices')
            .insert(invoiceData)
            .select()
            .single(),
          'POST',
          'Invoice'
        );
      },

      update: async (invoiceId: string, invoiceData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('accounts_invoices')
            .update(invoiceData)
            .eq('id', invoiceId)
            .select()
            .single(),
          'PUT',
          'Invoice'
        );
      },

      updateStatus: async (invoiceId: string, status: string) => {
        return this.executeWithNotification(
          () => supabase
            .from('accounts_invoices')
            .update({ payment_status: status, updated_at: new Date().toISOString() })
            .eq('id', invoiceId)
            .select()
            .single(),
          'PATCH',
          'Invoice status'
        );
      }
    },

    expenses: {
      getAll: async (filters?: { userId?: string; category?: string }) => {
        return this.executeWithNotification(
          () => {
            let query = supabase
              .from('expenses')
              .select(`*, created_by:profiles!created_by(id, full_name, email)`);

            if (filters?.userId) query = query.eq('created_by', filters.userId);
            if (filters?.category) query = query.eq('category', filters.category);

            return query.order('created_at', { ascending: false });
          },
          'GET',
          'Expenses',
          false
        );
      },

      create: async (expenseData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('expenses')
            .insert(expenseData)
            .select()
            .single(),
          'POST',
          'Expense'
        );
      },

      update: async (expenseId: string, expenseData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('expenses')
            .update(expenseData)
            .eq('id', expenseId)
            .select()
            .single(),
          'PUT',
          'Expense'
        );
      },

      delete: async (expenseId: string) => {
        return this.executeWithNotification(
          () => supabase
            .from('expenses')
            .delete()
            .eq('id', expenseId),
          'DELETE',
          'Expense'
        );
      }
    },

    transactions: {
      getAll: async (filters?: { type?: string; status?: string; dateFrom?: string; dateTo?: string }) => {
        return this.executeWithNotification(
          () => {
            let query = supabase
              .from('payment_transactions')
              .select(`
                *,
                invoice:accounts_invoices(id, invoice_number, client_name, total_amount),
                created_by:profiles!created_by(id, full_name, email),
                processed_by:profiles!processed_by(id, full_name, email)
              `);

            if (filters?.type) query = query.eq('transaction_type', filters.type);
            if (filters?.status) query = query.eq('status', filters.status);
            if (filters?.dateFrom) query = query.gte('transaction_date', filters.dateFrom);
            if (filters?.dateTo) query = query.lte('transaction_date', filters.dateTo);

            return query.order('created_at', { ascending: false });
          },
          'GET',
          'Transactions',
          false
        );
      },

      create: async (transactionData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('payment_transactions')
            .insert(transactionData)
            .select()
            .single(),
          'POST',
          'Transaction'
        );
      },

      update: async (transactionId: string, transactionData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('payment_transactions')
            .update(transactionData)
            .eq('id', transactionId)
            .select()
            .single(),
          'PUT',
          'Transaction'
        );
      },

      updateStatus: async (transactionId: string, status: string) => {
        return this.executeWithNotification(
          () => supabase
            .from('payment_transactions')
            .update({ status, updated_at: new Date().toISOString() })
            .eq('id', transactionId)
            .select()
            .single(),
          'PUT',
          'Transaction Status'
        );
      },

      delete: async (transactionId: string) => {
        return this.executeWithNotification(
          () => supabase
            .from('payment_transactions')
            .delete()
            .eq('id', transactionId),
          'DELETE',
          'Transaction'
        );
      },

      getById: async (transactionId: string) => {
        return this.executeWithNotification(
          () => supabase
            .from('payment_transactions')
            .select(`
              *,
              invoice:accounts_invoices(id, invoice_number, client_name, total_amount),
              created_by:profiles!created_by(id, full_name, email),
              processed_by:profiles!processed_by(id, full_name, email)
            `)
            .eq('id', transactionId)
            .single(),
          'GET',
          'Transaction',
          false
        );
      },

      getSummary: async (dateRange?: { from: string; to: string }) => {
        return this.executeWithNotification(
          () => {
            let query = supabase
              .from('payment_transactions')
              .select('amount, transaction_type, status, transaction_date');

            if (dateRange) {
              query = query
                .gte('transaction_date', dateRange.from)
                .lte('transaction_date', dateRange.to);
            }

            return query;
          },
          'GET',
          'Transaction Summary',
          false
        );
      }
    }
  };

  // ============= ENHANCED DOCUMENTS API =============
  documents = {
    list: async (filters?: { folder_id?: string; category?: string; search?: string }) => {
      return this.executeWithNotification(
        () => {
          let query = supabase
            .from('document_archive')
            .select(`
              *,
              uploader:profiles!uploaded_by(id, full_name, email),
              department:departments!department_id(id, name)
            `);

          if (filters?.folder_id) query = query.eq('department_id', filters.folder_id);
          if (filters?.category && filters.category !== 'all') query = query.eq('file_type', filters.category);
          if (filters?.search) {
            query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
          }

          return query.order('created_at', { ascending: false });
        },
        'GET',
        'Documents',
        false
      );
    },

    getById: async (documentId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('document_archive')
          .select(`
            *,
            uploader:profiles!uploaded_by(id, full_name, email),
            department:departments!department_id(id, name)
          `)
          .eq('id', documentId)
          .single(),
        'GET',
        'Document',
        false
      );
    },

    create: async (documentData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('document_archive')
          .insert(documentData)
          .select()
          .single(),
        'POST',
        'Document'
      );
    },

    update: async (documentId: string, documentData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('document_archive')
          .update(documentData)
          .eq('id', documentId)
          .select()
          .single(),
        'PUT',
        'Document'
      );
    },

    delete: async (documentId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('document_archive')
          .delete()
          .eq('id', documentId),
        'DELETE',
        'Document'
      );
    },

    getFolders: async () => {
      return this.executeWithNotification(
        () => supabase
          .from('departments')
          .select('id, name, description')
          .order('name', { ascending: true }),
        'GET',
        'Folders',
        false
      );
    },

    createFolder: async (folderData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('departments')
          .insert({
            name: folderData.name,
            description: folderData.description
          })
          .select()
          .single(),
        'POST',
        'Folder'
      );
    },

    updateFolder: async (folderId: string, folderData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('departments')
          .update({
            name: folderData.name,
            description: folderData.description
          })
          .eq('id', folderId)
          .select()
          .single(),
        'PUT',
        'Folder'
      );
    },

    deleteFolder: async (folderId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('departments')
          .delete()
          .eq('id', folderId),
        'DELETE',
        'Folder'
      );
    },

    // Root level folder creation for system types
    createRootFolder: async (folderData: { name: string; description?: string; type?: string }) => {
      return this.executeWithNotification(
        () => supabase
          .from('departments')
          .insert({
            name: folderData.name,
            description: folderData.description || `Root level ${folderData.type || 'folder'}`,
            manager_id: null // Root level folders don't have managers
          })
          .select()
          .single(),
        'POST',
        'Root folder'
      );
    }
  };

  // ============= ENHANCED SYSTEM API =============
  system = {
    getDashboardStats: async () => {
      return this.executeWithNotification(
        async () => {
          const [users, projects, tasks, activities] = await Promise.all([
            supabase.from('profiles').select('id, role'),
            supabase.from('projects').select('id, status'),
            supabase.from('tasks').select('id, status, priority'),
            supabase.from('system_activities').select('*').limit(10)
          ]);

          return {
            data: {
              users: users.data || [],
              projects: projects.data || [],
              tasks: tasks.data || [],
              activities: activities.data || []
            },
            error: null
          };
        },
        'GET',
        'Dashboard stats',
        false
      );
    },

    getActivityLogs: async (limit = 10) => {
      return this.executeWithNotification(
        async () => {
          try {
            // Use manual join approach to avoid foreign key issues
            const { data: activitiesData, error: activitiesError } = await supabase
              .from('system_activities')
              .select('*')
              .order('created_at', { ascending: false })
              .limit(limit);

            if (activitiesError) {
              console.error('Activities query failed:', activitiesError);
              throw activitiesError;
            }

            if (!activitiesData || activitiesData.length === 0) {
              console.log('No activity logs found');
              return [];
            }
            
            // Get unique user IDs from activities
            const userIds = [...new Set(activitiesData?.map(a => a.user_id).filter(Boolean) || [])];
            
            // Get profiles for these user IDs if any exist
            let profilesData = [];
            if (userIds.length > 0) {
              const { data: profiles, error: profilesError } = await supabase
                .from('profiles')
                .select('id, full_name, email')
                .in('id', userIds);
              
              if (!profilesError && profiles) {
                profilesData = profiles;
              } else {
                console.warn('Profiles query failed:', profilesError);
              }
            }
            
            // Create a map of profiles for quick lookup
            const profilesMap = new Map();
            profilesData.forEach(profile => {
              profilesMap.set(profile.id, profile);
            });
            
            // Transform the data to match the expected format
            const transformedData = activitiesData?.map(activity => ({
              ...activity,
              action: activity.action || activity.type,
              user: activity.user_id ? profilesMap.get(activity.user_id) || null : null
            })) || [];
            
            return transformedData;
          } catch (error) {
            console.error('Error in getActivityLogs:', error);
            
            // Return fallback data with proper structure
            return [
              {
                id: '1',
                type: 'system_startup',
                action: 'system_startup',
                description: 'System initialized successfully',
                user_id: null,
                user: null,
                created_at: new Date().toISOString(),
                metadata: { version: '1.0.0' }
              },
              {
                id: '2',
                type: 'user_login',
                action: 'user_login',
                description: 'User logged in to the system',
                user_id: null,
                user: { id: 'demo', full_name: 'Demo User', email: '<EMAIL>' },
                created_at: new Date(Date.now() - 300000).toISOString(),
                metadata: { session_id: 'demo-session' }
              },
              {
                id: '3',
                type: 'data_sync',
                action: 'data_sync',
                description: 'Database synchronization completed',
                user_id: null,
                user: null,
                created_at: new Date(Date.now() - 600000).toISOString(),
                metadata: { records: 150 }
              }
            ];
          }
        },
        'GET',
        'Activity logs',
        false
      );
    },

    createActivity: async (activityData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('system_activities')
          .insert(activityData)
          .select()
          .single(),
        'POST',
        'Activity log',
        false
      );
    },

    // Helper function to populate sample activity data
    populateSampleActivities: async () => {
      return this.executeWithNotification(
        async () => {
          const { data: currentUser } = await supabase.auth.getUser();
          const userId = currentUser.user?.id;
          
          const sampleActivities = [
            {
              type: 'system_startup',
              description: 'System initialized successfully',
              user_id: null,
              metadata: { version: '1.0.0', timestamp: new Date().toISOString() }
            },
            {
              type: 'user_login',
              description: 'User logged in to the system',
              user_id: userId,
              metadata: { session_id: 'session-' + Math.random().toString(36).substring(2, 11) }
            },
            {
              type: 'data_sync',
              description: 'Database synchronization completed',
              user_id: null,
              metadata: { records_synced: 150, duration: '2.3s' }
            },
            {
              type: 'user_action',
              description: 'User accessed activity management page',
              user_id: userId,
              metadata: { page: 'activity-management', action: 'view' }
            },
            {
              type: 'system_maintenance',
              description: 'Database schema updated',
              user_id: null,
              metadata: { operation: 'migration', version: '20250102060000' }
            }
          ];
          
          const { data, error } = await supabase
            .from('system_activities')
            .insert(sampleActivities)
            .select();
          
          if (error) throw error;
          return data;
        },
        'POST',
        'Sample activity data',
        false
      );
    }
  };

  // ============= NOTIFICATION SYSTEM =============
  notifications = {
    // Send in-app notification
    sendInAppNotification: async (data: {
      recipientId: string;
      title: string;
      message: string;
      type: 'info' | 'success' | 'warning' | 'error';
      actionUrl?: string;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase.from('notifications').insert([{
          recipient_id: data.recipientId,
          title: data.title,
          message: data.message,
          type: data.type,
          action_url: data.actionUrl,
          is_read: false,
        }]),
        "Notification sent successfully",
        "Failed to send notification"
      );
    },

    // Send leave request notification to managers
    sendLeaveRequestNotification: async (data: {
      leaveRequestId: string;
      employeeName: string;
      leaveType: string;
      startDate: string;
      endDate: string;
      reason: string;
    }): Promise<APIResponse<any>> => {
      try {
        // Get all managers
        const { data: managers, error: managersError } = await supabase
          .from('profiles')
          .select('id, full_name, email')
          .in('role', ['manager', 'admin']);

        if (managersError) throw managersError;

        // Send in-app notifications to all managers
        const notificationPromises = managers.map(manager => 
          supabase.from('notifications').insert([{
            recipient_id: manager.id,
            title: 'New Leave Request',
            message: `${data.employeeName} has submitted a leave request for ${data.leaveType} leave from ${data.startDate} to ${data.endDate}.`,
            type: 'info',
            action_url: '/dashboard/manager/leave',
            is_read: false,
          }])
        );

        await Promise.all(notificationPromises);

        // Send email notifications
        try {
          await supabase.functions.invoke('send-notification', {
            body: {
              type: 'leave_request_submitted',
              recipients: managers.map(m => m.email).filter(Boolean),
              data: {
                employeeName: data.employeeName,
                leaveType: data.leaveType,
                startDate: data.startDate,
                endDate: data.endDate,
                reason: data.reason,
                leaveRequestId: data.leaveRequestId,
              }
            }
          });
        } catch (emailError) {
          console.warn('Failed to send email notifications:', emailError);
          // Don't fail the whole operation if email fails
        }

        return {
          data: { success: true },
          error: null,
          success: true,
          message: "Leave request notifications sent successfully"
        };
      } catch (error: any) {
        return {
          data: null,
          error: error.message,
          success: false,
          message: "Failed to send leave request notifications"
        };
      }
    },

    // Send leave status update notification
    sendLeaveStatusNotification: async (data: {
      leaveRequestId: string;
      employeeId: string;
      employeeName: string;
      status: 'approved' | 'rejected';
      approverName: string;
      rejectionReason?: string;
    }): Promise<APIResponse<any>> => {
      try {
        const isApproved = data.status === 'approved';
        const title = isApproved ? 'Leave Request Approved' : 'Leave Request Rejected';
        const message = isApproved 
          ? `Your leave request has been approved by ${data.approverName}.`
          : `Your leave request has been rejected by ${data.approverName}. ${data.rejectionReason ? `Reason: ${data.rejectionReason}` : ''}`;

        // Send in-app notification
        await supabase.from('notifications').insert([{
          recipient_id: data.employeeId,
          title,
          message,
          type: isApproved ? 'success' : 'error',
          action_url: '/dashboard/staff/profile',
          is_read: false,
        }]);

        // Send email notification
        try {
          const { data: employee } = await supabase
            .from('profiles')
            .select('email')
            .eq('id', data.employeeId)
            .single();

          if (employee?.email) {
            await supabase.functions.invoke('send-notification', {
              body: {
                type: `leave_request_${data.status}`,
                recipients: [employee.email],
                data: {
                  employeeName: data.employeeName,
                  approverName: data.approverName,
                  rejectionReason: data.rejectionReason,
                  leaveRequestId: data.leaveRequestId,
                }
              }
            });
          }
        } catch (emailError) {
          console.warn('Failed to send email notification:', emailError);
        }

        return {
          data: { success: true },
          error: null,
          success: true,
          message: "Leave status notification sent successfully"
        };
      } catch (error: any) {
        return {
          data: null,
          error: error.message,
          success: false,
          message: "Failed to send leave status notification"
        };
      }
    },

    // Get user notifications
    getUserNotifications: async (userId: string, limit = 50): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        () => supabase
          .from('notifications')
          .select('*')
          .eq('recipient_id', userId)
          .order('created_at', { ascending: false })
          .limit(limit),
        null, // Don't show success toast for fetching
        "Failed to load notifications"
      );
    },

    // Mark notification as read
    markAsRead: async (notificationId: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase
          .from('notifications')
          .update({ is_read: true })
          .eq('id', notificationId),
        null, // Don't show success toast
        "Failed to mark notification as read"
      );
    },

    // Mark all notifications as read
    markAllAsRead: async (userId: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase
          .from('notifications')
          .update({ is_read: true })
          .eq('recipient_id', userId)
          .eq('is_read', false),
        "All notifications marked as read",
        "Failed to mark notifications as read"
      );
    },
  };

  leave = {
    // Submit leave request
    submitRequest: async (data: {
      leaveType: string;
      startDate: string;
      endDate: string;
      reason: string;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase.from('leave_requests').insert([{
          leave_type: data.leaveType,
          start_date: data.startDate,
          end_date: data.endDate,
          reason: data.reason,
          status: 'pending',
        }]).select().single(),
        "Leave request submitted successfully",
        "Failed to submit leave request"
      );
    },

    // Get user's leave requests
    getUserRequests: async (userId: string): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_requests')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false }),
        null,
        "Failed to load leave requests"
      );
    },

    // Get all leave requests (for managers)
    getAllRequests: async (): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_requests')
          .select(`
            *,
            profiles:user_id (
              full_name,
              email,
              department:department_id (name)
            )
          `)
          .order('created_at', { ascending: false }),
        null,
        "Failed to load leave requests"
      );
    },

    // Approve leave request
    approveRequest: async (requestId: string, approverId: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_requests')
          .update({
            status: 'approved',
            approved_by: approverId,
            approved_at: new Date().toISOString(),
          })
          .eq('id', requestId)
          .select()
          .single(),
        "Leave request approved successfully",
        "Failed to approve leave request"
      );
    },

    // Reject leave request
    rejectRequest: async (requestId: string, approverId: string, rejectionReason: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_requests')
          .update({
            status: 'rejected',
            approved_by: approverId,
            approved_at: new Date().toISOString(),
            rejection_reason: rejectionReason,
          })
          .eq('id', requestId)
          .select()
          .single(),
        "Leave request rejected",
        "Failed to reject leave request"
      );
    },

    // Get user's leave balances
    getUserBalances: async (userId: string): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_balances')
          .select('*')
          .eq('user_id', userId)
          .eq('year', new Date().getFullYear()),
        null,
        "Failed to load leave balances"
      );
    },

    // Update leave balance
    updateBalance: async (userId: string, leaveType: string, data: {
      totalDays?: number;
      usedDays?: number;
    }): Promise<APIResponse<any>> => {
      const currentYear = new Date().getFullYear();
      
      return this.executeWithNotification(
        () => supabase
          .from('leave_balances')
          .upsert([{
            user_id: userId,
            leave_type: leaveType,
            year: currentYear,
            ...data,
          }], {
            onConflict: 'user_id,leave_type,year'
          }),
        "Leave balance updated successfully",
        "Failed to update leave balance"
      );
    },
  };

  // API Key Management endpoints
  apiKeys = {
    // Get all API keys
    getAllKeys: async (): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('api_keys')
            .select('*')
            .order('created_at', { ascending: false });
          
          if (error) throw error;
          return data;
        },
        'GET',
        'API keys',
        false
      );
    },

    // Get API key statistics
    getStatistics: async (): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .rpc('get_api_key_statistics');
          
          if (error) throw error;
          return data[0] || { total_keys: 0, active_keys: 0, expired_keys: 0, total_usage: 0, most_used_service: null };
        },
        'GET',
        'API key statistics',
        false
      );
    },

    // Create new API key
    createKey: async (keyData: any): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          // Encrypt the API key before storing
          const encryptedKey = await supabase
            .rpc('encrypt_api_key', { api_key: keyData.api_key });
          
          if (encryptedKey.error) throw encryptedKey.error;

          const { data, error } = await supabase
            .from('api_keys')
            .insert([{
              name: keyData.name,
              service_provider: keyData.service_provider,
              api_key_encrypted: encryptedKey.data,
              description: keyData.description,
              expires_at: keyData.expires_at,
              created_by: (await supabase.auth.getUser()).data.user?.id
            }])
            .select()
            .single();
          
          if (error) throw error;
          return data;
        },
        'POST',
        'API key',
        false
      );
    },

    // Update API key
    updateKey: async (id: string, keyData: any): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const updateData: any = {
            name: keyData.name,
            service_provider: keyData.service_provider,
            description: keyData.description,
            expires_at: keyData.expires_at,
            status: keyData.status
          };

          // Only encrypt and update API key if it's being changed
          if (keyData.api_key && keyData.api_key.trim() !== '') {
            const encryptedKey = await supabase
              .rpc('encrypt_api_key', { api_key: keyData.api_key });
            
            if (encryptedKey.error) throw encryptedKey.error;
            updateData.api_key_encrypted = encryptedKey.data;
          }

          const { data, error } = await supabase
            .from('api_keys')
            .update(updateData)
            .eq('id', id)
            .select()
            .single();
          
          if (error) throw error;
          return data;
        },
        'PATCH',
        'API key',
        false
      );
    },

    // Delete API key
    deleteKey: async (id: string): Promise<APIResponse<void>> => {
      return this.executeWithNotification(
        async () => {
          const { error } = await supabase
            .from('api_keys')
            .delete()
            .eq('id', id);
          
          if (error) throw error;
          return undefined;
        },
        'DELETE',
        'API key',
        false
      );
    },

    // Get API key usage logs
    getUsageLogs: async (apiKeyId: string): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('api_key_usage_logs')
            .select('*')
            .eq('api_key_id', apiKeyId)
            .order('used_at', { ascending: false })
            .limit(100);
          
          if (error) throw error;
          return data;
        },
        'GET',
        'API key usage logs',
        false
      );
    },

    // Log API key usage
    logUsage: async (apiKeyId: string, usageData: any): Promise<APIResponse<void>> => {
      return this.executeWithNotification(
        async () => {
          const { error } = await supabase
            .from('api_key_usage_logs')
            .insert([{
              api_key_id: apiKeyId,
              endpoint: usageData.endpoint,
              method: usageData.method,
              status_code: usageData.status_code,
              response_time_ms: usageData.response_time_ms,
              error_message: usageData.error_message
            }]);
          
          if (error) throw error;

          // Update usage count
          // Get current usage count first, then increment
          const { data: currentKey } = await supabase
            .from('api_keys')
            .select('usage_count')
            .eq('id', apiKeyId)
            .single();

          const { error: updateError } = await supabase
            .from('api_keys')
            .update({
              usage_count: (currentKey?.usage_count || 0) + 1,
              last_used_at: new Date().toISOString()
            })
            .eq('id', apiKeyId);
          
          if (updateError) throw updateError;
          
          return undefined;
        },
        'POST',
        'API key usage',
        false
      );
    },

    // Get API key by service provider
    getKeyByProvider: async (serviceProvider: string): Promise<APIResponse<any | null>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('api_keys')
            .select('*')
            .eq('service_provider', serviceProvider)
            .eq('status', 'active')
            .order('created_at', { ascending: false })
            .limit(1);
          
          if (error) throw error;
          return data[0] || null;
        },
        'GET',
        'API key by provider',
        false
      );
    },

    // Test API key connectivity
    testKey: async (id: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const { data: apiKey, error } = await supabase
            .from('api_keys')
            .select('*')
            .eq('id', id)
            .single();
          
          if (error) throw error;
          
          // For now, just return success (in production, you'd test the actual API)
          return { success: true, message: 'API key is valid and active' };
        },
        'GET',
        'API key test',
        false
      );
    }
  };

  // Battery Management endpoints
  battery = {
    // Get all battery inventory
    getInventory: async (): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('battery_inventory')
            .select('*')
            .order('battery_id');
          
          if (error) throw error;
          return data;
        },
        'GET',
        'battery inventory',
        false
      );
    },

    // Get battery by ID
    getById: async (id: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('battery_inventory')
            .select('*')
            .eq('id', id)
            .single();
          
          if (error) throw error;
          return data;
        },
        'GET',
        'battery',
        false
      );
    },

    // Get battery statistics
    getStatistics: async (): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .rpc('get_battery_statistics');
          
          if (error) throw error;
          return data[0] || {
            total_batteries: 0,
            operational_batteries: 0,
            maintenance_batteries: 0,
            critical_batteries: 0,
            total_capacity_kwh: 0,
            average_charge_level: 0,
            pending_reports: 0,
            maintenance_due: 0
          };
        },
        'GET',
        'battery statistics',
        false
      );
    },

    // Submit battery report
    submitReport: async (reportData: {
      battery_id: string;
      charge_level: number;
      voltage_reading?: number;
      temperature?: number;
      status: string;
      maintenance_notes: string;
      issues_found?: string;
      corrective_actions?: string;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const user = await supabase.auth.getUser();
          if (!user.data.user) throw new Error('User not authenticated');

          const { data, error } = await supabase
            .from('battery_reports')
            .insert([{
              ...reportData,
              reported_by: user.data.user.id,
              report_date: new Date().toISOString().split('T')[0],
              approval_status: 'pending'
            }])
            .select()
            .single();
          
          if (error) throw error;
          return data;
        },
        'POST',
        'battery report'
      );
    },

    // Get battery reports (filtered by user for staff, all for managers/admins)
    getReports: async (filters?: {
      userId?: string;
      batteryId?: string;
      status?: string;
      limit?: number;
    }): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
          let query = supabase
            .from('battery_reports')
            .select(`
              *,
              battery_inventory!inner(battery_id, battery_model, location),
              reported_by_profile:profiles!reported_by(full_name, email)
            `);

          if (filters?.userId) {
            query = query.eq('reported_by', filters.userId);
          }
          if (filters?.batteryId) {
            query = query.eq('battery_id', filters.batteryId);
          }
          if (filters?.status) {
            query = query.eq('approval_status', filters.status);
          }

          query = query.order('created_at', { ascending: false });
          
          if (filters?.limit) {
            query = query.limit(filters.limit);
          }

          const { data, error } = await query;
          
          if (error) throw error;
          return data;
        },
        'GET',
        'battery reports',
        false
      );
    },

    // Approve battery report (managers/admins only)
    approveReport: async (reportId: string, approvalNotes?: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const user = await supabase.auth.getUser();
          if (!user.data.user) throw new Error('User not authenticated');

          const { data, error } = await supabase
            .from('battery_reports')
            .update({
              approval_status: 'approved',
              approved_by: user.data.user.id,
              approval_date: new Date().toISOString(),
              approval_notes: approvalNotes
            })
            .eq('id', reportId)
            .select()
            .single();
          
          if (error) throw error;
          return data;
        },
        'PATCH',
        'battery report approval'
      );
    },

    // Reject battery report (managers/admins only)
    rejectReport: async (reportId: string, rejectionNotes: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const user = await supabase.auth.getUser();
          if (!user.data.user) throw new Error('User not authenticated');

          const { data, error } = await supabase
            .from('battery_reports')
            .update({
              approval_status: 'rejected',
              approved_by: user.data.user.id,
              approval_date: new Date().toISOString(),
              approval_notes: rejectionNotes
            })
            .eq('id', reportId)
            .select()
            .single();
          
          if (error) throw error;
          return data;
        },
        'PATCH',
        'battery report rejection'
      );
    },

    // Add new battery to inventory
    addToInventory: async (batteryData: {
      battery_id: string;
      battery_model: string;
      manufacturer: string;
      capacity_kwh: number;
      voltage: number;
      installation_date: string;
      location: string;
      status?: string;
      warranty_expiry?: string;
      purchase_cost?: number;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('battery_inventory')
            .insert([{
              ...batteryData,
              status: batteryData.status || 'operational'
            }])
            .select()
            .single();
          
          if (error) throw error;
          return data;
        },
        'POST',
        'battery inventory'
      );
    },

    // Update battery in inventory
    updateInventory: async (id: string, batteryData: any): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('battery_inventory')
            .update(batteryData)
            .eq('id', id)
            .select()
            .single();
          
          if (error) throw error;
          return data;
        },
        'PATCH',
        'battery inventory'
      );
    },

    // Delete battery from inventory
    deleteFromInventory: async (id: string): Promise<APIResponse<void>> => {
      return this.executeWithNotification(
        async () => {
          const { error } = await supabase
            .from('battery_inventory')
            .delete()
            .eq('id', id);
          
          if (error) throw error;
          return undefined;
        },
        'DELETE',
        'battery inventory'
      );
    },

    // Get maintenance schedule
    getMaintenanceSchedule: async (batteryId?: string): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
          let query = supabase
            .from('battery_maintenance_schedule')
            .select(`
              *,
              battery_inventory!inner(battery_id, battery_model, location),
              assigned_to_profile:profiles!assigned_to(full_name, email)
            `);

          if (batteryId) {
            query = query.eq('battery_id', batteryId);
          }

          query = query.order('scheduled_date', { ascending: true });

          const { data, error } = await query;
          
          if (error) throw error;
          return data;
        },
        'GET',
        'maintenance schedule',
        false
      );
    },

    // Schedule maintenance
    scheduleMaintenance: async (maintenanceData: {
      battery_id: string;
      maintenance_type: string;
      scheduled_date: string;
      assigned_to?: string;
      notes?: string;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('battery_maintenance_schedule')
            .insert([{
              ...maintenanceData,
              status: 'scheduled'
            }])
            .select()
            .single();
          
          if (error) throw error;
          return data;
        },
        'POST',
        'maintenance schedule'
      );
    },

    // Record performance metrics
    recordPerformance: async (metricsData: {
      battery_id: string;
      charge_cycles?: number;
      efficiency_percentage?: number;
      capacity_degradation?: number;
      average_temperature?: number;
      power_output_kw?: number;
      energy_stored_kwh?: number;
      energy_discharged_kwh?: number;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const user = await supabase.auth.getUser();
          
          const { data, error } = await supabase
            .from('battery_performance_metrics')
            .insert([{
              ...metricsData,
              metric_date: new Date().toISOString().split('T')[0],
              recorded_by: user.data.user?.id
            }])
            .select()
            .single();
          
          if (error) throw error;
          return data;
        },
        'POST',
        'performance metrics'
      );
    },

    // Get performance metrics
    getPerformanceMetrics: async (batteryId: string, days?: number): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
          let query = supabase
            .from('battery_performance_metrics')
            .select('*')
            .eq('battery_id', batteryId);

          if (days) {
            const fromDate = new Date();
            fromDate.setDate(fromDate.getDate() - days);
            query = query.gte('metric_date', fromDate.toISOString().split('T')[0]);
          }

          query = query.order('metric_date', { ascending: false });

          const { data, error } = await query;
          
          if (error) throw error;
          return data;
        },
        'GET',
        'performance metrics',
        false
      );
    }
  };

  // Route-based API call handler
  async handleRoute(path: string, method: string = 'GET', data?: any): Promise<APIResponse<any>> {
    const endpoint = `${method} ${path}`;
    
    // Handle API key endpoints
    if (path.startsWith('/api/admin/api-keys')) {
      if (method === 'GET' && path === '/api/admin/api-keys') {
        return this.apiKeys.getAllKeys();
      } else if (method === 'GET' && path === '/api/admin/api-keys/statistics') {
        return this.apiKeys.getStatistics();
      } else if (method === 'POST' && path === '/api/admin/api-keys') {
        return this.apiKeys.createKey(data);
      } else if (method === 'PATCH' && path.match(/^\/api\/admin\/api-keys\/[^/]+$/)) {
        const id = path.split('/').pop()!;
        return this.apiKeys.updateKey(id, data);
      } else if (method === 'DELETE' && path.match(/^\/api\/admin\/api-keys\/[^/]+$/)) {
        const id = path.split('/').pop()!;
        return this.apiKeys.deleteKey(id);
      } else if (method === 'GET' && path.match(/^\/api\/admin\/api-keys\/[^/]+\/usage$/)) {
        const id = path.split('/')[4];
        return this.apiKeys.getUsageLogs(id);
      } else if (method === 'POST' && path.match(/^\/api\/admin\/api-keys\/[^/]+\/usage$/)) {
        const id = path.split('/')[4];
        return this.apiKeys.logUsage(id, data);
      } else if (method === 'GET' && path.match(/^\/api\/admin\/api-keys\/provider\/[^/]+$/)) {
        const provider = path.split('/').pop()!;
        return this.apiKeys.getKeyByProvider(provider);
      } else if (method === 'POST' && path.match(/^\/api\/admin\/api-keys\/[^/]+\/test$/)) {
        const id = path.split('/')[4];
        return this.apiKeys.testKey(id);
      }
    }
    
    if (this.endpoints[endpoint]) {
      return this.endpoints[endpoint](data);
    }
    
    // Handle parameterized routes
    for (const [pattern, handler] of Object.entries(this.endpoints)) {
      if (this.matchRoute(pattern, endpoint)) {
        return handler(data);
      }
    }
    
    throw new Error(`No handler found for ${endpoint}`);
  }
}

// Create and export the enhanced API instance
export const api = new APIService();

// Export legacy API for backward compatibility
export {
  authAPI as auth,
  usersAPI as users,
  departmentsAPI as departments,
  projectsAPI as projects,
  tasksAPI as tasks,
  reportsAPI as reports,
  telecomSitesAPI as telecomSites,
  memosAPI as memos,
  financialAPI as financial,
  fleetAPI as fleet,
  assetsAPI as assets,
  constructionAPI as construction,
  timeTrackingAPI as timeTracking
} from './api-legacy';

// API Key Management TypeScript interfaces
export interface APIKey {
  id: string;
  name: string;
  service_provider: string;
  api_key_encrypted: string;
  description: string;
  status: 'active' | 'inactive' | 'expired';
  usage_count: number;
  last_used_at: string | null;
  expires_at: string | null;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface APIKeyStatistics {
  total_keys: number;
  active_keys: number;
  expired_keys: number;
  total_usage: number;
  most_used_service: string | null;
}

export interface APIKeyUsageLog {
  id: string;
  api_key_id: string;
  endpoint: string;
  method: string;
  status_code: number;
  response_time_ms: number;
  error_message: string | null;
  used_at: string;
}

// Update the main API object to include API key management endpoints
api.admin = {
  ...api.admin,
  apiKeys: {
    get: api.apiKeys.getAllKeys,
    statistics: api.apiKeys.getStatistics,
    create: api.apiKeys.createKey,
    update: api.apiKeys.updateKey,
    delete: api.apiKeys.deleteKey,
    getUsage: api.apiKeys.getUsageLogs,
    logUsage: api.apiKeys.logUsage,
    getByProvider: api.apiKeys.getKeyByProvider,
    test: api.apiKeys.testKey
  }
};

// Update API endpoints map
api.endpoints = {
  ...api.endpoints,
  // API Key Management
  'GET /api/admin/api-keys': api.apiKeys.getAllKeys,
  'GET /api/admin/api-keys/statistics': api.apiKeys.getStatistics,
  'POST /api/admin/api-keys': api.apiKeys.createKey,
  'PATCH /api/admin/api-keys/:id': api.apiKeys.updateKey,
  'DELETE /api/admin/api-keys/:id': api.apiKeys.deleteKey,
  'GET /api/admin/api-keys/:id/usage': api.apiKeys.getUsageLogs,
  'POST /api/admin/api-keys/:id/usage': api.apiKeys.logUsage,
  'GET /api/admin/api-keys/provider/:provider': api.apiKeys.getKeyByProvider,
  'POST /api/admin/api-keys/:id/test': api.apiKeys.testKey
};