import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { 
  Trash2, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Database,
  HardDrive,
  Wifi,
  Settings,
  Zap
} from 'lucide-react';
import { CacheBuster } from '@/utils/cache-buster';

export const CacheClearingTool: React.FC = () => {
  const [isClearing, setIsClearing] = useState(false);
  const [cacheInfo, setCacheInfo] = useState<{
    currentVersion: string;
    storedVersion: string | null;
    isRunningFromCache: boolean;
    serviceWorkerCount: number;
    cacheCount: number;
  } | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadCacheInfo();
  }, []);

  const loadCacheInfo = async () => {
    try {
      const currentVersion = CacheBuster.getCurrentVersion();
      const storedVersion = CacheBuster.getStoredVersion();
      const isRunningFromCache = CacheBuster.isRunningFromCache();

      // Get service worker count
      let serviceWorkerCount = 0;
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        serviceWorkerCount = registrations.length;
      }

      // Get cache count
      let cacheCount = 0;
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        cacheCount = cacheNames.length;
      }

      setCacheInfo({
        currentVersion,
        storedVersion,
        isRunningFromCache,
        serviceWorkerCount,
        cacheCount
      });
    } catch (error) {
      console.error('Error loading cache info:', error);
    }
  };

  const handleManualCacheClear = async () => {
    setIsClearing(true);
    
    try {
      toast({
        title: "Cache Clearing Started",
        description: "Clearing all caches and PWA data...",
      });

      await CacheBuster.manualCacheClear();
      
      toast({
        title: "Cache Cleared Successfully",
        description: "All caches have been cleared. The page will reload shortly.",
      });

      // Reload cache info after clearing
      setTimeout(() => {
        loadCacheInfo();
      }, 1000);

    } catch (error) {
      toast({
        title: "Cache Clear Failed",
        description: `Error: ${error}`,
        variant: "destructive"
      });
    } finally {
      setIsClearing(false);
    }
  };

  const handleForceReload = () => {
    toast({
      title: "Force Reload",
      description: "Forcing hard reload from server...",
    });
    
    CacheBuster.forceHardReload();
  };

  const getVersionStatus = () => {
    if (!cacheInfo) return 'unknown';
    
    if (cacheInfo.storedVersion === cacheInfo.currentVersion) {
      return 'current';
    } else if (cacheInfo.storedVersion === null) {
      return 'new';
    } else {
      return 'outdated';
    }
  };

  const getVersionBadge = () => {
    const status = getVersionStatus();
    
    switch (status) {
      case 'current':
        return <Badge variant="default" className="bg-green-500">Up to Date</Badge>;
      case 'new':
        return <Badge variant="secondary">New Installation</Badge>;
      case 'outdated':
        return <Badge variant="destructive">Outdated</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-500" />
            Cache Clearing & PWA Cleanup Tool
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            This tool helps clear old PWA implementations, service workers, and cached data 
            that might be causing issues with ai.ctnigeria.com. Use this when the site is 
            stuck with old implementations or showing PWA/SW errors.
          </p>

          <div className="flex gap-2">
            <Button
              onClick={handleManualCacheClear}
              disabled={isClearing}
              variant="destructive"
              className="flex-1"
            >
              {isClearing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Clearing Cache...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All Caches
                </>
              )}
            </Button>
            
            <Button
              onClick={handleForceReload}
              variant="outline"
              disabled={isClearing}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Force Reload
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Cache Information */}
      {cacheInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-blue-500" />
              Cache Status Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              {/* Version Info */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">App Version:</span>
                  {getVersionBadge()}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Current:</span>
                  <code className="text-xs bg-muted px-2 py-1 rounded">
                    {cacheInfo.currentVersion}
                  </code>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Stored:</span>
                  <code className="text-xs bg-muted px-2 py-1 rounded">
                    {cacheInfo.storedVersion || 'None'}
                  </code>
                </div>
              </div>

              {/* Cache Counts */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Service Workers:</span>
                  <Badge variant={cacheInfo.serviceWorkerCount > 0 ? "destructive" : "default"}>
                    {cacheInfo.serviceWorkerCount}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Browser Caches:</span>
                  <Badge variant={cacheInfo.cacheCount > 0 ? "secondary" : "default"}>
                    {cacheInfo.cacheCount}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Running from Cache:</span>
                  <Badge variant={cacheInfo.isRunningFromCache ? "destructive" : "default"}>
                    {cacheInfo.isRunningFromCache ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Warnings and Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {cacheInfo?.serviceWorkerCount > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Service Workers Detected:</strong> {cacheInfo.serviceWorkerCount} service worker(s) 
                  are still registered. These may be causing PWA/SW errors. Clear caches to remove them.
                </AlertDescription>
              </Alert>
            )}

            {getVersionStatus() === 'outdated' && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Outdated Version:</strong> Your cached version is outdated. 
                  Clear caches to get the latest implementation.
                </AlertDescription>
              </Alert>
            )}

            {cacheInfo?.cacheCount > 5 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Many Caches:</strong> {cacheInfo.cacheCount} browser caches detected. 
                  Consider clearing to free up space and ensure fresh content.
                </AlertDescription>
              </Alert>
            )}

            {cacheInfo?.isRunningFromCache && (
              <Alert>
                <Wifi className="h-4 w-4" />
                <AlertDescription>
                  <strong>PWA Mode:</strong> The app appears to be running in PWA mode. 
                  This might cause issues with updates. Use force reload to bypass cache.
                </AlertDescription>
              </Alert>
            )}

            {getVersionStatus() === 'current' && cacheInfo?.serviceWorkerCount === 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>All Good:</strong> Your app version is current and no problematic 
                  service workers are detected. The cache clearing system is working properly.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Manual Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-gray-500" />
            Manual Cache Clearing Instructions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">If automatic clearing doesn't work:</h4>
              <ol className="list-decimal list-inside space-y-1 text-muted-foreground">
                <li>Open browser Developer Tools (F12)</li>
                <li>Go to Application/Storage tab</li>
                <li>Clear all storage data (Local Storage, Session Storage, IndexedDB)</li>
                <li>Go to Service Workers section and unregister all workers</li>
                <li>Go to Cache Storage and delete all caches</li>
                <li>Perform a hard refresh (Ctrl+Shift+R or Cmd+Shift+R)</li>
              </ol>
            </div>

            <div>
              <h4 className="font-medium mb-2">For persistent issues:</h4>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>Try opening the site in an incognito/private window</li>
                <li>Clear browser data completely for ai.ctnigeria.com</li>
                <li>Disable browser extensions temporarily</li>
                <li>Try a different browser to confirm the issue</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
