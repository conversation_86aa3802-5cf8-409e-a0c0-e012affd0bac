
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { Battery, Plus, AlertCircle, CheckCircle, Eye } from "lucide-react";
import { format } from "date-fns";

export const BatteryReports = () => {
  const { userProfile } = useAuth();
  const [showNewReportDialog, setShowNewReportDialog] = useState(false);

  const { data: batteryReports, isLoading } = useQuery({
    queryKey: ['battery-reports', userProfile?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('battery_reports')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'default';
      case 'good': return 'secondary';
      case 'fair': return 'outline';
      case 'poor': return 'destructive';
      default: return 'outline';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading battery reports...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Battery Reports</h2>
          <p className="text-muted-foreground">Monitor battery system health and performance</p>
        </div>
        <Dialog open={showNewReportDialog} onOpenChange={setShowNewReportDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Battery Report
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New Battery Report</DialogTitle>
            </DialogHeader>
            <div className="p-4 text-center">
              <p className="text-muted-foreground mb-4">
                Battery report creation functionality will be implemented here.
              </p>
              <Button onClick={() => setShowNewReportDialog(false)}>
                Close
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {batteryReports && batteryReports.length > 0 ? (
          batteryReports.map((report) => (
            <Card key={report.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Battery className="h-4 w-4" />
                    {report.site_name || 'Battery System'}
                  </CardTitle>
                  <Badge variant={getHealthStatusColor(report.health_status)}>
                    {report.health_status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Voltage:</span>
                      <div className="font-medium">{report.battery_voltage}V</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Capacity:</span>
                      <div className="font-medium">{report.current_capacity}%</div>
                    </div>
                  </div>
                  
                  {report.maintenance_required && (
                    <div className="flex items-center gap-2 text-amber-600">
                      <AlertCircle className="h-4 w-4" />
                      <span className="text-sm">Maintenance Required</span>
                    </div>
                  )}
                  
                  {!report.maintenance_required && (
                    <div className="flex items-center gap-2 text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-sm">Operating Normally</span>
                    </div>
                  )}
                  
                  <div className="text-xs text-muted-foreground">
                    Report Date: {format(new Date(report.report_date), 'MMM dd, yyyy')}
                  </div>
                </div>
                
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="w-full mt-4" variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      View Full Report
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Battery Report Details</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Site Name</label>
                          <p className="text-sm">{report.site_name || 'Battery System'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Health Status</label>
                          <p className="text-sm">
                            <Badge variant={getHealthStatusColor(report.health_status)}>
                              {report.health_status}
                            </Badge>
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Voltage</label>
                          <p className="text-sm">{report.battery_voltage}V</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Capacity</label>
                          <p className="text-sm">{report.current_capacity}%</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Report Date</label>
                          <p className="text-sm">{format(new Date(report.report_date), 'MMM dd, yyyy')}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Maintenance Required</label>
                          <p className="text-sm">{report.maintenance_required ? 'Yes' : 'No'}</p>
                        </div>
                      </div>
                      {report.maintenance_notes && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Maintenance Notes</label>
                          <p className="text-sm">{report.maintenance_notes}</p>
                        </div>
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="col-span-full text-center py-8 text-muted-foreground">
            <Battery className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No battery reports found</p>
          </div>
        )}
      </div>
    </div>
  );
};
