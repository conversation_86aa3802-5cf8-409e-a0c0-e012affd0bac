import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  X, 
  Download, 
  FileText, 
  Calendar, 
  User, 
  HardDrive, 
  Tag,
  ExternalLink
} from "lucide-react";
import { formatBytes } from "@/lib/utils";

interface DocumentType {
  id: string;
  title: string;
  file_name: string;
  file_type: string;
  file_size: number;
  category: string;
  tags: string[] | null;
  created_at: string;
  updated_at: string;
  file_path: string;
  uploaded_by: string;
  access_level: string;
  uploader_name?: string;
}

interface DocumentViewerProps {
  document: DocumentType;
  onClose: () => void;
}

export const DocumentViewer = ({ document, onClose }: DocumentViewerProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      // Create download link
      const link = window.document.createElement('a');
      link.href = `/api/documents/${document.id}/download`;
      link.download = document.file_name;
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const isPreviewable = (fileType: string) => {
    return fileType.includes('pdf') || 
           fileType.includes('image') || 
           fileType.includes('text');
  };

  const getPreviewUrl = () => {
    if (document.file_type.includes('pdf')) {
      return `/api/documents/${document.id}/preview`;
    }
    if (document.file_type.includes('image')) {
      return `/api/documents/${document.id}/content`;
    }
    return null;
  };

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {document.title}
        </CardTitle>
        <div className="flex items-center gap-2">
          <Button 
            onClick={handleDownload} 
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button onClick={onClose} variant="ghost" size="sm">
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Document Metadata */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            <HardDrive className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">File Size</p>
              <p className="text-xs text-muted-foreground">
                {formatBytes(document.file_size)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Created</p>
              <p className="text-xs text-muted-foreground">
                {new Date(document.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
          
          {document.uploader_name && (
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Uploaded by</p>
                <p className="text-xs text-muted-foreground">
                  {document.uploader_name}
                </p>
              </div>
            </div>
          )}
          
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Category</p>
              <Badge variant="outline" className="text-xs">
                {document.category}
              </Badge>
            </div>
          </div>
        </div>

        {/* Tags */}
        {document.tags && document.tags.length > 0 && (
          <div>
            <h3 className="text-sm font-medium mb-2">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {document.tags.map((tag, index) => (
                <Badge key={index} variant="secondary">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Preview Section */}
        <div className="border rounded-lg">
          <div className="p-4 border-b bg-muted/50">
            <h3 className="text-sm font-medium">Document Preview</h3>
          </div>
          
          <div className="p-4">
            {isPreviewable(document.file_type) ? (
              <div className="space-y-4">
                {document.file_type.includes('pdf') && (
                  <iframe
                    src={getPreviewUrl()}
                    className="w-full h-96 border rounded"
                    title={document.title}
                  />
                )}
                
                {document.file_type.includes('image') && (
                  <img
                    src={getPreviewUrl()}
                    alt={document.title}
                    className="max-w-full h-auto border rounded"
                  />
                )}
                
                {document.file_type.includes('text') && (
                  <div className="p-4 bg-muted rounded text-sm">
                    <p>Text file preview would be rendered here</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground mb-4">
                  Preview not available for this file type
                </p>
                <Button onClick={handleDownload} disabled={isLoading}>
                  <Download className="h-4 w-4 mr-2" />
                  Download to View
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* File Information */}
        <div className="space-y-2 text-sm text-muted-foreground">
          <p><strong>File Name:</strong> {document.file_name}</p>
          <p><strong>File Type:</strong> {document.file_type}</p>
          <p><strong>Access Level:</strong> {document.access_level}</p>
          <p><strong>Last Modified:</strong> {new Date(document.updated_at).toLocaleString()}</p>
        </div>
      </CardContent>
    </Card>
  );
};