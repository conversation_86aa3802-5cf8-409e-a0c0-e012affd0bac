import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Brain, 
  Cpu, 
  Zap, 
  Target, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Play,
  Pause,
  Square,
  RotateCcw,
  Settings,
  Eye,
  Network,
  Activity
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";

interface AgentTask {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  priority: 'low' | 'medium' | 'high' | 'critical';
  progress: number;
  steps: AgentStep[];
  dependencies: string[];
  estimatedTime: number;
  actualTime?: number;
  startTime?: Date;
  endTime?: Date;
  result?: any;
  error?: string;
}

interface AgentStep {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  action: string;
  parameters: any;
  result?: any;
  executionTime?: number;
}

interface AgentCapability {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  confidence: number;
  lastUsed?: Date;
  successRate: number;
}

export const AgenticAISystem: React.FC = () => {
  const [tasks, setTasks] = useState<AgentTask[]>([]);
  const [capabilities, setCapabilities] = useState<AgentCapability[]>([]);
  const [isSystemActive, setIsSystemActive] = useState(false);
  const [systemMetrics, setSystemMetrics] = useState({
    tasksCompleted: 0,
    tasksRunning: 0,
    tasksFailed: 0,
    averageExecutionTime: 0,
    successRate: 0
  });
  const [newTaskInput, setNewTaskInput] = useState('');
  const [selectedTask, setSelectedTask] = useState<AgentTask | null>(null);
  
  const { userProfile } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    initializeAgentSystem();
    const interval = setInterval(updateSystemMetrics, 2000);
    return () => clearInterval(interval);
  }, []);

  const initializeAgentSystem = () => {
    // Initialize agent capabilities
    const agentCapabilities: AgentCapability[] = [
      {
        id: 'data_analysis',
        name: 'Data Analysis',
        description: 'Analyze datasets and generate insights',
        enabled: true,
        confidence: 0.92,
        successRate: 0.88
      },
      {
        id: 'document_processing',
        name: 'Document Processing',
        description: 'Extract, analyze, and categorize documents',
        enabled: true,
        confidence: 0.89,
        successRate: 0.91
      },
      {
        id: 'task_automation',
        name: 'Task Automation',
        description: 'Automate repetitive business processes',
        enabled: true,
        confidence: 0.85,
        successRate: 0.83
      },
      {
        id: 'decision_making',
        name: 'Decision Making',
        description: 'Make intelligent decisions based on data',
        enabled: true,
        confidence: 0.78,
        successRate: 0.75
      },
      {
        id: 'system_monitoring',
        name: 'System Monitoring',
        description: 'Monitor system health and performance',
        enabled: true,
        confidence: 0.94,
        successRate: 0.96
      },
      {
        id: 'predictive_analysis',
        name: 'Predictive Analysis',
        description: 'Predict future trends and outcomes',
        enabled: true,
        confidence: 0.72,
        successRate: 0.68
      }
    ];

    setCapabilities(agentCapabilities);

    // Initialize sample tasks
    const sampleTasks: AgentTask[] = [
      {
        id: 'task-1',
        name: 'Weekly Report Generation',
        description: 'Generate comprehensive weekly performance report',
        status: 'completed',
        priority: 'medium',
        progress: 100,
        steps: [
          { id: 'step-1', name: 'Collect Data', status: 'completed', action: 'data_collection', parameters: {} },
          { id: 'step-2', name: 'Analyze Metrics', status: 'completed', action: 'analysis', parameters: {} },
          { id: 'step-3', name: 'Generate Report', status: 'completed', action: 'report_generation', parameters: {} }
        ],
        dependencies: [],
        estimatedTime: 300000,
        actualTime: 285000,
        startTime: new Date(Date.now() - 300000),
        endTime: new Date(Date.now() - 15000)
      },
      {
        id: 'task-2',
        name: 'Database Optimization',
        description: 'Optimize database queries and performance',
        status: 'running',
        priority: 'high',
        progress: 65,
        steps: [
          { id: 'step-1', name: 'Analyze Queries', status: 'completed', action: 'query_analysis', parameters: {} },
          { id: 'step-2', name: 'Identify Bottlenecks', status: 'running', action: 'bottleneck_detection', parameters: {} },
          { id: 'step-3', name: 'Apply Optimizations', status: 'pending', action: 'optimization', parameters: {} }
        ],
        dependencies: [],
        estimatedTime: 600000,
        startTime: new Date(Date.now() - 200000)
      }
    ];

    setTasks(sampleTasks);
  };

  const updateSystemMetrics = () => {
    const completed = tasks.filter(t => t.status === 'completed').length;
    const running = tasks.filter(t => t.status === 'running').length;
    const failed = tasks.filter(t => t.status === 'failed').length;
    
    const completedTasks = tasks.filter(t => t.status === 'completed' && t.actualTime);
    const avgTime = completedTasks.length > 0 
      ? completedTasks.reduce((sum, t) => sum + (t.actualTime || 0), 0) / completedTasks.length 
      : 0;
    
    const successRate = tasks.length > 0 ? completed / tasks.length : 0;

    setSystemMetrics({
      tasksCompleted: completed,
      tasksRunning: running,
      tasksFailed: failed,
      averageExecutionTime: avgTime,
      successRate
    });
  };

  const createAgentTask = async (instruction: string) => {
    try {
      // Use AI to break down the instruction into executable steps
      const { data, error } = await supabase.functions.invoke('ai-agent-intent', {
        body: {
          message: instruction,
          userId: userProfile?.id,
          userRole: userProfile?.role,
          context: {
            capabilities: capabilities.filter(c => c.enabled),
            systemMetrics
          }
        }
      });

      if (error) throw error;

      // Create new task based on AI analysis
      const newTask: AgentTask = {
        id: `task-${Date.now()}`,
        name: data.taskName || instruction.substring(0, 50),
        description: instruction,
        status: 'pending',
        priority: data.priority || 'medium',
        progress: 0,
        steps: data.steps || [
          {
            id: `step-${Date.now()}`,
            name: 'Execute Task',
            status: 'pending',
            action: 'general_execution',
            parameters: { instruction }
          }
        ],
        dependencies: data.dependencies || [],
        estimatedTime: data.estimatedTime || 60000
      };

      setTasks(prev => [newTask, ...prev]);
      setNewTaskInput('');

      toast({
        title: "🤖 Agent Task Created",
        description: `Task "${newTask.name}" added to execution queue`,
      });

      // Auto-start if system is active
      if (isSystemActive) {
        executeTask(newTask.id);
      }

    } catch (error) {
      console.error('Error creating agent task:', error);
      toast({
        title: "⚠️ Task Creation Failed",
        description: "Failed to create agent task",
        variant: "destructive",
      });
    }
  };

  const executeTask = async (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (!task || task.status === 'running') return;

    // Update task status
    setTasks(prev => prev.map(t => 
      t.id === taskId 
        ? { ...t, status: 'running', startTime: new Date(), progress: 0 }
        : t
    ));

    try {
      for (let i = 0; i < task.steps.length; i++) {
        const step = task.steps[i];
        
        // Update step status
        setTasks(prev => prev.map(t => 
          t.id === taskId 
            ? {
                ...t, 
                steps: t.steps.map((s, idx) => 
                  idx === i ? { ...s, status: 'running' } : s
                ),
                progress: (i / task.steps.length) * 100
              }
            : t
        ));

        // Execute step
        const { data, error } = await supabase.functions.invoke('ai-agent-executor', {
          body: {
            intent: step.action, // Use 'intent' instead of 'action' to match the function's expected parameter
            query: step.description || `Execute ${step.action}`,
            parameters: step.parameters,
            userId: userProfile?.id,
            userRole: userProfile?.role,
            context: {
              taskId,
              stepId: step.id,
              capabilities: capabilities.filter(c => c.enabled)
            }
          }
        });

        if (error) throw error;

        // Update step completion
        setTasks(prev => prev.map(t => 
          t.id === taskId 
            ? {
                ...t, 
                steps: t.steps.map((s, idx) => 
                  idx === i 
                    ? { ...s, status: 'completed', result: data, executionTime: Date.now() - (task.startTime?.getTime() || 0) }
                    : s
                )
              }
            : t
        ));

        // Simulate execution time
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Complete task
      setTasks(prev => prev.map(t => 
        t.id === taskId 
          ? { 
              ...t, 
              status: 'completed', 
              progress: 100, 
              endTime: new Date(),
              actualTime: Date.now() - (task.startTime?.getTime() || 0)
            }
          : t
      ));

      toast({
        title: "✅ Task Completed",
        description: `Agent successfully completed "${task.name}"`,
      });

    } catch (error) {
      console.error('Task execution error:', error);
      
      setTasks(prev => prev.map(t => 
        t.id === taskId 
          ? { 
              ...t, 
              status: 'failed', 
              error: error instanceof Error ? error.message : 'Unknown error',
              endTime: new Date()
            }
          : t
      ));

      toast({
        title: "❌ Task Failed",
        description: `Failed to execute "${task.name}"`,
        variant: "destructive",
      });
    }
  };

  const pauseTask = (taskId: string) => {
    setTasks(prev => prev.map(t => 
      t.id === taskId && t.status === 'running'
        ? { ...t, status: 'paused' }
        : t
    ));
  };

  const resumeTask = (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (task && task.status === 'paused') {
      executeTask(taskId);
    }
  };

  const cancelTask = (taskId: string) => {
    setTasks(prev => prev.filter(t => t.id !== taskId));
  };

  const toggleSystemActive = () => {
    setIsSystemActive(!isSystemActive);
    
    if (!isSystemActive) {
      // Start pending tasks
      const pendingTasks = tasks.filter(t => t.status === 'pending');
      pendingTasks.forEach(task => executeTask(task.id));
    }

    toast({
      title: isSystemActive ? "🛑 Agent System Paused" : "🚀 Agent System Activated",
      description: isSystemActive ? "Autonomous execution paused" : "Autonomous execution enabled",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400';
      case 'running': return 'text-blue-400';
      case 'failed': return 'text-red-400';
      case 'paused': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'border-red-500 text-red-400';
      case 'high': return 'border-orange-500 text-orange-400';
      case 'medium': return 'border-yellow-500 text-yellow-400';
      default: return 'border-green-500 text-green-400';
    }
  };

  return (
    <div className="space-y-6 text-green-400 font-mono">
      {/* System Control Header */}
      <Card className="bg-black border-green-500">
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-green-400">
            <div className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AGENTIC AI SYSTEM
              <Badge 
                variant="outline" 
                className={`${isSystemActive ? 'border-green-500 text-green-400' : 'border-red-500 text-red-400'}`}
              >
                {isSystemActive ? 'ACTIVE' : 'STANDBY'}
              </Badge>
            </div>
            <Button
              onClick={toggleSystemActive}
              className={`${
                isSystemActive 
                  ? 'bg-red-500/20 border-red-500 text-red-400 hover:bg-red-500/30' 
                  : 'bg-green-500/20 border-green-500 text-green-400 hover:bg-green-500/30'
              }`}
            >
              {isSystemActive ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              {isSystemActive ? 'PAUSE SYSTEM' : 'ACTIVATE SYSTEM'}
            </Button>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* System Metrics */}
      <div className="grid grid-cols-5 gap-4">
        {[
          { label: 'COMPLETED', value: systemMetrics.tasksCompleted, icon: CheckCircle, color: 'text-green-400' },
          { label: 'RUNNING', value: systemMetrics.tasksRunning, icon: Activity, color: 'text-blue-400' },
          { label: 'FAILED', value: systemMetrics.tasksFailed, icon: AlertCircle, color: 'text-red-400' },
          { label: 'AVG TIME', value: `${(systemMetrics.averageExecutionTime / 1000).toFixed(1)}s`, icon: Clock, color: 'text-yellow-400' },
          { label: 'SUCCESS', value: `${(systemMetrics.successRate * 100).toFixed(1)}%`, icon: Target, color: 'text-green-400' }
        ].map((metric, index) => (
          <Card key={index} className="bg-black border-green-500">
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <metric.icon className={`h-4 w-4 ${metric.color}`} />
                <span className="text-xs text-green-400">{metric.label}</span>
              </div>
              <div className={`text-lg font-bold ${metric.color} mt-2`}>
                {metric.value}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Task Creation */}
        <div className="lg:col-span-2">
          <Card className="bg-black border-green-500">
            <CardHeader>
              <CardTitle className="text-green-400 text-sm">CREATE AGENT TASK</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Input
                  value={newTaskInput}
                  onChange={(e) => setNewTaskInput(e.target.value)}
                  placeholder="Describe what you want the AI agent to do..."
                  className="bg-transparent border-green-500 text-green-400 placeholder-green-600"
                  onKeyPress={(e) => e.key === 'Enter' && newTaskInput.trim() && createAgentTask(newTaskInput)}
                />
                <Button
                  onClick={() => createAgentTask(newTaskInput)}
                  disabled={!newTaskInput.trim()}
                  className="bg-green-500/20 border border-green-500 text-green-400 hover:bg-green-500/30"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  CREATE
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Task Queue */}
          <Card className="bg-black border-green-500 mt-4">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-green-400 text-sm">
                TASK QUEUE
                <Badge variant="outline" className="border-green-500 text-green-400">
                  {tasks.length} TASKS
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-3">
                  {tasks.map((task) => (
                    <div
                      key={task.id}
                      className={`p-3 border rounded cursor-pointer transition-colors ${
                        selectedTask?.id === task.id
                          ? 'border-green-400 bg-green-500/10'
                          : 'border-green-500/30 hover:border-green-400'
                      }`}
                      onClick={() => setSelectedTask(task)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-semibold text-green-400 text-sm">{task.name}</span>
                        <div className="flex gap-2">
                          <Badge 
                            variant="outline" 
                            className={getPriorityColor(task.priority)}
                          >
                            {task.priority.toUpperCase()}
                          </Badge>
                          <Badge 
                            variant="outline" 
                            className={`border-green-500 ${getStatusColor(task.status)}`}
                          >
                            {task.status.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="text-xs text-gray-400 mb-2">{task.description}</div>
                      
                      {task.status === 'running' && (
                        <Progress value={task.progress} className="h-1 mb-2 bg-gray-800" />
                      )}
                      
                      <div className="flex items-center justify-between">
                        <div className="text-xs text-green-300">
                          {task.steps.length} steps • Est: {(task.estimatedTime / 1000).toFixed(0)}s
                        </div>
                        <div className="flex gap-1">
                          {task.status === 'pending' && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                executeTask(task.id);
                              }}
                              className="h-6 w-6 p-0 text-green-400 hover:text-green-300"
                            >
                              <Play className="h-3 w-3" />
                            </Button>
                          )}
                          {task.status === 'running' && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                pauseTask(task.id);
                              }}
                              className="h-6 w-6 p-0 text-yellow-400 hover:text-yellow-300"
                            >
                              <Pause className="h-3 w-3" />
                            </Button>
                          )}
                          {task.status === 'paused' && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                resumeTask(task.id);
                              }}
                              className="h-6 w-6 p-0 text-blue-400 hover:text-blue-300"
                            >
                              <Play className="h-3 w-3" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              cancelTask(task.id);
                            }}
                            className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
                          >
                            <Square className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Agent Capabilities */}
        <div>
          <Card className="bg-black border-green-500">
            <CardHeader>
              <CardTitle className="text-green-400 text-sm">AGENT CAPABILITIES</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-3">
                  {capabilities.map((capability) => (
                    <div key={capability.id} className="p-3 border border-green-500/30 rounded">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-semibold text-green-400 text-sm">{capability.name}</span>
                        <Badge 
                          variant="outline" 
                          className={`${capability.enabled ? 'border-green-500 text-green-400' : 'border-gray-500 text-gray-400'}`}
                        >
                          {capability.enabled ? 'ENABLED' : 'DISABLED'}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-400 mb-2">{capability.description}</div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span className="text-green-300">Confidence:</span>
                          <span className="text-green-400">{(capability.confidence * 100).toFixed(0)}%</span>
                        </div>
                        <Progress value={capability.confidence * 100} className="h-1 bg-gray-800" />
                        <div className="flex justify-between text-xs">
                          <span className="text-green-300">Success Rate:</span>
                          <span className="text-green-400">{(capability.successRate * 100).toFixed(0)}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Task Details */}
          {selectedTask && (
            <Card className="bg-black border-green-500 mt-4">
              <CardHeader>
                <CardTitle className="text-green-400 text-sm">TASK DETAILS</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="text-xs text-green-300 mb-1">NAME:</div>
                    <div className="text-sm text-green-400">{selectedTask.name}</div>
                  </div>
                  <div>
                    <div className="text-xs text-green-300 mb-1">STATUS:</div>
                    <Badge 
                      variant="outline" 
                      className={`border-green-500 ${getStatusColor(selectedTask.status)}`}
                    >
                      {selectedTask.status.toUpperCase()}
                    </Badge>
                  </div>
                  <div>
                    <div className="text-xs text-green-300 mb-1">STEPS:</div>
                    <div className="space-y-1">
                      {selectedTask.steps.map((step, index) => (
                        <div key={step.id} className="flex items-center gap-2 text-xs">
                          <div className={`w-2 h-2 rounded-full ${
                            step.status === 'completed' ? 'bg-green-400' :
                            step.status === 'running' ? 'bg-blue-400' :
                            step.status === 'failed' ? 'bg-red-400' :
                            'bg-gray-400'
                          }`}></div>
                          <span className={getStatusColor(step.status)}>{step.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  {selectedTask.error && (
                    <div>
                      <div className="text-xs text-red-300 mb-1">ERROR:</div>
                      <div className="text-xs text-red-400">{selectedTask.error}</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
