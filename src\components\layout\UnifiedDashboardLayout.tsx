import React from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import { EnhancedAppLayout } from "./EnhancedAppLayout";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { AINavigationAssistant } from "@/components/ai/AINavigationAssistant";

// Dashboard Components
import Dashboard from "@/pages/Dashboard";
import { AdminDashboard } from "@/components/admin/dashboard/AdminDashboard";

// Admin Components
import { UserManagement } from "@/components/admin/UserManagement";
import { DepartmentManagement } from "@/components/admin/DepartmentManagement";
import { ProjectManagement } from "@/components/admin/ProjectManagement";
import { AdminReportsManagement } from "@/components/admin/ReportsManagement";

import { CommunicationCenter } from "@/components/admin/CommunicationCenter";
import SystemDiagnosticsPage from "@/pages/admin/SystemDiagnosticsPage";
import IntegrationsPage from "@/pages/admin/IntegrationsPage";
import DatabasePopulatePage from "@/pages/admin/DatabasePopulatePage";
import APIKeysPage from "@/pages/admin/APIKeysPage";
import AdminSettings from "@/pages/admin/AdminSettings";
import CommunicationPage from "@/pages/admin/CommunicationPage";
import NotificationManagement from "@/pages/admin/NotificationManagement";

// Manager Components
import ManagerTeamPage from "@/pages/manager/ManagerTeamPage";
import ManagerTimeTrackingPage from "@/pages/manager/ManagerTimeTrackingPage";
import ManagerWorkBoardPage from "@/pages/manager/ManagerWorkBoardPage";
import ManagerLeavePage from "@/pages/manager/ManagerLeavePage";
import ManagerSitesPage from "@/pages/manager/ManagerSitesPage";
import ManagerMeetingsPage from "@/pages/manager/ManagerMeetingsPage";
import ManagerMemosPage from "@/pages/manager/ManagerMemosPage";
import { InvoiceManagement } from "@/components/manager/InvoiceManagement";
import { ReportManagement } from "@/components/manager/ReportManagement";
import ManagerSettings from "@/pages/manager/ManagerSettings";

// Staff Components
import { MyTasks } from "@/components/staff/MyTasks";
import StaffCurrentTasksPage from "@/pages/staff/StaffCurrentTasksPage";
import StaffMyTasksPage from "@/pages/staff/StaffMyTasksPage";
import StaffProjectProgressPage from "@/pages/staff/StaffProjectProgressPage";
import StaffTaskUpdatesPage from "@/pages/staff/StaffTaskUpdatesPage";
import Memos from "@/pages/staff/Memos";
import Reports from "@/pages/staff/Reports";
import StaffTelecomReportsPage from "@/pages/staff/StaffTelecomReportsPage";
import StaffBatteryReportsPage from "@/pages/staff/StaffBatteryReportsPage";
import Meetings from "@/pages/staff/Meetings";
import StaffProfilePage from "@/pages/staff/StaffProfilePage";
import StaffSettingsPage from "@/pages/staff/StaffSettingsPage";

// Accountant Components
import { InvoiceManagement as AccountantInvoiceManagement } from "@/components/accountant/InvoiceManagement";

// Staff Admin Components
import ExpenseManagement from "@/components/staff-admin/modules/ExpenseManagement";
import FleetManagement from "@/components/staff-admin/modules/FleetManagement";
import AssetInventoryManagement from "@/components/staff-admin/modules/AssetInventoryManagement";

// Shared Pages
import AssetsPage from "@/pages/assets/AssetsPage";
import ConstructionPage from "@/pages/construction/ConstructionPage";
import DocumentsPage from "@/pages/documents/DocumentsPage";
import FleetPage from "@/pages/fleet/FleetPage";
import FinancialPage from "@/pages/financial/FinancialPage";
import BatteryPage from "@/pages/battery/BatteryPage";
import TasksPage from "@/pages/tasks/TasksPage";
import ProcurementPage from "@/pages/procurement/ProcurementPage";
import ReportsPage from "@/pages/reports/ReportsPage";
import SettingsPage from "@/pages/settings/SettingsPage";
import ToolzPage from "@/pages/toolz/ToolzPage";
import AccountPage from "@/pages/account/AccountPage";
import FilesPage from "@/pages/files/FilesPage";

// AI Pages
const AIPage = React.lazy(() => import("@/pages/ai/AIPage"));

export const UnifiedDashboardLayout: React.FC = () => {
  const location = useLocation();

  // Extract the route path for content switching
  const getRouteContent = () => {
    const path = location.pathname;

    // Admin Routes
    if (path === "/dashboard/admin") return <ProtectedRoute requiredRole="admin"><AdminDashboard /></ProtectedRoute>;
    if (path === "/dashboard/admin/users") return <ProtectedRoute requiredRole="admin"><UserManagement /></ProtectedRoute>;
    if (path === "/dashboard/admin/departments") return <ProtectedRoute requiredRole="admin"><DepartmentManagement /></ProtectedRoute>;
    if (path === "/dashboard/admin/projects") return <ProtectedRoute requiredRole="admin"><ProjectManagement /></ProtectedRoute>;
    if (path === "/dashboard/admin/reports") return <ProtectedRoute requiredRole="admin"><AdminReportsManagement /></ProtectedRoute>;

    if (path === "/dashboard/admin/communication") return <ProtectedRoute requiredRole="admin"><CommunicationPage /></ProtectedRoute>;
    if (path === "/dashboard/admin/database") return <ProtectedRoute requiredRole="admin"><DatabasePopulatePage /></ProtectedRoute>;
    if (path === "/dashboard/admin/integrations") return <ProtectedRoute requiredRole="admin"><IntegrationsPage /></ProtectedRoute>;
    if (path === "/dashboard/admin/diagnostics") return <ProtectedRoute requiredRole="admin"><SystemDiagnosticsPage /></ProtectedRoute>;
    if (path === "/dashboard/admin/ai") return <ProtectedRoute requiredRole="admin"><AIPage /></ProtectedRoute>;
    if (path === "/dashboard/admin/api-keys") return <ProtectedRoute requiredRole="admin"><APIKeysPage /></ProtectedRoute>;
    if (path === "/dashboard/admin/settings") return <ProtectedRoute requiredRole="admin"><AdminSettings /></ProtectedRoute>;
    if (path === "/dashboard/admin/notification-management") return <ProtectedRoute requiredRole="admin"><NotificationManagement /></ProtectedRoute>;

    // Manager Routes
    if (path === "/dashboard/manager") return <ProtectedRoute requiredRole="manager"><Dashboard /></ProtectedRoute>;
    if (path === "/dashboard/manager/projects") return <ProtectedRoute requiredRole="manager"><ProjectManagement /></ProtectedRoute>;
    if (path === "/dashboard/manager/team") return <ProtectedRoute requiredRole="manager"><ManagerTeamPage /></ProtectedRoute>;
    if (path === "/dashboard/manager/time") return <ProtectedRoute requiredRole="manager"><ManagerTimeTrackingPage /></ProtectedRoute>;
    if (path === "/dashboard/manager/workboard") return <ProtectedRoute requiredRole="manager"><ManagerWorkBoardPage /></ProtectedRoute>;
    if (path === "/dashboard/manager/leave") return <ProtectedRoute requiredRole="manager"><ManagerLeavePage /></ProtectedRoute>;
    if (path === "/dashboard/manager/sites") return <ProtectedRoute requiredRole="manager"><ManagerSitesPage /></ProtectedRoute>;
    if (path === "/dashboard/manager/memos") return <ProtectedRoute requiredRole="manager"><ManagerMemosPage /></ProtectedRoute>;
    if (path === "/dashboard/manager/meetings") return <ProtectedRoute requiredRole="manager"><ManagerMeetingsPage /></ProtectedRoute>;
    if (path === "/dashboard/manager/invoices") return <ProtectedRoute requiredRole="manager"><InvoiceManagement /></ProtectedRoute>;
    if (path === "/dashboard/manager/reports") return <ProtectedRoute requiredRole="manager"><ReportManagement /></ProtectedRoute>;
    if (path === "/dashboard/manager/settings") return <ProtectedRoute requiredRole="manager"><ManagerSettings /></ProtectedRoute>;

    // Staff Routes
    if (path === "/dashboard/staff") return <ProtectedRoute requiredRole="staff"><Dashboard /></ProtectedRoute>;
    if (path === "/dashboard/staff/tasks") return <ProtectedRoute requiredRole="staff"><MyTasks /></ProtectedRoute>;
    if (path === "/dashboard/staff/current-tasks") return <ProtectedRoute requiredRole="staff"><StaffCurrentTasksPage /></ProtectedRoute>;
    if (path === "/dashboard/staff/my-tasks") return <ProtectedRoute requiredRole="staff"><StaffMyTasksPage /></ProtectedRoute>;
    if (path === "/dashboard/staff/project-progress") return <ProtectedRoute requiredRole="staff"><StaffProjectProgressPage /></ProtectedRoute>;
    if (path === "/dashboard/staff/task-updates") return <ProtectedRoute requiredRole="staff"><StaffTaskUpdatesPage /></ProtectedRoute>;
    if (path === "/dashboard/staff/memos") return <ProtectedRoute requiredRole="staff"><Memos /></ProtectedRoute>;
    if (path === "/dashboard/staff/reports") return <ProtectedRoute requiredRole="staff"><Reports /></ProtectedRoute>;
    if (path === "/dashboard/staff/telecom-reports") return <ProtectedRoute requiredRole="staff"><StaffTelecomReportsPage /></ProtectedRoute>;
    if (path === "/dashboard/staff/battery-reports") return <ProtectedRoute requiredRole="staff"><StaffBatteryReportsPage /></ProtectedRoute>;
    if (path === "/dashboard/staff/meetings") return <ProtectedRoute requiredRole="staff"><Meetings /></ProtectedRoute>;
    if (path === "/dashboard/staff/profile") return <ProtectedRoute requiredRole="staff"><StaffProfilePage /></ProtectedRoute>;
    if (path === "/dashboard/staff/settings") return <ProtectedRoute requiredRole="staff"><StaffSettingsPage /></ProtectedRoute>;

    // Accountant Routes
    if (path === "/dashboard/accountant") return <ProtectedRoute requiredRole="accountant"><Dashboard /></ProtectedRoute>;
    if (path === "/dashboard/accountant/invoices") return <ProtectedRoute requiredRole="accountant"><AccountantInvoiceManagement /></ProtectedRoute>;

    // Staff Admin Routes
    if (path === "/dashboard/staff-admin") return <ProtectedRoute requiredRole="staff-admin"><Dashboard /></ProtectedRoute>;
    if (path === "/dashboard/staff-admin/expenses") return <ProtectedRoute requiredRole="staff-admin"><ExpenseManagement /></ProtectedRoute>;
    if (path === "/dashboard/staff-admin/fleet") return <ProtectedRoute requiredRole="staff-admin"><FleetManagement /></ProtectedRoute>;
    if (path === "/dashboard/staff-admin/assets") return <ProtectedRoute requiredRole="staff-admin"><AssetInventoryManagement /></ProtectedRoute>;

    // Shared Routes
    if (path === "/dashboard/assets") return <ProtectedRoute><AssetsPage /></ProtectedRoute>;
    if (path === "/dashboard/construction") return <ProtectedRoute><ConstructionPage /></ProtectedRoute>;
    if (path === "/dashboard/documents") return <ProtectedRoute><DocumentsPage /></ProtectedRoute>;
    if (path === "/dashboard/fleet") return <ProtectedRoute><FleetPage /></ProtectedRoute>;
    if (path === "/dashboard/financial") return <ProtectedRoute><FinancialPage /></ProtectedRoute>;
    if (path === "/dashboard/battery") return <ProtectedRoute><BatteryPage /></ProtectedRoute>;
    if (path === "/dashboard/tasks") return <ProtectedRoute><TasksPage /></ProtectedRoute>;
    if (path === "/dashboard/procurement") return <ProtectedRoute><ProcurementPage /></ProtectedRoute>;
    if (path === "/dashboard/reports") return <ProtectedRoute><ReportsPage /></ProtectedRoute>;
    if (path === "/dashboard/settings") return <ProtectedRoute><SettingsPage /></ProtectedRoute>;
    if (path === "/dashboard/toolz") return <ProtectedRoute><ToolzPage /></ProtectedRoute>;
    if (path === "/dashboard/ai") return <ProtectedRoute><AIPage /></ProtectedRoute>;
    if (path === "/dashboard/account") return <ProtectedRoute><AccountPage /></ProtectedRoute>;
    if (path === "/dashboard/files") return <ProtectedRoute><FilesPage /></ProtectedRoute>;

    // Default fallback
    return <div className="p-8 text-center">Page not found</div>;
  };

  return (
    <EnhancedAppLayout>
      {getRouteContent()}
      <AINavigationAssistant />
    </EnhancedAppLayout>
  );
}; 