import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { defineConfig } from 'vite';

export default defineConfig({
  server: {
    host: 'localhost',
    port: 8083,
    watch: {
      // usePolling: true, // Enable only if needed
    },
    middlewareMode: false,
    fs: {
      strict: false
    },
    hmr: {
      overlay: true
    },
    cors: true
  },
  esbuild: {
    target: 'es2020',
    format: 'esm'
  },
  plugins: [
    react()
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  define: {
    global: 'globalThis',
  },

  build: {
    target: ['es2020', 'edge90', 'firefox88', 'chrome90', 'safari14'], // Align with browserslist
    chunkSizeWarningLimit: 1000,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false, // Keep console.log for debugging
        drop_debugger: true, // Remove debugger statements
      },
    },
    rollupOptions: {
      output: {
        format: 'es',
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        manualChunks(id: string) {
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            if (id.includes('lucide-react')) {
              return 'icons';
            }
            return 'vendor';
          }
          if (id.includes('src/components/voice')) {
            return 'voice';
          }
        },
      },
    },
    assetsDir: 'assets',
    sourcemap: true, // Enable source maps for debugging
  },
optimizeDeps: {
  include: ['react', 'react-dom', 'jspdf', 'jspdf-autotable', 'sonner', 'performance-now'],
},
});
