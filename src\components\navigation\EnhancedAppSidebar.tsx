import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import {
  LayoutDashboard, Users, Clock, Clipboard, FileCheck, Network,
  BarChart3, StickyNote, FileText, DollarSign, Car, Calendar,
  Settings, ChevronDown, ChevronRight, User, Building2,
  FileImage, Wrench, Battery, Construction, Package, Brain,
  Shield, ClipboardList, Key, Database, Bell, TrendingUp, CheckSquare,
  BookOpen, ExternalLink, ShoppingCart
} from "lucide-react";
import { useAuth } from "@/components/auth/AuthProvider";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
  useSidebar,
} from "@/components/ui/sidebar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { LogoutButton } from "@/components/auth/LogoutButton";
import { Badge } from "@/components/ui/badge";
import { QuickCacheClearButton } from "@/components/ui/QuickCacheClearButton";

interface MenuItem {
  title: string;
  href: string;
  icon: any;
  subItems?: MenuItem[];
  badge?: string;
}

export function EnhancedAppSidebar() {
  const { userProfile } = useAuth();
  const { state } = useSidebar();
  const location = useLocation();
  const [expandedGroups, setExpandedGroups] = useState<string[]>(["main", "operations"]);

  const toggleGroup = (groupName: string) => {
    setExpandedGroups(prev =>
      prev.includes(groupName)
        ? prev.filter(g => g !== groupName)
        : [...prev, groupName]
    );
  };

  const handleUserManual = () => {
    // Open user manual in new tab
    window.open('/user-manual.html', '_blank');
  };

  // Simplified and consolidated menu items based on role
  const getMenuItems = (): MenuItem[] => {
    const role = userProfile?.role || userProfile?.account_type;
    
    const commonItems = [
      { title: "AI Assistant", href: "/dashboard/ai", icon: Brain },
      { title: "Documents", href: "/dashboard/documents", icon: FileImage },
      { title: "Settings", href: "/dashboard/settings", icon: Settings },
    ];
    
    switch (role) {
      case 'admin':
        return [
          { title: "Dashboard", href: "/dashboard/admin", icon: LayoutDashboard },
          { title: "User Management", href: "/dashboard/admin/users", icon: Users },

          { title: "System Diagnostics", href: "/dashboard/admin/diagnostics", icon: BarChart3 },
          { title: "API Keys", href: "/dashboard/admin/api-keys", icon: Key },
          { title: "Database Management", href: "/dashboard/admin/database", icon: Database },
          { title: "Integrations", href: "/dashboard/admin/integrations", icon: Shield },
          { title: "AI Management", href: "/dashboard/admin/ai", icon: Brain },
          { title: "Settings", href: "/dashboard/admin/settings", icon: Settings },
          { title: "Departments", href: "/dashboard/admin/departments", icon: Building2 },
          { title: "Projects", href: "/dashboard/admin/projects", icon: Clipboard },
          { title: "Reports", href: "/dashboard/admin/reports", icon: BarChart3 },
          { title: "Activities", href: "/dashboard/admin/activities", icon: Calendar },
          { title: "Communication", href: "/dashboard/admin/communication", icon: StickyNote },
          { title: "Construction", href: "/dashboard/construction", icon: Construction },
          { title: "Fleet", href: "/dashboard/fleet", icon: Car },
          { title: "Battery", href: "/dashboard/battery", icon: Battery },
          { title: "Assets", href: "/dashboard/assets", icon: Package },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          { title: "Notification Management", href: "/dashboard/admin/notification-management", icon: Bell },
          ...commonItems,
        ];
      
      case 'manager':
        return [
          { title: "Dashboard", href: "/dashboard/manager", icon: LayoutDashboard },
          { title: "Projects", href: "/dashboard/manager/projects", icon: Clipboard },
          { title: "Team", href: "/dashboard/manager/team", icon: Users },
          { title: "Time Tracking", href: "/dashboard/manager/time", icon: Clock },
          { title: "Work Board", href: "/dashboard/manager/workboard", icon: Clipboard },
          { title: "Leave Requests", href: "/dashboard/manager/leave", icon: FileCheck },
          { title: "Sites", href: "/dashboard/manager/sites", icon: Network },
          { title: "Memos", href: "/dashboard/manager/memos", icon: StickyNote },
          { title: "Reports", href: "/dashboard/manager/reports", icon: BarChart3 },
          { title: "Invoices", href: "/dashboard/manager/invoices", icon: FileText },
          { title: "Meetings", href: "/dashboard/manager/meetings", icon: Calendar },
          { title: "Fleet", href: "/dashboard/fleet", icon: Car },
          { title: "Construction", href: "/dashboard/construction", icon: Construction },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          ...commonItems,
        ];
      
      case 'accountant':
        return [
          { title: "Dashboard", href: "/dashboard/accountant", icon: LayoutDashboard },
          { title: "Invoices", href: "/dashboard/accountant/invoices", icon: FileText },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Assets", href: "/dashboard/assets", icon: Package },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          ...commonItems,
        ];

      case 'staff-admin':
        return [
          { title: "Dashboard", href: "/dashboard/staff-admin", icon: LayoutDashboard },
          { title: "Expenses", href: "/dashboard/staff-admin/expenses", icon: DollarSign },
          { title: "Fleet", href: "/dashboard/staff-admin/fleet", icon: Car },
          { title: "Assets", href: "/dashboard/staff-admin/assets", icon: Package },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Construction", href: "/dashboard/construction", icon: Construction },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          { title: "Reports", href: "/dashboard/reports", icon: BarChart3 },
          ...commonItems,
        ];
      
      default: // staff
        return [
          { title: "Dashboard", href: "/dashboard/staff", icon: LayoutDashboard },
          { title: "Current Tasks", href: "/dashboard/staff/current-tasks", icon: Clock },
          { title: "My Tasks", href: "/dashboard/staff/my-tasks", icon: FileCheck },
          { title: "Project Progress", href: "/dashboard/staff/project-progress", icon: TrendingUp },
          { title: "Task Updates", href: "/dashboard/staff/task-updates", icon: CheckSquare },
          { title: "Reports", href: "/dashboard/staff/reports", icon: BarChart3 },
          { title: "Battery Reports", href: "/dashboard/staff/battery-reports", icon: Battery },
          { title: "TOOLZ", href: "/dashboard/toolz", icon: Wrench },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          { title: "Memos", href: "/dashboard/staff/memos", icon: StickyNote },
          { title: "Meetings", href: "/dashboard/staff/meetings", icon: Calendar },
          { title: "Profile", href: "/dashboard/staff/profile", icon: User },
          { title: "Settings", href: "/dashboard/staff/settings", icon: Settings },
          { title: "AI Assistant", href: "/dashboard/ai", icon: Brain },
          { title: "Documents", href: "/dashboard/documents", icon: FileImage },
        ];
    }
  };

  const menuItems = getMenuItems();
  const isCollapsed = state === "collapsed";

  return (
    <Sidebar className="border-r bg-gradient-to-b from-background to-muted/20 border-border/50">
      <SidebarContent>
        {/* Enhanced User Profile Section */}
        <SidebarGroup>
          <div className="flex items-center gap-3 p-4 border-b bg-gradient-to-r from-primary/5 to-secondary/10 rounded-lg m-2">
            <Avatar className="h-10 w-10 ring-2 ring-primary/20 transition-all duration-300 hover:ring-primary/40">
              <AvatarImage src={userProfile?.avatar_url} />
              <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                {userProfile?.full_name?.charAt(0) || userProfile?.email?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold truncate">
                  {userProfile?.full_name || 'User'}
                </p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="text-xs bg-secondary/10 text-secondary border-secondary/20">
                    {userProfile?.role || userProfile?.account_type || 'Staff'}
                  </Badge>
                </div>
              </div>
            )}
          </div>
        </SidebarGroup>

        {/* Enhanced Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-4 py-2">
            <div className="flex items-center justify-between w-full">
              <span>Navigation</span>
              {!isCollapsed && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-primary/10 transition-colors"
                  onClick={() => toggleGroup("main")}
                >
                  {expandedGroups.includes("main") ? 
                    <ChevronDown className="h-3 w-3" /> : 
                    <ChevronRight className="h-3 w-3" />
                  }
                </Button>
              )}
            </div>
          </SidebarGroupLabel>
          
          {(isCollapsed || expandedGroups.includes("main")) && (
            <SidebarGroupContent>
              <SidebarMenu>
                {menuItems.map((item) => (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton asChild>
                      <NavLink
                        to={item.href}
                        className={({ isActive }) =>
                          `sidebar-nav-button nav-item flex items-center gap-3 text-sm font-medium group px-3 py-2.5 rounded-2xl transition-all duration-300 transform ${
                            isActive
                              ? 'active'
                              : ''
                          }`
                        }
                      >
                        <div className={`p-1.5 rounded-xl transition-all duration-300 ${
                          location.pathname === item.href
                            ? 'bg-white/20'
                            : 'bg-white/10 group-hover:bg-white/20'
                        }`}>
                          <item.icon className="h-4 w-4 shrink-0 transition-transform group-hover:scale-110" />
                        </div>
                        {!isCollapsed && (
                          <div className="flex items-center justify-between w-full">
                            <span className="font-medium">{item.title}</span>
                            {item.badge && (
                              <Badge
                                variant="secondary"
                                className="ml-2 text-xs bg-muted/50 text-muted-foreground border-muted"
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </div>
                        )}
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          )}
        </SidebarGroup>

        {/* Enhanced Logout Section */}
        <SidebarGroup className="mt-auto">
          <SidebarGroupContent>
            <div className="p-2 space-y-2">
              {/* User Manual Button */}
              <Button
                onClick={handleUserManual}
                variant="outline"
                size="sm"
                className="w-full glassmorphism hover:bg-primary/10 hover:text-primary hover:border-primary/20 transition-all duration-200 group"
                title="Open User Manual"
              >
                <BookOpen className={`h-4 w-4 ${!isCollapsed ? 'mr-2' : ''} group-hover:text-primary transition-colors`} />
                {!isCollapsed && (
                  <>
                    User Manual
                    <ExternalLink className="h-3 w-3 ml-auto opacity-60" />
                  </>
                )}
              </Button>

              {!isCollapsed && (
                <>
                  <QuickCacheClearButton
                    variant="outline"
                    size="sm"
                    showText={true}
                  />
                  <LogoutButton
                    variant="outline"
                    className="w-full hover:bg-destructive/10 hover:text-destructive hover:border-destructive/20 transition-all duration-200"
                  />
                </>
              )}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
