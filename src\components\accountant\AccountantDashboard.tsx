import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { EnhancedDashboardStats } from "@/components/dashboard/EnhancedDashboardStats";
import { Enhanced<PERSON>hart } from "@/components/charts/EnhancedChart";
import { DollarSign, FileText, TrendingUp, Calculator, BarChart3, CreditCard, PieChart, Activity } from "lucide-react";
import { CompactTimeCard } from "@/components/time-tracking/CompactTimeCard";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, LineChart, Line, PieChart as Rechart<PERSON>ie<PERSON>hart, Pie, Cell } from "recharts";
import { useEffect } from "react";

export const AccountantDashboard = () => {
  const queryClient = useQueryClient();

  // Fetch financial data from database with real-time updates
  const { data: invoicesData } = useQuery({
    queryKey: ['accountant-invoices'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('invoices')
        .select('amount, total_amount, payment_status, created_at, due_date')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching invoices:', error);
        // Return empty array if table doesn't exist
        return [];
      }
      return data || [];
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch expenses data with real-time updates
  const { data: expensesData } = useQuery({
    queryKey: ['accountant-expenses'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('expense_reports')
        .select('amount, category, status, created_at, approval_status')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching expenses:', error);
        // Return empty array if table doesn't exist
        return [];
      }
      return data || [];
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Set up real-time subscriptions for live data updates
  useEffect(() => {
    // Subscribe to invoices changes
    const invoicesChannel = supabase
      .channel('invoices-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'invoices'
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['accountant-invoices'] });
      })
      .subscribe();

    // Subscribe to expenses changes
    const expensesChannel = supabase
      .channel('expenses-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'expense_reports'
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['accountant-expenses'] });
      })
      .subscribe();

    // Cleanup subscriptions
    return () => {
      supabase.removeChannel(invoicesChannel);
      supabase.removeChannel(expensesChannel);
    };
  }, [queryClient]);

  // Calculate financial metrics
  const calculateFinancialData = () => {
    if (!invoicesData || !expensesData) return [];

    // Handle empty arrays gracefully
    const invoices = Array.isArray(invoicesData) ? invoicesData : [];
    const expenses = Array.isArray(expensesData) ? expensesData : [];

    const last6Months = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      return {
        name: date.toLocaleDateString('en', { month: 'short' }),
        month: date.getMonth(),
        year: date.getFullYear(),
        revenue: 0,
        expenses: 0,
        profit: 0
      };
    }).reverse();

    // Calculate revenue from paid invoices
    invoices.forEach(invoice => {
      if (invoice.payment_status === 'paid') {
        const invoiceDate = new Date(invoice.created_at);
        const monthData = last6Months.find(m =>
          m.month === invoiceDate.getMonth() &&
          m.year === invoiceDate.getFullYear()
        );
        if (monthData) {
          monthData.revenue += invoice.total_amount || invoice.amount || 0;
        }
      }
    });

    // Calculate expenses from approved reports
    expenses.forEach(expense => {
      if (expense.approval_status === 'approved') {
        const expenseDate = new Date(expense.created_at);
        const monthData = last6Months.find(m =>
          m.month === expenseDate.getMonth() &&
          m.year === expenseDate.getFullYear()
        );
        if (monthData) {
          monthData.expenses += expense.amount || 0;
        }
      }
    });

    // Calculate profit
    last6Months.forEach(month => {
      month.profit = month.revenue - month.expenses;
    });

    return last6Months;
  };

  // Calculate expense breakdown by category
  const calculateExpenseBreakdown = () => {
    if (!expensesData) return [];

    const categoryTotals: { [key: string]: number } = {};
    let totalExpenses = 0;

    expensesData
      .filter(expense => expense.approval_status === 'approved')
      .forEach(expense => {
        const category = expense.category || 'Other';
        categoryTotals[category] = (categoryTotals[category] || 0) + expense.amount;
        totalExpenses += expense.amount;
      });

    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
    
    return Object.entries(categoryTotals).map(([name, value], index) => ({
      name,
      value: Math.round((value / totalExpenses) * 100),
      color: colors[index % colors.length]
    }));
  };

  const financialData = calculateFinancialData();
  const expenseBreakdown = calculateExpenseBreakdown();

  // Calculate current month metrics
  const currentMonthRevenue = financialData[financialData.length - 1]?.revenue || 0;
  const currentMonthExpenses = financialData[financialData.length - 1]?.expenses || 0;
  const currentMonthProfit = currentMonthRevenue - currentMonthExpenses;
  const pendingInvoicesCount = invoicesData?.filter(inv => inv.payment_status === 'pending').length || 0;

  // Calculate trends (compared to previous month)
  const prevMonthRevenue = financialData[financialData.length - 2]?.revenue || 0;
  const prevMonthExpenses = financialData[financialData.length - 2]?.expenses || 0;
  const prevMonthProfit = prevMonthRevenue - prevMonthExpenses;

  const revenueTrend = prevMonthRevenue > 0 ? ((currentMonthRevenue - prevMonthRevenue) / prevMonthRevenue) * 100 : 0;
  const expenseTrend = prevMonthExpenses > 0 ? ((currentMonthExpenses - prevMonthExpenses) / prevMonthExpenses) * 100 : 0;
  const profitTrend = prevMonthProfit > 0 ? ((currentMonthProfit - prevMonthProfit) / prevMonthProfit) * 100 : 0;

  const stats = [
    {
      title: "Monthly Revenue",
      value: `₦${currentMonthRevenue.toLocaleString()}`,
      icon: <DollarSign className="h-8 w-8" />,
      trend: { value: Math.round(revenueTrend * 10) / 10, label: "from last month" },
      color: 'green' as const
    },
    {
      title: "Total Expenses",
      value: `₦${currentMonthExpenses.toLocaleString()}`,
      icon: <CreditCard className="h-8 w-8" />,
      trend: { value: Math.round(expenseTrend * 10) / 10, label: "from last month" },
      color: 'red' as const
    },
    {
      title: "Net Profit",
      value: `₦${currentMonthProfit.toLocaleString()}`,
      icon: <TrendingUp className="h-8 w-8" />,
      trend: { value: Math.round(profitTrend * 10) / 10, label: "from last month" },
      color: 'blue' as const
    },
    {
      title: "Pending Invoices",
      value: pendingInvoicesCount.toString(),
      icon: <FileText className="h-8 w-8" />,
      trend: { value: 0, label: "invoices to review" },
      color: 'purple' as const
    }
  ];

  return (
    <div className="space-y-8 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-[#8b5cf6]/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-48 h-48 bg-gradient-to-tr from-[#0FA0CE]/5 to-transparent rounded-full blur-2xl"></div>
      </div>

      <div className="relative z-10">
        <EnhancedDashboardStats stats={stats} />

        {/* Time Attendance Card */}
        <div className="mt-8">
          <CompactTimeCard userRole="accountant" showControls={true} />
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 mt-8">
          <EnhancedChart
            title="Financial Performance Overview"
            icon={<BarChart3 className="h-5 w-5" />}
            timeFilter
            exportable
            refreshable
            onRefresh={() => {
              queryClient.invalidateQueries({ queryKey: ['accountant-invoices'] });
              queryClient.invalidateQueries({ queryKey: ['accountant-expenses'] });
            }}
          >
            <BarChart data={financialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="revenue" fill="#10b981" />
              <Bar dataKey="expenses" fill="#ef4444" />
              <Bar dataKey="profit" fill="#3b82f6" />
            </BarChart>
          </EnhancedChart>

          <EnhancedChart
            title="Expense Distribution Analysis (Real-time)"
            icon={<Calculator className="h-5 w-5" />}
            exportable
            refreshable
            onRefresh={() => {
              queryClient.invalidateQueries({ queryKey: ['accountant-expenses'] });
            }}
          >
            <RechartPieChart>
              <Pie
                data={expenseBreakdown}
                cx="50%"
                cy="50%"
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {expenseBreakdown.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </RechartPieChart>
          </EnhancedChart>
        </div>

        <div className="grid grid-cols-1 gap-8 mt-8">
          <EnhancedChart
            title="Revenue & Profitability Trends (Live Data)"
            icon={<TrendingUp className="h-5 w-5" />}
            timeFilter
            exportable
            refreshable
            fullscreen
            onRefresh={() => {
              queryClient.invalidateQueries({ queryKey: ['accountant-invoices'] });
              queryClient.invalidateQueries({ queryKey: ['accountant-expenses'] });
            }}
          >
            <LineChart data={financialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="revenue" stroke="#10b981" strokeWidth={3} />
              <Line type="monotone" dataKey="profit" stroke="#3b82f6" strokeWidth={3} />
              <Line type="monotone" dataKey="expenses" stroke="#ef4444" strokeWidth={2} />
            </LineChart>
          </EnhancedChart>
        </div>
      </div>
    </div>
  );
};
