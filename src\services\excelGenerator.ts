import ExcelJS from 'exceljs';
import { supabase } from '@/integrations/supabase/client';

export interface ExcelGenerationConfig {
  documentName: string;
  sheets: SheetConfig[];
  charts: ChartConfig[];
  styling: StylingConfig;
  aiEnhancements?: AIEnhancementConfig;
}

export interface SheetConfig {
  name: string;
  data: any[];
  headers: string[];
  formatting?: CellFormatting;
  tables?: TableConfig[];
  pivotTables?: PivotTableConfig[];
}

export interface ChartConfig {
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'area' | 'column' | 'doughnut';
  title: string;
  sheetName: string;
  dataRange: string;
  position: { row: number; col: number };
  size: { width: number; height: number };
  styling: ChartStyling;
}

export interface TableConfig {
  name: string;
  range: string;
  headers: string[];
  data: any[];
  styling: TableStyling;
  autoFilter: boolean;
  totalsRow: boolean;
}

export interface PivotTableConfig {
  name: string;
  sourceRange: string;
  rows: string[];
  columns: string[];
  values: string[];
  filters: string[];
}

export interface CellFormatting {
  font?: {
    name?: string;
    size?: number;
    bold?: boolean;
    italic?: boolean;
    color?: string;
  };
  fill?: {
    type: 'solid' | 'gradient';
    color?: string;
    gradientColors?: string[];
  };
  border?: {
    style: 'thin' | 'medium' | 'thick';
    color?: string;
  };
  alignment?: {
    horizontal?: 'left' | 'center' | 'right';
    vertical?: 'top' | 'middle' | 'bottom';
    wrapText?: boolean;
  };
  numberFormat?: string;
}

export interface ChartStyling {
  colors: string[];
  theme: 'modern' | 'classic' | 'colorful' | 'monochrome';
  showLegend: boolean;
  showDataLabels: boolean;
  gridLines: boolean;
}

export interface TableStyling {
  headerStyle: CellFormatting;
  dataStyle: CellFormatting;
  alternateRowColor?: string;
  borderStyle: 'none' | 'light' | 'medium' | 'heavy';
}

export interface StylingConfig {
  theme: 'corporate' | 'modern' | 'colorful' | 'minimal';
  primaryColor: string;
  secondaryColor: string;
  fontFamily: string;
  fontSize: number;
}

export interface AIEnhancementConfig {
  generateInsights: boolean;
  suggestCharts: boolean;
  optimizeLayout: boolean;
  addCommentary: boolean;
  dataAnalysis: boolean;
}

export class ExcelGenerator {
  private workbook: ExcelJS.Workbook;
  private config: ExcelGenerationConfig;

  constructor(config: ExcelGenerationConfig) {
    this.workbook = new ExcelJS.Workbook();
    this.config = config;
  }

  async generateExcel(): Promise<Blob> {
    try {
      // Create sheets with data
      for (const sheetConfig of this.config.sheets) {
        await this.createSheet(sheetConfig);
      }

      // Add AI-generated insights if enabled
      if (this.config.aiEnhancements?.generateInsights) {
        await this.addAIInsights();
      }

      // Generate the Excel file
      const excelBuffer = await this.workbook.xlsx.writeBuffer();

      return new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
    } catch (error) {
      console.error('Error generating Excel file:', error);
      throw new Error('Failed to generate Excel file');
    }
  }

  private async createSheet(sheetConfig: SheetConfig): Promise<void> {
    // Create worksheet
    const worksheet = this.workbook.addWorksheet(sheetConfig.name);

    // Add headers
    if (sheetConfig.headers.length > 0) {
      worksheet.addRow(sheetConfig.headers);

      // Style headers
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
    }

    // Add data
    if (sheetConfig.data.length > 0) {
      const dataArray = this.convertDataToArray(sheetConfig.data, sheetConfig.headers);
      dataArray.forEach(row => {
        worksheet.addRow(row);
      });
    }

    // Apply formatting
    if (sheetConfig.formatting) {
      this.applyFormatting(worksheet, sheetConfig.formatting);
    }

    // Create tables
    if (sheetConfig.tables) {
      for (const tableConfig of sheetConfig.tables) {
        this.createTable(worksheet, tableConfig);
      }
    }

    // Add charts (metadata for chart creation)
    const chartsForSheet = this.config.charts.filter(chart => chart.sheetName === sheetConfig.name);
    if (chartsForSheet.length > 0) {
      this.addChartMetadata(worksheet, chartsForSheet);
    }

    // Set column widths
    this.autoSizeColumns(worksheet, sheetConfig.data);
  }

  private convertDataToArray(data: any[], headers: string[]): any[][] {
    return data.map(row => {
      if (Array.isArray(row)) {
        return row;
      } else if (typeof row === 'object') {
        return headers.map(header => row[header] || '');
      } else {
        return [row];
      }
    });
  }

  private applyFormatting(worksheet: ExcelJS.Worksheet, formatting: CellFormatting): void {
    // Apply header formatting to first row
    const headerRow = worksheet.getRow(1);

    if (formatting.font) {
      headerRow.font = {
        name: formatting.font.name || 'Calibri',
        size: formatting.font.size || 11,
        bold: formatting.font.bold || true,
        color: { argb: formatting.font.color?.replace('#', '') || 'FF000000' }
      };
    }

    if (formatting.fill) {
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: formatting.fill.color?.replace('#', '') || 'FFE7E6E6' }
      };
    }

    if (formatting.alignment) {
      headerRow.alignment = {
        horizontal: formatting.alignment.horizontal || 'center',
        vertical: formatting.alignment.vertical || 'middle',
        wrapText: formatting.alignment.wrapText || false
      };
    }
  }

  private createTable(worksheet: ExcelJS.Worksheet, tableConfig: TableConfig): void {
    // ExcelJS supports native table creation
    try {
      worksheet.addTable({
        name: tableConfig.name,
        ref: tableConfig.range,
        headerRow: true,
        style: {
          theme: 'TableStyleMedium2',
          showRowStripes: true,
        },
        columns: tableConfig.headers.map(header => ({ name: header })),
        rows: []
      });
    } catch (error) {
      console.warn('Could not create table:', error);
    }
  }

  private addChartMetadata(worksheet: ExcelJS.Worksheet, charts: ChartConfig[]): void {
    // Add chart metadata as comments for post-processing
    charts.forEach((chart, index) => {
      const metadataCell = worksheet.getCell(`Z${index + 1}`);
      metadataCell.value = JSON.stringify({
        type: chart.type,
        title: chart.title,
        dataRange: chart.dataRange,
        position: chart.position,
        size: chart.size,
        styling: chart.styling
      });
      metadataCell.note = `Chart: ${chart.title}`;
    });
  }

  private autoSizeColumns(worksheet: ExcelJS.Worksheet, data: any[]): void {
    if (data.length === 0) return;

    // Get column count from first data row
    const firstRow = data[0];
    const columnCount = Array.isArray(firstRow) ? firstRow.length : Object.keys(firstRow).length;

    // Calculate column widths based on content
    for (let col = 1; col <= columnCount; col++) {
      let maxWidth = 10; // Minimum width

      // Check header width
      const headerCell = worksheet.getCell(1, col);
      if (headerCell.value) {
        maxWidth = Math.max(maxWidth, String(headerCell.value).length);
      }

      // Check data rows
      for (let row = 2; row <= data.length + 1; row++) {
        const cell = worksheet.getCell(row, col);
        if (cell.value) {
          const cellValue = String(cell.value);
          maxWidth = Math.max(maxWidth, cellValue.length);
        }
      }

      // Set column width (max 50 characters)
      worksheet.getColumn(col).width = Math.min(maxWidth + 2, 50);
    }
  }

  private async addAIInsights(): Promise<void> {
    try {
      // Generate AI insights for the data
      const insights = await this.generateAIInsights();

      if (insights) {
        // Create insights sheet
        const insightsSheet = this.workbook.addWorksheet('AI Insights');

        // Add title
        insightsSheet.addRow(['AI-Generated Insights']);
        insightsSheet.addRow(['']); // Empty row

        // Add insights
        insights.forEach(insight => {
          insightsSheet.addRow([insight]);
        });

        // Apply formatting to insights sheet
        this.applyFormatting(insightsSheet, {
          font: { bold: true, size: 14, color: '#2563eb' },
          fill: { type: 'solid', color: '#f8fafc' }
        });

        // Style the title row
        const titleRow = insightsSheet.getRow(1);
        titleRow.font = { bold: true, size: 16, color: { argb: 'FF2563eb' } };
        titleRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFf8fafc' }
        };
      }
    } catch (error) {
      console.error('Error generating AI insights:', error);
    }
  }

  private async generateAIInsights(): Promise<string[]> {
    try {
      // Call AI service to generate insights
      const { data, error } = await supabase.functions.invoke('generate-data-insights', {
        body: {
          sheets: this.config.sheets,
          charts: this.config.charts
        }
      });

      if (error) throw error;
      return data.insights || [];
    } catch (error) {
      console.error('Error calling AI insights service:', error);
      return [];
    }
  }

  static async createFinancialReport(data: any[]): Promise<ExcelGenerationConfig> {
    return {
      documentName: 'Financial Report',
      sheets: [
        {
          name: 'Summary',
          headers: ['Metric', 'Value', 'Change %', 'Status'],
          data: [
            ['Total Revenue', '$1,250,000', '+15.2%', 'Good'],
            ['Total Expenses', '$890,000', '****%', 'Acceptable'],
            ['Net Profit', '$360,000', '+28.4%', 'Excellent'],
            ['Profit Margin', '28.8%', '****%', 'Good']
          ],
          formatting: {
            font: { name: 'Arial', size: 11 },
            fill: { type: 'solid', color: '#f0f9ff' }
          }
        },
        {
          name: 'Detailed Data',
          headers: ['Date', 'Revenue', 'Expenses', 'Profit'],
          data: data,
          tables: [{
            name: 'FinancialTable',
            range: 'A1:D100',
            headers: ['Date', 'Revenue', 'Expenses', 'Profit'],
            data: data,
            autoFilter: true,
            totalsRow: true,
            styling: {
              headerStyle: {
                font: { bold: true, color: '#ffffff' },
                fill: { type: 'solid', color: '#2563eb' }
              },
              dataStyle: {
                font: { size: 10 }
              },
              borderStyle: 'light'
            }
          }]
        }
      ],
      charts: [
        {
          type: 'column',
          title: 'Revenue vs Expenses',
          sheetName: 'Summary',
          dataRange: 'A1:D5',
          position: { row: 8, col: 1 },
          size: { width: 600, height: 400 },
          styling: {
            colors: ['#2563eb', '#dc2626', '#16a34a'],
            theme: 'modern',
            showLegend: true,
            showDataLabels: true,
            gridLines: true
          }
        },
        {
          type: 'pie',
          title: 'Expense Breakdown',
          sheetName: 'Summary',
          dataRange: 'A1:B10',
          position: { row: 8, col: 8 },
          size: { width: 400, height: 400 },
          styling: {
            colors: ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'],
            theme: 'colorful',
            showLegend: true,
            showDataLabels: true,
            gridLines: false
          }
        }
      ],
      styling: {
        theme: 'corporate',
        primaryColor: '#2563eb',
        secondaryColor: '#64748b',
        fontFamily: 'Arial',
        fontSize: 11
      },
      aiEnhancements: {
        generateInsights: true,
        suggestCharts: true,
        optimizeLayout: true,
        addCommentary: true,
        dataAnalysis: true
      }
    };
  }

  static async createProjectReport(projectData: any[]): Promise<ExcelGenerationConfig> {
    return {
      documentName: 'Project Report',
      sheets: [
        {
          name: 'Project Overview',
          headers: ['Project', 'Status', 'Progress %', 'Budget', 'Spent', 'Remaining'],
          data: projectData,
          formatting: {
            font: { name: 'Calibri', size: 11 },
            fill: { type: 'solid', color: '#f8fafc' }
          }
        },
        {
          name: 'Timeline',
          headers: ['Task', 'Start Date', 'End Date', 'Duration', 'Assigned To', 'Status'],
          data: [],
          tables: [{
            name: 'ProjectTimeline',
            range: 'A1:F50',
            headers: ['Task', 'Start Date', 'End Date', 'Duration', 'Assigned To', 'Status'],
            data: [],
            autoFilter: true,
            totalsRow: false,
            styling: {
              headerStyle: {
                font: { bold: true, color: '#ffffff' },
                fill: { type: 'solid', color: '#16a34a' }
              },
              dataStyle: {
                font: { size: 10 }
              },
              borderStyle: 'medium'
            }
          }]
        }
      ],
      charts: [
        {
          type: 'bar',
          title: 'Project Progress',
          sheetName: 'Project Overview',
          dataRange: 'A1:C10',
          position: { row: 12, col: 1 },
          size: { width: 800, height: 400 },
          styling: {
            colors: ['#16a34a', '#f59e0b', '#ef4444'],
            theme: 'modern',
            showLegend: true,
            showDataLabels: true,
            gridLines: true
          }
        }
      ],
      styling: {
        theme: 'modern',
        primaryColor: '#16a34a',
        secondaryColor: '#64748b',
        fontFamily: 'Calibri',
        fontSize: 11
      },
      aiEnhancements: {
        generateInsights: true,
        suggestCharts: true,
        optimizeLayout: true,
        addCommentary: true,
        dataAnalysis: true
      }
    };
  }
}
