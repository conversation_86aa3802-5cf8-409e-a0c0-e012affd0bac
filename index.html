<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CTNL AI Workboard</title>
    <meta name="description" content="Advanced AI-powered workforce management system with voice navigation and intelligent project planning" />
    <meta name="author" content="CTNL - Developed by Ifeanyi" />
    <meta name="keywords" content="AI, workforce management, project planning, voice commands, productivity" />

    <!-- App Icons -->
    <link rel="icon" type="image/png" href="/icon.png" />
    <link rel="apple-touch-icon" href="/icon.png" />

    <!-- Basic theme color -->
    <meta name="theme-color" content="#ff1c04" />

    <!-- Open Graph / Social Media -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="CTNL AI Workboard" />
    <meta property="og:description" content="Advanced AI-powered workforce management system" />
    <meta property="og:image" content="/og-image.png" />
    <meta property="og:url" content="https://ai.ctnigeria.com" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="CTNL AI Workboard" />
    <meta name="twitter:description" content="Advanced AI-powered workforce management system" />
    <meta name="twitter:image" content="/og-image.png" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rubik+Doodle+Shadow&display=swap" rel="stylesheet">
  </head>

  <body>
    <div id="root"></div>

    <!-- Emergency Cache Clearing Script (Safe Mode) -->
    <script>
      console.log('🧹 Emergency cache clearing (safe mode)...');

      // Check if we're in an infinite reload loop
      const reloadCount = parseInt(sessionStorage.getItem('reload-count') || '0');
      const maxReloads = 3;

      if (reloadCount >= maxReloads) {
        console.log('🛑 Max reloads reached, stopping to prevent infinite loop');
        sessionStorage.removeItem('reload-count');
        // Just clear caches without reloading
      } else {
        // Increment reload count
        sessionStorage.setItem('reload-count', (reloadCount + 1).toString());
      }

      // Clear service workers (safe)
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(function(registrations) {
          if (registrations.length > 0) {
            console.log('🔍 Found', registrations.length, 'service worker registrations');

            for(let registration of registrations) {
              console.log('🧹 Unregistering service worker:', registration.scope);
              registration.unregister().catch(function(error) {
                console.log('❌ Failed to unregister service worker:', error);
              });
            }
          }
        }).catch(function(error) {
          console.log('❌ Error getting service worker registrations:', error);
        });
      }

      // Clear caches (safe)
      if ('caches' in window) {
        caches.keys().then(function(cacheNames) {
          if (cacheNames.length > 0) {
            console.log('🔍 Found', cacheNames.length, 'caches');

            cacheNames.forEach(function(cacheName) {
              caches.delete(cacheName).then(function(success) {
                console.log('✅ Cache deleted:', cacheName, success);
              }).catch(function(error) {
                console.log('❌ Failed to delete cache:', cacheName, error);
              });
            });
          }
        }).catch(function(error) {
          console.log('❌ Error accessing caches:', error);
        });
      }

      // Clear PWA storage (safe)
      try {
        const pwaKeys = ['workbox-runtime', 'workbox-precache', 'pwa-cache', 'sw-cache'];
        pwaKeys.forEach(key => {
          if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
            console.log('🧹 Removed PWA key:', key);
          }
        });
      } catch (error) {
        console.log('❌ Error clearing PWA storage:', error);
      }

      // Version tracking (no reload)
      const currentVersion = '2024.12.18.002'; // Updated version
      const storedVersion = localStorage.getItem('app-version');

      if (storedVersion !== currentVersion) {
        console.log('🔄 Version updated:', storedVersion, '→', currentVersion);
        localStorage.setItem('app-version', currentVersion);
        // No automatic reload - let the app handle it
      }

      console.log('✅ Emergency cache clearing completed (safe mode)');
    </script>

    <!-- Block Grammarly and other browser extensions that cause console errors -->
    <script>
      // Block Grammarly extension errors
      if (typeof window !== 'undefined') {
        // Prevent Grammarly from injecting scripts
        const originalAppendChild = Element.prototype.appendChild;
        Element.prototype.appendChild = function(child) {
          if (child.tagName === 'SCRIPT' &&
              (child.src && child.src.includes('grammarly') ||
               child.textContent && child.textContent.includes('grammarly'))) {
            console.log('🚫 Blocked Grammarly script injection');
            return child;
          }
          return originalAppendChild.call(this, child);
        };

        // Block Grammarly console errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
          const message = args.join(' ');
          if (message.includes('grm ERROR') ||
              message.includes('Grammarly') ||
              message.includes('iterable') ||
              message.includes('Not supported: in app messages')) {
            // Silently ignore Grammarly errors
            return;
          }
          return originalConsoleError.apply(console, args);
        };

        // Block Grammarly warnings
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
          const message = args.join(' ');
          if (message.includes('grm') ||
              message.includes('Grammarly') ||
              message.includes('iterable')) {
            // Silently ignore Grammarly warnings
            return;
          }
          return originalConsoleWarn.apply(console, args);
        };

        // Prevent Grammarly from accessing certain APIs
        if (window.navigator && window.navigator.userAgent) {
          const originalUserAgent = window.navigator.userAgent;
          Object.defineProperty(window.navigator, 'userAgent', {
            get: function() {
              return originalUserAgent.replace(/Grammarly/gi, '');
            }
          });
        }

        console.log('🛡️ Grammarly blocking initialized');
      }
    </script>

    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
