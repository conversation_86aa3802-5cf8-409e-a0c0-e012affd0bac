import ExcelJS from 'exceljs';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

export interface ExportData {
  [key: string]: any;
}

export interface ExportOptions {
  filename?: string;
  title?: string;
  subtitle?: string;
  headers?: string[];
  orientation?: 'portrait' | 'landscape';
  includeTimestamp?: boolean;
  companyName?: string;
  logo?: string;
}

export class DocumentGenerator {
  private static formatFilename(filename: string, extension: string): string {
    const timestamp = new Date().toISOString().split('T')[0];
    return `${filename}-${timestamp}.${extension}`;
  }

  private static getDefaultOptions(): ExportOptions {
    return {
      filename: 'export',
      title: 'Data Export',
      subtitle: '',
      orientation: 'portrait',
      includeTimestamp: true,
      companyName: 'Your Company',
    };
  }

  // Export to Excel (.xlsx)
  static async exportToExcel(data: ExportData[], options: ExportOptions = {}): Promise<void> {
    try {
      const opts = { ...this.getDefaultOptions(), ...options };

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Data');

      // Add headers
      if (data.length > 0) {
        const headers = Object.keys(data[0]);
        worksheet.addRow(headers);

        // Style headers
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' }
        };

        // Add data rows
        data.forEach(row => {
          const values = headers.map(header => row[header] || '');
          worksheet.addRow(values);
        });

        // Auto-size columns
        headers.forEach((header, index) => {
          const maxLength = Math.max(
            header.length,
            ...data.map(row => String(row[header] || '').length)
          );
          worksheet.getColumn(index + 1).width = Math.min(maxLength + 2, 50);
        });
      }

      // Add metadata sheet if requested
      if (opts.includeTimestamp) {
        const metaWorksheet = workbook.addWorksheet('Export Info');
        metaWorksheet.addRow(['Property', 'Value']);
        metaWorksheet.addRow(['Export Date', new Date().toLocaleString()]);
        metaWorksheet.addRow(['Total Records', data.length]);
        metaWorksheet.addRow(['Generated By', opts.companyName]);

        // Style metadata headers
        const metaHeaderRow = metaWorksheet.getRow(1);
        metaHeaderRow.font = { bold: true };
        metaWorksheet.getColumn(1).width = 20;
        metaWorksheet.getColumn(2).width = 30;
      }

      // Write file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = this.formatFilename(opts.filename!, 'xlsx');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return Promise.resolve();
    } catch (error) {
      console.error('Excel export error:', error);
      throw new Error('Failed to export to Excel');
    }
  }

  // Export to PDF
  static async exportToPDF(data: ExportData[], options: ExportOptions = {}): Promise<void> {
    try {
      const opts = { ...this.getDefaultOptions(), ...options };
      const jsPDF = (await import('jspdf')).default;
      const autoTable = (await import('jspdf-autotable')).default;
      
      const doc = new jsPDF({
        orientation: opts.orientation,
        unit: 'mm',
        format: 'a4',
      });

      // Add header
      doc.setFontSize(20);
      doc.setFont(undefined, 'bold');
      doc.text(opts.title!, 20, 25);

      if (opts.subtitle) {
        doc.setFontSize(12);
        doc.setFont(undefined, 'normal');
        doc.text(opts.subtitle, 20, 35);
      }

      // Add company info and timestamp
      const pageWidth = doc.internal.pageSize.width;
      doc.setFontSize(10);
      doc.setFont(undefined, 'normal');
      
      if (opts.includeTimestamp) {
        doc.text(`Generated: ${new Date().toLocaleString()}`, pageWidth - 70, 15);
        doc.text(`Total Records: ${data.length}`, pageWidth - 70, 25);
      }
      
      if (opts.companyName) {
        doc.text(opts.companyName, 20, 15);
      }

      // Prepare table data
      if (data.length > 0) {
        const headers = opts.headers || Object.keys(data[0]);
        const rows = data.map(item => 
          headers.map(header => {
            const value = item[header];
            return value !== null && value !== undefined ? String(value) : '';
          })
        );

        // Add table
        autoTable(doc, {
          head: [headers],
          body: rows,
          startY: opts.subtitle ? 45 : 35,
          styles: { 
            fontSize: 8,
            cellPadding: 3,
          },
          headStyles: { 
            fillColor: [66, 139, 202],
            textColor: 255,
            fontSize: 9,
            fontStyle: 'bold',
          },
          alternateRowStyles: {
            fillColor: [245, 245, 245],
          },
          margin: { top: 20, left: 20, right: 20 },
        });
      }

      // Add footer
      const pageCount = doc.internal.pages.length - 1;
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(
          `Page ${i} of ${pageCount}`,
          pageWidth / 2,
          doc.internal.pageSize.height - 10,
          { align: 'center' }
        );
      }

      doc.save(this.formatFilename(opts.filename!, 'pdf'));
      return Promise.resolve();
    } catch (error) {
      console.error('PDF export error:', error);
      throw new Error('Failed to export to PDF');
    }
  }

  // Export to CSV
  static async exportToCSV(data: ExportData[], options: ExportOptions = {}): Promise<void> {
    try {
      const opts = { ...this.getDefaultOptions(), ...options };
      
      if (data.length === 0) {
        throw new Error('No data to export');
      }

      const headers = opts.headers || Object.keys(data[0]);
      const csvContent = [
        // Add title and metadata as comments
        `# ${opts.title}`,
        opts.subtitle ? `# ${opts.subtitle}` : null,
        opts.includeTimestamp ? `# Generated: ${new Date().toLocaleString()}` : null,
        `# Total Records: ${data.length}`,
        '', // Empty line
        // Headers
        headers.join(','),
        // Data rows
        ...data.map(row => 
          headers.map(header => {
            const value = row[header];
            const stringValue = value !== null && value !== undefined ? String(value) : '';
            // Escape commas and quotes
            return stringValue.includes(',') || stringValue.includes('"') 
              ? `"${stringValue.replace(/"/g, '""')}"` 
              : stringValue;
          }).join(',')
        )
      ].filter(line => line !== null).join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', this.formatFilename(opts.filename!, 'csv'));
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
      
      return Promise.resolve();
    } catch (error) {
      console.error('CSV export error:', error);
      throw new Error('Failed to export to CSV');
    }
  }

  // Export to Word Document (HTML format for compatibility)
  static async exportToWord(data: ExportData[], options: ExportOptions = {}): Promise<void> {
    try {
      const opts = { ...this.getDefaultOptions(), ...options };
      
      if (data.length === 0) {
        throw new Error('No data to export');
      }

      const headers = opts.headers || Object.keys(data[0]);
      
      // Create HTML content for Word
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>${opts.title}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 10px; }
            h2 { color: #666; }
            .metadata { background: #f5f5f5; padding: 10px; margin: 20px 0; border-radius: 5px; }
            table { border-collapse: collapse; width: 100%; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #4CAF50; color: white; font-weight: bold; }
            tr:nth-child(even) { background-color: #f2f2f2; }
            .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
          </style>
        </head>
        <body>
          <h1>${opts.title}</h1>
          ${opts.subtitle ? `<h2>${opts.subtitle}</h2>` : ''}
          
          ${opts.includeTimestamp ? `
            <div class="metadata">
              <strong>Export Information:</strong><br>
              Generated: ${new Date().toLocaleString()}<br>
              Total Records: ${data.length}<br>
              Company: ${opts.companyName}
            </div>
          ` : ''}
          
          <table>
            <thead>
              <tr>
                ${headers.map(header => `<th>${header}</th>`).join('')}
              </tr>
            </thead>
            <tbody>
              ${data.map(row => `
                <tr>
                  ${headers.map(header => {
                    const value = row[header];
                    return `<td>${value !== null && value !== undefined ? String(value) : ''}</td>`;
                  }).join('')}
                </tr>
              `).join('')}
            </tbody>
          </table>
          
          <div class="footer">
            <p>Document generated by ${opts.companyName} - ${new Date().toLocaleDateString()}</p>
          </div>
        </body>
        </html>
      `;

      // Create and download file
      const blob = new Blob([htmlContent], { 
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
      });
      const link = document.createElement('a');
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', this.formatFilename(opts.filename!, 'doc'));
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
      
      return Promise.resolve();
    } catch (error) {
      console.error('Word export error:', error);
      throw new Error('Failed to export to Word document');
    }
  }

  // Export to multiple formats at once
  static async exportToAll(data: ExportData[], options: ExportOptions = {}): Promise<void> {
    try {
      await Promise.all([
        this.exportToExcel(data, { ...options, filename: `${options.filename}-excel` }),
        this.exportToPDF(data, { ...options, filename: `${options.filename}-pdf` }),
        this.exportToCSV(data, { ...options, filename: `${options.filename}-csv` }),
        this.exportToWord(data, { ...options, filename: `${options.filename}-word` }),
      ]);
    } catch (error) {
      console.error('Multi-format export error:', error);
      throw new Error('Failed to export to multiple formats');
    }
  }

  // Utility method to format data for export
  static formatDataForExport(
    data: any[], 
    fieldMapping?: { [key: string]: string },
    dateFields?: string[],
    numberFields?: string[]
  ): ExportData[] {
    return data.map(item => {
      const formatted: ExportData = {};
      
      Object.keys(item).forEach(key => {
        const displayName = fieldMapping?.[key] || key;
        let value = item[key];
        
        // Format dates
        if (dateFields?.includes(key) && value) {
          value = new Date(value).toLocaleDateString();
        }
        
        // Format numbers
        if (numberFields?.includes(key) && typeof value === 'number') {
          value = value.toLocaleString();
        }
        
        formatted[displayName] = value;
      });
      
      return formatted;
    });
  }
} 