import { useAuth } from "@/components/auth/AuthProvider";
import { useDashboardData } from "@/hooks/useDashboardData";
import { DashboardSkeleton } from "@/components/dashboard/DashboardSkeleton";
import { ErrorState } from "@/components/dashboard/ErrorState";
import { EnhancedDashboardStats } from "@/components/dashboard/EnhancedDashboardStats";
import { EnhancedChart } from "@/components/charts/EnhancedChart";
import {
  Users, Building, CheckSquare, TrendingUp, Activity,
  Clock, FileText, AlertTriangle, Zap, Star, Target,
  BarChart3, DollarSign
} from "lucide-react";
import { CompactTimeCard } from "@/components/time-tracking/CompactTimeCard";
import { NotificationCenter } from "@/components/notifications/NotificationCenter";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, LineChart, Line, PieChart, Pie, Cell } from "recharts";

export const UnifiedDashboard = () => {
  const { userProfile } = useAuth();
  const { data: dashboardData, isLoading, error, refetch } = useDashboardData();

  if (isLoading) {
    return <DashboardSkeleton />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={refetch} title="Failed to load dashboard" />;
  }

  if (!dashboardData) {
    return <ErrorState error={new Error("No data available")} onRetry={refetch} />;
  }

  const { stats, chartData, pieChartData, recentActivity, performanceMetrics, financialSummary } = dashboardData;
  const role = userProfile?.role || 'staff';

  // Role-specific stats configuration
  const getStatsForRole = () => {
    const baseStats = [
      {
        title: role === 'staff' ? "Tasks Completed" : "Total Projects",
        value: role === 'staff' ? stats.completedTasks.toString() : stats.activeProjects.toString(),
        icon: <CheckSquare className="h-8 w-8" />,
        trend: { value: 18, label: "from last period" },
        color: 'green' as const
      },
      {
        title: role === 'staff' ? "Hours Logged" : "Team Members",
        value: role === 'staff' ? performanceMetrics.hoursWorked.toString() : stats.onlineUsers.toString(),
        icon: role === 'staff' ? <Clock className="h-8 w-8" /> : <Users className="h-8 w-8" />,
        trend: { value: 5, label: "from last week" },
        color: 'blue' as const
      },
      {
        title: role === 'admin' ? "System Health" : "Efficiency",
        value: role === 'admin' ? `${stats.systemHealth}%` : `${performanceMetrics.efficiency}%`,
        icon: <Zap className="h-8 w-8" />,
        trend: { value: 7, label: "improvement" },
        color: 'blue' as const
      },
      {
        title: "Pending Items",
        value: stats.pendingApprovals.toString(),
        icon: <AlertTriangle className="h-8 w-8" />,
        trend: { value: -15, label: "from yesterday" },
        color: 'red' as const
      }
    ];

    // Add financial stats for admin and accountant roles
    if (role === 'admin' || role === 'accountant') {
      baseStats.push({
        title: "Total Revenue",
        value: `₦${stats.totalRevenue.toLocaleString()}`,
        icon: <DollarSign className="h-8 w-8" />,
        trend: { value: 12, label: "from last month" },
        color: 'green' as const
      });
    }

    return baseStats;
  };

  const roleStats = getStatsForRole();

  // Generate role-appropriate chart data
  const generateTimeSeriesData = () => {
    const baseData = [
      { name: 'Mon', value1: Math.floor(Math.random() * 50) + 20, value2: Math.floor(Math.random() * 100) + 50 },
      { name: 'Tue', value1: Math.floor(Math.random() * 50) + 25, value2: Math.floor(Math.random() * 100) + 55 },
      { name: 'Wed', value1: Math.floor(Math.random() * 50) + 30, value2: Math.floor(Math.random() * 100) + 60 },
      { name: 'Thu', value1: Math.floor(Math.random() * 50) + 28, value2: Math.floor(Math.random() * 100) + 58 },
      { name: 'Fri', value1: Math.floor(Math.random() * 50) + 35, value2: Math.floor(Math.random() * 100) + 65 },
    ];

    if (role === 'staff') {
      return baseData.map(item => ({
        ...item,
        tasks: item.value1,
        hours: item.value2 / 10,
        productivity: item.value2
      }));
    } else if (role === 'manager') {
      return baseData.map(item => ({
        ...item,
        team: item.value1,
        projects: Math.floor(item.value1 * 0.6),
        efficiency: item.value2
      }));
    } else {
      return baseData.map(item => ({
        ...item,
        users: item.value1,
        projects: Math.floor(item.value1 * 0.8),
        revenue: item.value2 * 1000
      }));
    }
  };

  const timeSeriesData = generateTimeSeriesData();

  return (
    <div className="space-y-8 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-primary/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-48 h-48 bg-gradient-to-tr from-secondary/5 to-transparent rounded-full blur-2xl"></div>
      </div>

      <div className="relative z-10">
        {/* Welcome Header */}
        <div className="glassmorphism rounded-2xl p-8 mb-8" data-aos="fade-down">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 rounded-2xl bg-gradient-to-br from-primary to-primary/80 shadow-xl">
              <Star className="h-8 w-8 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-4xl font-bold modern-heading mb-2">
                {role === 'admin' ? 'Admin Command Center' :
                 role === 'manager' ? 'Manager Dashboard' :
                 role === 'accountant' ? 'Financial Dashboard' :
                 role === 'staff-admin' ? 'Staff Admin Panel' :
                 'Staff Performance Hub'}
              </h1>
              <p className="text-muted-foreground text-lg">
                Welcome back, {userProfile?.full_name || 'User'}! Here's your overview.
              </p>
            </div>

            {/* Notification Center */}
            <div className="flex items-center">
              <NotificationCenter />
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <EnhancedDashboardStats stats={roleStats} />

        {/* Time Card for staff-admin and accountant */}
        {(role === 'staff-admin' || role === 'accountant') && (
          <div className="mt-8">
            <CompactTimeCard userRole={role} showControls={true} />
          </div>
        )}

        {/* Charts Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 mt-8">
          <EnhancedChart
            title={role === 'staff' ? "Weekly Performance" : role === 'manager' ? "Team Overview" : "System Metrics"}
            icon={<Activity className="h-5 w-5" />}
            timeFilter
            exportable
            refreshable
          >
            <BarChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              {role === 'staff' ? (
                <>
                  <Bar dataKey="tasks" fill="hsl(var(--primary))" name="Tasks" />
                  <Bar dataKey="hours" fill="hsl(var(--secondary))" name="Hours" />
                </>
              ) : role === 'manager' ? (
                <>
                  <Bar dataKey="team" fill="hsl(var(--primary))" name="Team" />
                  <Bar dataKey="projects" fill="hsl(var(--secondary))" name="Projects" />
                </>
              ) : (
                <>
                  <Bar dataKey="users" fill="hsl(var(--primary))" name="Users" />
                  <Bar dataKey="projects" fill="hsl(var(--secondary))" name="Projects" />
                </>
              )}
            </BarChart>
          </EnhancedChart>

          <EnhancedChart
            title="Status Distribution"
            icon={<Target className="h-5 w-5" />}
            exportable
          >
            <PieChart>
              <Pie
                data={pieChartData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                fill="hsl(var(--primary))"
                dataKey="value"
              >
                {pieChartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={`hsl(var(--chart-${(index % 5) + 1}))`} 
                  />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </EnhancedChart>
        </div>

        {/* Trends Chart */}
        <div className="grid grid-cols-1 gap-8 mt-8">
          <EnhancedChart
            title="Trends & Analytics"
            icon={<TrendingUp className="h-5 w-5" />}
            timeFilter
            exportable
            refreshable
            fullscreen
          >
            <LineChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              {role === 'staff' ? (
                <>
                  <Line type="monotone" dataKey="tasks" stroke="hsl(var(--primary))" strokeWidth={3} name="Tasks" />
                  <Line type="monotone" dataKey="productivity" stroke="hsl(var(--secondary))" strokeWidth={3} name="Productivity" />
                </>
              ) : role === 'manager' ? (
                <>
                  <Line type="monotone" dataKey="team" stroke="hsl(var(--primary))" strokeWidth={3} name="Team Size" />
                  <Line type="monotone" dataKey="efficiency" stroke="hsl(var(--secondary))" strokeWidth={3} name="Efficiency" />
                </>
              ) : (
                <>
                  <Line type="monotone" dataKey="users" stroke="hsl(var(--primary))" strokeWidth={3} name="Users" />
                  <Line type="monotone" dataKey="projects" stroke="hsl(var(--secondary))" strokeWidth={3} name="Projects" />
                </>
              )}
            </LineChart>
          </EnhancedChart>
        </div>

        {/* Recent Activity */}
        {recentActivity.length > 0 && (
          <div className="glassmorphism rounded-2xl p-6 mt-8">
            <div className="flex items-center gap-2 mb-6">
              <Activity className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Recent Activity</h3>
            </div>
            <div className="space-y-4">
              {recentActivity.slice(0, 5).map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div className="flex items-center gap-3">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.type === 'error' ? 'bg-destructive' :
                      activity.type === 'warning' ? 'bg-yellow-500' :
                      activity.type === 'success' ? 'bg-green-500' : 'bg-primary'
                    }`} />
                    <div>
                      <p className="font-medium">{activity.action}</p>
                      <p className="text-sm text-muted-foreground">
                        by {activity.user} • {activity.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    {new Date(activity.timestamp).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};