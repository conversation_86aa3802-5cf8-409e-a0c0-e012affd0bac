# CTNL AI Workboard

Advanced AI-powered workforce management system with voice navigation and intelligent project planning.

## Features

- [x] AI-powered workforce management
- [x] Voice navigation and commands
- [x] Intelligent project planning
- [x] Real-time collaboration
- [x] Advanced analytics and reporting
- [x] Supabase integration for backend services

## Getting Started

### Prerequisites

- Node.js >= 20.16.0
- npm >= 10.0.0

### Installation

**Important: This project uses npm only. Do not use yarn or pnpm.**

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

### Package Management

This project is configured to use **npm only**. Other package managers (yarn, pnpm) have been removed to prevent conflicts.

Available scripts:

```bash
npm run dev              # Start development server
npm run build            # Build for production
npm run clear-cache      # Clear all caches
npm run fresh-install    # Clear cache and reinstall
npm run fix-blank-screen # Fix blank screen issues
```

## Troubleshooting

### Black Screen Issues

If you encounter a black screen, try:

1. Clear cache: `npm run clear-cache`
2. Fresh install: `npm run fresh-install`
3. Use the fix script: `npm run fix-blank-screen`

### Stack Overflow Errors

The Navigator circular reference issue has been fixed in the latest version. If you still encounter issues:

1. Clear browser cache completely
2. Restart the development server
3. Check browser console for specific errors

## Development

This project uses:

- **React 18** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **Supabase** for backend services
- **Tanstack Query** for data fetching

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request
